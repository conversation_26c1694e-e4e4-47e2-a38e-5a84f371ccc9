<script setup>
import { onMounted, ref } from "vue";
import { formatTime } from "@/utils/index";
import { findCommentsAll } from "@/api/period.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();
onMounted(() => {
  getTableList();
  // tableData.value = Array.from({ length: 20 }).fill({
  //   completed_at: "0",
  //   pending_review: 1,
  //   createdAt: 172627952334,
  //   name: "2024-09-14习题任务",
  //   termNumber: 1,
  //   courseTypeName: "手工",
  //   state: 1,
  //   organizationName: "2024-09-14习题任务",
  //   freeze: 1
  // });
});
// 搜索表单
const form = ref({
  startTime: "",
  endTime: "",
  termNumber: "",
  userName: "",
  score: "",
  startRating: "",
  endRating: "",
  pickTime: ""
});
// 表格数据
const tableData = ref([]);
const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});
const value1 = ref([]);
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort,
    // courseId: route.query.courseId,
    coursePeriodId: Number(route.query.periodId)
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  // console.log("🍧-----paramsData-----", paramsData);
  const [err, result] = await requestTo(findCommentsAll(paramsData));
  // console.log("🎁-----result-----", result);
  if (result) {
    tableData.value = result?.content;
    params.value.totalElements = result?.totalElements;
  } else {
    ElMessage.error(err);
  }
  getListLoading.value = false;
};
// 选择时间
const timeChange = value => {
  if (value) {
    form.value.startTime = new Date(value[0])?.getTime();
    // form.value.endTime = new Date(value[1])?.getTime();
    const endDate = new Date(value[1]);
    endDate.setHours(23, 59, 59, 999); // 关键修改
    form.value.endTime = endDate?.getTime();
  }
};
//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTableList();
};
const handleSizeChange = val => {
  params.value.page = 1;
  params.value.size = val;
  getTableList();
};
//搜索
const searchData = () => {
  params.value.page = 1;
  getTableList();
};
// 重置
const setData = () => {
  form.value = {};
  params.value.page = 1;
  value1.value = [];
  getTableList();
};
// 清除数据
const clearEvt = val => {
  if (val === "name") {
    form.value.userName = "";
  } else if (val === "time") {
    form.value.startTime = "";
    form.value.endTime = "";
  } else if (val === "termNumber") {
    form.value.termNumber = "";
  } else if (val === "startRating") {
    form.value.startRating = "";
  }
  // params.value.page = 1;
  getTableList();
};
// 分数最大值和最小值
const handleChangeMax = () => {
  if (form.value.endRating < 0) {
    ElMessage({
      type: "error",
      message: "最大评分不能小于0"
    });
    form.value.endRating = "";
    return;
  }
  if (form.value.startRating && form.value.endRating) {
    if (form.value.startRating > form.value.endRating) {
      ElMessage({
        type: "error",
        message: "最小评分不能大于最大评分"
      });
      form.value.startRating = "";
      form.value.endRating = "";
      return;
    } else {
      getTableList();
    }
  }
};
const handleChangeMin = () => {
  if (form.value.startRating < 0) {
    ElMessage({
      type: "error",
      message: "最小评分不能小于0"
    });
    form.value.startRating = "";
    return;
  }
};
// 详情
const detailEvt = id => {
  router.push({
    path: "/course/currentDetails/evaluateDetail",
    query: {
      courseId: route.query.courseId,
      periodId: route.query.periodId,
      id: id
    }
  });
};
</script>

<template>
  <div class="containers">
    <!-- <div class="con_search">
      <el-form :model="form" :inline="true">
        <el-form-item label="评价时间">
          <el-date-picker
            v-model="value1"
            type="daterange"
            start-placeholder="请选择开始时间"
            end-placeholder="请选择结束时间"
            @change="timeChange"
            @clear="clearEvt('time')"
          />
        </el-form-item>
        <el-form-item label="期号">
          <el-input
            v-model="form.termNumber"
            placeholder="请输入"
            clearable
            @clear="clearEvt('termNumber')"
          />
        </el-form-item>
        <el-form-item label="用户名">
          <el-input
            v-model="form.userName"
            placeholder="请输入"
            clearable
            @clear="clearEvt('name')"
          />
        </el-form-item>
        <el-form-item label="评分">
          <div class="range-input">
            <el-input
              v-model.number="form.startRating"
              placeholder="输最小值"
              type="number"
              style="width: 100px"
              :min="0"
              @change="handleChangeMin"
            />
            <span class="separator">-</span>
            <el-input
              v-model.number="form.endRating"
              placeholder="输最大值"
              type="number"
              style="width: 100px"
              :min="0"
              @change="handleChangeMax"
            />
          </div>
        </el-form-item>
        <el-form-item label=" ">
          <div class="flex">
            <el-button type="primary" @click="searchData"> 搜索 </el-button>
            <el-button @click="setData">重置</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div> -->
    <div class="con_table">
      <el-table
        :data="tableData"
        table-layout="fixed"
        :header-cell-style="{ backgroundColor: '#fafafa', color: '#565353' }"
        highlight-current-row
        height="100%"
        :style="{ flex: 1, minHeight: 0 }"
      >
        <!-- <el-table-column prop="termNumber" label="期号" align="left">
          <template #default="scope">
            <div>
              {{ scope.row.termNumber || "0" }}
            </div>
          </template>
        </el-table-column> -->
        <el-table-column prop="userName" label="用户名" align="left" fixed>
          <template #default="scope">
            <div>
              {{ scope.row.userName || "--" }}
            </div>
          </template>
        </el-table-column>

        <el-table-column
          width="200px"
          prop="createdAt"
          label="评价时间"
          align="left"
        >
          <template #default="scope">
            <div>
              {{
                formatTime(scope.row?.createdAt, "YYYY-MM-DD HH:mm:ss") || "--"
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="rating" label="分数" align="left">
          <template #default="scope">
            <div>
              {{ scope.row.rating || "0" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="content"
          label="评论内容"
          align="left"
          width="300"
        >
          <template #default="scope">
            <span class="no-wrap-text">{{ scope.row.content || "--" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          width="200px"
          prop="replies.createdAt"
          label="回复时间"
          align="left"
        >
          <template #default="scope">
            <div>
              {{
                formatTime(
                  scope.row?.replies?.createdAt,
                  "YYYY-MM-DD HH:mm:ss"
                ) || "--"
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="replies.content"
          label="机构回复"
          align="left"
          width="300"
        >
          <template #default="scope">
            <span class="no-wrap-text">{{
              scope.row.replies?.content || "--"
            }}</span>
            <!-- {{ scope.row.replies?.content || "--" }} -->
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" align="left" width="150px">
          <template #default="{ row }">
            <div class="option">
              <div class="btnse" @click="detailEvt(row.id)">详情</div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="con_pagination">
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="params.page"
        v-model:page-size="params.size"
        class="pagination"
        background
        :page-sizes="[15, 30, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="params.totalElements"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.containers {
  height: 100%;
  display: flex;
  flex-direction: column;
  //   box-sizing: border-box;
  //   width: calc(100% - 48px);
  //   height: 100%;
  //   padding: 24px;
  //   background: #fff;

  .con_search {
    display: flex;
    align-items: center;
    width: 100%;
    height: fit-content;
    margin-bottom: 12px;
  }

  .con_table {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
    // width: calc(100% - 25px);
    width: 100%;
    // margin-bottom: 24px;
    // margin-left: 25px;
    margin: 10px 0 24px 0;

    .option {
      display: flex;

      .btnse {
        display: flex;
        // margin-left: 16px;
        color: #4095e5;
        cursor: pointer;
      }
    }
  }

  .con_pagination {
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }
}
:deep(.el-form--inline .el-form-item) {
  margin-right: 32px;
}
.range-input {
  display: flex;
  align-items: center;
}

.separator {
  margin: 0 10px;
  color: #606266;
}
:deep(.el-popper.is-dark) {
  max-width: 500px !important;
  word-break: break-all !important;
}
/* 强制不换行，超出显示省略号 */
.no-wrap-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  width: 100%;
}
</style>
