<script setup>
import {
  ref,
  onMounted,
  reactive,
  computed,
  onActivated,
  watch,
  nextTick,
  onUnmounted
} from "vue";
import {
  complexId,
  leaderLecturerFind,
  courseFindId,
  coursePeriodFind
} from "@/api/course.js";
import { findAllCourseType } from "@/api/period.js";
import {
  findByCourseDraftId,
  saveBasicInformation,
  nextBasicInformation,
  findByCoursePeriodDraftId,
  draftDelete
} from "@/api/drafts.js";
import { useRouter, useRoute } from "vue-router";
import { requestTo } from "@/utils/http/tool";
import { ElIcon, ElMessage, ElMessageBox, ElLoading } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import { uploadFile } from "@/utils/upload/upload.js";
import dayjs from "dayjs";
import weekday from "dayjs/plugin/weekday";
import { to, isEmpty, isNumber } from "@iceywu/utils";
import { courseStore } from "@/store/modules/course.js";
import { whitePath } from "@/utils/common.js";
const emites = defineEmits(["baseInfo", "baseInfoPeriod"]);
const useCourseStore = courseStore();
const router = useRouter();
const route = useRoute();
const courseFormRef = ref();
const ruleFormRef = ref();
const openCourseImg = ref(false); //控制是否打开使用课程图片开关
const switchLoading = ref(false); //控制使用课程图片开关的加载状态
const isPromptVisible = ref(false); //控制提示窗口是否正在显示（防止连续点击）
const periodName = ref(""); //课期命名
const termPeriodNumber = ref(); //课期数
const dialogVisiblesc = ref(false); // 弹窗显示状态
const currentDatesed = ref(new Date());
const selectedDates = ref([]);
const tableData = ref([]); // 开课时间设置表格数据
const selectDate = ref(); // 选择添加的日期
const prefixRule = ref(""); //前缀规则
const suffixRule = ref("");
const imgText = ref(
  "支持上传png、jpg、jpeg图片格式，最多上传9张，最佳尺寸：750*750px，单张大小不超过10MB"
);
const ruleForm = reactive({
  complexId: "", //基地
  lecturers: "", //领队
  maxPeopleNumber: "", //人数上限
  leaders: "", //讲师
  coursePeriodFiles: [], //课期图片
  coursePeriodName: "", //课期名
  prefix: "无", //命名前缀
  suffix: "无", //命名后缀
  coursePeriodTags: [], //课期标签
  teachers: [] //师资
});
const courseForm = reactive({
  courseName: "", //课程名
  courseTypeId: "", //课程类型
  courseFiles: [], //课程图片
  maxAge: "", //年龄最大
  minAge: "", //年龄最小
  courseTags: [] //课程亮点标签
});
const validatePeopleNumber = (rule, value, callback) => {
  let num = Number(value);
  if (num && num < 0) {
    return callback(new Error("人数上限不能为负数"));
  }
  if (num && !Number.isInteger(num)) {
    return callback(new Error("人数上限必须为整数"));
  }

  if (num && num > 99999) {
    return callback(new Error("人数上限不能超过五位数"));
  }
  callback();
};
// 课程规则
const rules = reactive({
  courseName: [{ required: true, message: "请输入课程名", trigger: "change" }],
  courseTypeId: [
    { required: true, message: "请选择课程类型", trigger: "change" }
  ]
});
// 课期规则
const rule = reactive({
  coursePeriodName: [
    { required: true, message: "请输入课期名", trigger: "change" }
  ],
  maxPeopleNumber: [
    // {
    //   pattern: /^\d+$/,
    //   message: "必须为整数，不允许为小数和负数",
    //   trigger: "blur"
    // }
    { validator: validatePeopleNumber, trigger: "blur" }
  ],
  complexId: [{ required: true, message: "请选择基地", trigger: "change" }],
  leaders: [{ required: true, message: "请选择领队", trigger: "change" }],
  lecturers: [{ required: true, message: "请选择讲师", trigger: "change" }]
});
const formData = ref([
  {
    label: "课程名",
    type: "input",
    prop: "courseName",
    check: true,
    placeholder: "请输入课程名",
    width: "350px",
    maxLength: 30
  },
  {
    label: "课程类型",
    type: "cascader",
    prop: "courseTypeId",
    check: true,
    placeholder: "请选择课程类型",
    width: "350px",
    options: []
  },
  {
    label: "课程亮点标签",
    type: "tag",
    prop: "courseTags",
    check: false,
    placeholder: "请输入人数上限",
    width: "350px",
    tags: []
  },
  {
    label: "课程年龄段",
    type: "inputToInput",
    prop: "age",
    check: false,
    placeholder: "请输入年龄段",
    width: "350px"
  }
]);
const formPeriodData = ref([
  {
    label: "课期名",
    type: "input",
    typeInput: "text",
    prop: "coursePeriodName",
    check: true,
    placeholder: "请输入课期名",
    width: "65%",
    maxLength: 30
  },
  {
    label: "基地",
    type: "select",
    prop: "complexId",
    check: true,
    placeholder: "请选择基地",
    width: "50%",
    options: [],
    text: "新建基地"
  },
  {
    label: "人数上限",
    type: "input",
    typeInput: "number",
    prop: "maxPeopleNumber",
    check: false,
    placeholder: "请输入人数上限",
    width: "50%",
    maxLength: 5
  },
  // {
  //   label: "师资",
  //   type: "tagTab",
  //   prop: "teachers",
  //   check: false,
  //   placeholder: "请输入师资",
  //   width: "350px",
  //   tags: [],
  //   text: "添加师资"
  // },
  {
    label: "领队",
    type: "selectMultiple",
    prop: "leaders",
    check: true,
    placeholder: "请选择领队",
    width: "50%",
    options: [],
    text: "新建领队"
  },
  {
    label: "讲师",
    type: "selectMultiple",
    prop: "lecturers",
    check: true,
    placeholder: "请选择讲师",
    width: "50%",
    options: [],
    text: "新建讲师"
  },
  {
    label: "课期标签",
    type: "tag",
    prop: "coursePeriodTags",
    check: false,
    placeholder: "请输入课期标签",
    width: "350px",
    tags: []
  }
]);
// 课期前缀后缀选项
const periodPrefixOptions = ref([
  { value: 1, label: "无" },
  { value: 2, label: "课期数" },
  { value: 3, label: "开课日期" }
]);
const periodSuffixOptions = ref([
  { value: 1, label: "无" },
  { value: 2, label: "课期数" },
  { value: 3, label: "开课日期" }
]);
const dialogImageUrl = ref("");
const dialogVisible = ref(false);
const submitLoading = ref(false);
const inputValue = ref("");
const inputVisible = ref(false);
const InputRef = ref();
const handleClose = tag => {
  formData.value[2].tags.splice(formData.value[2].tags.indexOf(tag), 1);
};

const showInput = () => {
  inputVisible.value = true;
  nextTick(() => {
    InputRef.value?.input?.focus();
  });
};

const handleInputConfirm = () => {
  if (inputValue.value) {
    if (inputValue.value.length > 12) {
      ElMessage.error("输入的字数不能超过12个");
    }
    const item = formData.value[2].tags?.find(it => it === inputValue.value);
    if (item) {
      ElMessage.error("标签名称不可重复，请重新输入");
    }
    if (!item && inputValue.value.length <= 12) {
      formData.value[2].tags.push(inputValue.value);
    }
  }
  inputVisible.value = false;
  inputValue.value = "";
};
const inputPeriodValue = ref("");
const inputPeriodVisible = ref(false);
const periodInputRef = ref();
const handlePeriodClose = tag => {
  formPeriodData.value[5].tags.splice(
    formPeriodData.value[5].tags.indexOf(tag),
    1
  );
  periodPrefixOptions.value.splice(ruleForm.coursePeriodTags.indexOf(tag), 1);
  periodSuffixOptions.value.splice(ruleForm.coursePeriodTags.indexOf(tag), 1);
};

const showPeriodInput = () => {
  inputPeriodVisible.value = true;
  nextTick(() => {
    periodInputRef.value?.input?.focus();
  });
};

const handlePeriodInputConfirm = () => {
  if (inputPeriodValue.value) {
    if (inputPeriodValue.value.length > 12) {
      ElMessage.error("输入的字数不能超过12个");
    }
    const item = formPeriodData.value[5].tags?.find(
      it => it === inputPeriodValue.value
    );
    if (item) {
      ElMessage.error("标签名称不可重复，请重新输入");
    }
    if (!item && inputPeriodValue.value.length <= 12) {
      formPeriodData.value[5].tags.push(inputPeriodValue.value);
      periodPrefixOptions.value.push({
        value: periodPrefixOptions.value.length + 1,
        label: inputPeriodValue.value
      });
      periodSuffixOptions.value.push({
        value: periodSuffixOptions.value.length + 1,
        label: inputPeriodValue.value
      });
    }
  }
  inputPeriodVisible.value = false;
  inputPeriodValue.value = "";
};

// 课期命名示意
watch(
  () => [
    ruleForm.prefix,
    ruleForm.suffix,
    tableData.value,
    ruleForm.coursePeriodName
  ],
  ([newPrefix, newSuffix, newData, newName]) => {
    let prefixLabel =
      periodPrefixOptions.value.find(i => i.label === newPrefix)?.label || "";
    let suffixLabel =
      periodSuffixOptions.value.find(i => i.label === newSuffix)?.label || "";
    let prefixText = "";
    let suffixText = "";
    if (prefixLabel === "课期数") {
      if (termPeriodNumber.value) {
        prefixText = `第${termPeriodNumber.value + 1}期`;
      } else {
        prefixText = `第1期`;
      }
    } else if (prefixLabel === "无") {
      prefixText = ``;
    } else if (prefixLabel === "开课日期") {
      if (newData?.length > 0) {
        let openDate = dayjs(newData[0].openTime).format("MMDD");
        prefixText = `${openDate}`;
      } else {
        prefixText = ``;
      }
    } else {
      prefixText = `${prefixLabel}`;
    }
    if (suffixLabel === "课期数") {
      if (termPeriodNumber.value) {
        suffixText = `第${termPeriodNumber.value + 1}期`;
      } else {
        suffixText = `第1期`;
      }
    } else if (suffixLabel === "无") {
      suffixText = ``;
    } else if (suffixLabel === "开课日期") {
      if (newData?.length > 0) {
        let openDate = dayjs(newData[0].openTime).format("MMDD");
        suffixText = `${openDate}`;
      } else {
        suffixText = ``;
      }
    } else {
      suffixText = `${suffixLabel}`;
    }
    // 拼接新的课期名
    if (prefixText && ruleForm.coursePeriodName && suffixText) {
      periodName.value = `${prefixText}-${ruleForm.coursePeriodName}-${suffixText}`;
    } else if (prefixText && ruleForm.coursePeriodName) {
      periodName.value = `${prefixText}-${ruleForm.coursePeriodName}`;
    } else if (ruleForm.coursePeriodName && suffixText) {
      periodName.value = `${ruleForm.coursePeriodName}-${suffixText}`;
    } else if (ruleForm.coursePeriodName) {
      periodName.value = `${ruleForm.coursePeriodName}`;
    } else {
      periodName.value = "";
    }
  },
  { immediate: true, deep: true }
);
const handleChangeMax = () => {
  if (Number(courseForm.maxAge) < 0) {
    courseForm.maxAge = 2;
    ElMessage.error("最大年龄不能小于0");
  }
  if (Number(courseForm.maxAge) > 150) {
    courseForm.maxAge = 150;
    ElMessage.error("最大年龄不能大于150");
  }
  if (
    !isEmpty(courseForm.minAge) &&
    !isEmpty(courseForm.maxAge) &&
    Number(courseForm.minAge) >= Number(courseForm.maxAge)
  ) {
    courseForm.minAge = 1;
    ElMessage.error("最大年龄不能小于等于最小年龄");
    if (!isEmpty(courseForm.maxAge) && Number(courseForm.maxAge) <= 0) {
      courseForm.maxAge = 2;
    }
  }
  if (
    typeof courseForm.maxAge === "number" &&
    !isNaN(courseForm.maxAge) &&
    !Number.isInteger(courseForm.maxAge)
  ) {
    courseForm.maxAge = Math.floor(courseForm.maxAge);
    ElMessage.error("最大年龄请输入整数");
  }
};

const handleChangeMin = () => {
  if (
    !isEmpty(courseForm.minAge) &&
    !isEmpty(courseForm.maxAge) &&
    Number(courseForm.minAge) >= Number(courseForm.maxAge)
  ) {
    courseForm.minAge = 1;
    ElMessage.error("最小年龄不能大于等于最大年龄");
  }
  if (Number(courseForm.minAge) < 0) {
    courseForm.minAge = 1;
    ElMessage.error("最小年龄不能小于0");
  }
  if (Number(courseForm.minAge) > 150) {
    courseForm.minAge = 149;
    ElMessage.error("最小年龄不能大于150");
  }

  if (
    typeof courseForm.minAge === "number" &&
    !isNaN(courseForm.minAge) &&
    !Number.isInteger(courseForm.minAge)
  ) {
    courseForm.minAge = Math.floor(courseForm.minAge);
    ElMessage.error("最小年龄请输入整数");
  }
};
const filterInputMin = value => {
  // 只允许输入数字
  let filteredValue = value.replace(/\D/g, "");
  // 限制最多三位数
  if (filteredValue.length > 3) {
    filteredValue = filteredValue.substring(0, 3);
  }
  // 处理前导零的情况
  if (filteredValue.length > 1 && filteredValue.startsWith("0")) {
    filteredValue = filteredValue.substring(1);
  }
  if (filteredValue.length > 2 && filteredValue.startsWith("0")) {
    filteredValue = filteredValue.substring(1);
  }
  courseForm.minAge = filteredValue;
};
const filterInputMax = value => {
  let filteredValue = value.replace(/\D/g, "");
  if (filteredValue.length > 3) {
    filteredValue = filteredValue.substring(0, 3);
  }
  if (filteredValue.length > 1 && filteredValue.startsWith("0")) {
    filteredValue = filteredValue.substring(1);
  }
  if (filteredValue.length > 2 && filteredValue.startsWith("0")) {
    filteredValue = filteredValue.substring(1);
  }
  courseForm.maxAge = filteredValue;
};
const handlePictureCardPreview = uploadFile => {
  dialogImageUrl.value = uploadFile.url;
  dialogVisible.value = true;
};
const fileList = ref([]); // 课程封面上传显示用
const fileListCoursePeriod = ref([]); // 课期封面上传显示用
const courseCopyCover = ref([]); // 存储课程封面的深拷贝
const ownCoursePeriodCover = ref([]); // 课期自己上传的封面

// 跟踪同步状态
const isSyncActive = ref(true); // 是否是完全同步状态
const beforeUpload = async file => {
  // 增强重复图片检查：检查文件名是否重复
  let sameImg = fileList.value?.find(
    it => it.name === file.name || it.fileName === file.name
  );
  if (sameImg) {
    ElMessage.error("图片不可重复上传，请重新上传");
    return false;
  }
  // 检查文件大小，超过10MB拦截上传
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    ElMessage.error("图片大小超过10MB限制，请压缩后重新上传");
    return false;
  }
  let imgType = ["jpg", "jpeg", "png"];
  let fileType = file.type.split("/")[1];
  if (!imgType.includes(fileType)) {
    ElMessage.error("上传图片格式不支持，请上传png，jpg， jpeg格式的图片");
    return false;
  }
  if (courseForm.courseFiles?.length > 8) {
    ElMessage.error("上传图片数量过多，最多上传9张图片");
    return false;
  }
  try {
    if (file.type.startsWith("image/") === false) {
      ElMessage.error("上传类型不支持");
      fileList.value = fileList.value.filter(() => false);
      return false;
    }

    // 先添加一个临时项来显示上传进度
    const tempFile = {
      name: file.name,
      uid: file.uid,
      status: "uploading",
      percentage: 0
    };
    fileList.value.push(tempFile);

    const { data, code } = await uploadFile(file, progress => {
      // 更新上传进度
      if (progress.percent) {
        // 找到当前上传项
        const fileIndex = fileList.value.findIndex(
          item => item.uid === file.uid
        );
        if (fileIndex !== -1) {
          // 更新进度
          fileList.value[fileIndex].percentage = progress.percent;
          fileList.value[fileIndex].status = progress.status || "uploading";
        }
      }

      // 上传成功，补充 url 字段
      if (progress.status === "success" && progress.data?.url) {
        if (progress.data.fileIdentifier) {
          // 检查文件标识符是否重复 (仅检查课程内部)
          const fileIdentifierExists = courseForm.courseFiles.some(
            item => item.fileIdentifier === progress.data.fileIdentifier
          );

          if (fileIdentifierExists) {
            ElMessage.error("课程中已有相同图片，不可重复上传");

            // 从上传列表中移除
            fileList.value = fileList.value.filter(f => f.uid !== file.uid);
            return; // 直接返回，不继续处理
          }

          // 创建新文件对象
          const newFile = {
            fileIdentifier: progress.data.fileIdentifier,
            fileType: "PHOTO",
            uid: file.uid,
            url: progress.data.url,
            fileName: progress.data.fileName
          };

          // 更新显示项的URL
          const fileIndex = fileList.value.findIndex(
            item => item.uid === file.uid
          );
          if (fileIndex !== -1) {
            fileList.value[fileIndex].url = progress.data.url;
            fileList.value[fileIndex].status = "success";
          }

          // 添加到课程图片
          courseForm.courseFiles.push(newFile);

          // 如果同步功能开启，则同步到课期封面
          if (openCourseImg.value) {
            // 添加到课程图片的深拷贝
            const newFileCopy = JSON.parse(JSON.stringify(newFile));
            courseCopyCover.value.push(newFileCopy);

            // 更新课期文件数组
            ruleForm.coursePeriodFiles = [...courseCopyCover.value];

            // 更新课期图片显示列表
            fileListCoursePeriod.value = [...courseCopyCover.value];
          }
        }
      }

      // 处理上传失败的情况
      if (progress.status === "fail") {
        ElMessage.error(progress.errMessage || "上传失败，请重试");
        // 从上传列表中移除
        fileList.value = fileList.value.filter(f => f.uid !== file.uid);
      }
    });

    return true;
  } catch (error) {
    console.error("上传出错:", error);
    // 出错时也从列表中移除
    fileList.value = fileList.value.filter(f => f.uid !== file.uid);
    return false;
  }
};

const handleRemove = (uploadFile, uploadFiles) => {
  // 从课程封面移除
  courseForm.courseFiles = courseForm.courseFiles.filter(
    it =>
      it.uid !== uploadFile.uid &&
      it.fileIdentifier !== uploadFile.fileIdentifier
  );

  // 检查是否删除后课程封面为空
  const isCourseCoverEmpty = courseForm.courseFiles.length === 0;

  // 如果同步功能开启，则同步删除课期封面中对应图片
  if (openCourseImg.value) {
    // 从课程图片的深拷贝中删除
    courseCopyCover.value = courseCopyCover.value.filter(
      it =>
        it.uid !== uploadFile.uid &&
        it.fileIdentifier !== uploadFile.fileIdentifier
    );

    // 更新课期文件
    ruleForm.coursePeriodFiles = [...courseCopyCover.value];

    // 更新课期图片显示列表
    fileListCoursePeriod.value = [...courseCopyCover.value];

    // 如果删除后课程封面为空，自动关闭同步功能
    if (isCourseCoverEmpty) {
      openCourseImg.value = false;

      // 清空课期图片
      courseCopyCover.value = [];
      ruleForm.coursePeriodFiles = [];
      fileListCoursePeriod.value = [];
    }
  }
};

// 课期图片处理函数已在其他地方实现

// 课期图片相关
const dialogCorsePeriodUrl = ref("");
const dialogCoursePeriodVisible = ref(false);
const handleCoursePeriodPreview = uploadFile => {
  dialogCorsePeriodUrl.value = uploadFile.url;
  dialogCoursePeriodVisible.value = true;
};
const beforeUploadCoursePeriod = async file => {
  // 如果启用了课程封面同步，不允许上传
  if (openCourseImg.value) {
    ElMessage.error("已启用课程封面同步，请关闭后再上传");
    return false;
  }

  // 检查文件大小，超过10MB拦截上传
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    ElMessage.error("图片大小超过10MB限制，请压缩后重新上传");
    return false;
  }

  let imgType = ["jpg", "jpeg", "png"];
  let fileType = file.type.split("/")[1];
  if (!imgType.includes(fileType)) {
    ElMessage.error("上传图片格式不支持，请上传png，jpg， jpeg格式的图片");
    return false;
  }
  if (ruleForm.coursePeriodFiles?.length > 8) {
    ElMessage.error("上传图片数量过多，最多上传9张图片");
    return false;
  }
  try {
    if (file.type.startsWith("image/") === false) {
      ElMessage.error("上传类型不支持");
      fileListCoursePeriod.value = fileListCoursePeriod.value.filter(
        () => false
      );
      return false;
    }

    // 先添加一个临时项来显示上传进度
    const tempFile = {
      name: file.name,
      uid: file.uid,
      status: "uploading",
      percentage: 0
    };
    fileListCoursePeriod.value.push(tempFile);

    const { data, code } = await uploadFile(file, progress => {
      // 更新上传进度
      if (progress.percent) {
        // 找到当前上传项
        const fileIndex = fileListCoursePeriod.value.findIndex(
          item => item.uid === file.uid
        );
        if (fileIndex !== -1) {
          // 更新进度
          fileListCoursePeriod.value[fileIndex].percentage = progress.percent;
          fileListCoursePeriod.value[fileIndex].status =
            progress.status || "uploading";
        }
      }

      // 上传成功，补充 url 字段
      if (progress.status === "success" && progress.data?.url) {
        // 创建新文件对象
        const newFile = {
          fileIdentifier: progress.data.fileIdentifier,
          fileType: "PHOTO",
          uid: file.uid,
          url: progress.data.url,
          fileName: progress.data.fileName
        };

        // 检查文件标识符是否重复（仅在课期内部检查）
        const fileIdentifierExists = ruleForm.coursePeriodFiles.some(
          item => item.fileIdentifier === newFile.fileIdentifier
        );

        if (fileIdentifierExists) {
          ElMessage.error("课期中已有相同图片，不可重复上传");
          // 从上传列表中移除
          fileListCoursePeriod.value = fileListCoursePeriod.value.filter(
            f => f.uid !== file.uid
          );
          return; // 直接返回，不继续处理
        }

        // 更新显示项的URL
        const fileIndex = fileListCoursePeriod.value.findIndex(
          item => item.uid === file.uid
        );
        if (fileIndex !== -1) {
          fileListCoursePeriod.value[fileIndex].url = progress.data.url;
          fileListCoursePeriod.value[fileIndex].status = "success";
        }

        // 添加到课期自己上传的图片中
        ownCoursePeriodCover.value.push(newFile);

        // 更新课期文件
        ruleForm.coursePeriodFiles = [...ownCoursePeriodCover.value];
      }

      // 处理上传失败的情况
      if (progress.status === "fail") {
        ElMessage.error(progress.errMessage || "上传失败，请重试");
        // 从上传列表中移除
        fileListCoursePeriod.value = fileListCoursePeriod.value.filter(
          f => f.uid !== file.uid
        );
      }
    });

    return true;
  } catch (error) {
    console.error("上传出错:", error);
    // 出错时也从列表中移除
    fileListCoursePeriod.value = fileListCoursePeriod.value.filter(
      f => f.uid !== file.uid
    );
    return false;
  }
};

const handleCoursePeriodRemove = (uploadFile, uploadFiles) => {
  // 如果启用了课程封面同步，不允许删除
  if (openCourseImg.value) {
    ElMessage.error("已启用课程封面同步，无法删除图片");
    return;
  }

  // 从课期自己上传的图片中删除
  ownCoursePeriodCover.value = ownCoursePeriodCover.value.filter(
    item =>
      item.uid !== uploadFile.uid &&
      item.fileIdentifier !== uploadFile.fileIdentifier
  );

  // 更新课期文件
  ruleForm.coursePeriodFiles = [...ownCoursePeriodCover.value];
};

// 不再需要检查课程封面同步状态，因为已简化了交互逻辑

const isNeedTip = ref(false);
// 下一步草稿箱
const submitForm = async val => {
  if (submitLoading.value) return;
  submitLoading.value = true;
  let params = {};
  const courseSubForm = async formEl => {
    if (!formEl) return;
    return await formEl._value.validate(valid => {
      if (valid) {
      }
    });
  };
  const coursePeriodForm = async formEl => {
    if (!formEl) return;
    return await formEl._value.validate(valid => {
      if (valid) {
      }
    });
  };
  const formRes = await Promise.all([
    courseSubForm(courseFormRef),
    coursePeriodForm(ruleFormRef)
  ]);
  //判断当前并行返回的校验
  if (formRes[0] == true && formRes[1] == true) {
    let resForm = { ...courseForm, ...ruleForm };
    for (const paramsDataKey in resForm) {
      let isArray = Array.isArray(resForm[paramsDataKey]);
      if (isArray) {
        if (resForm[paramsDataKey].length > 0) {
          params[paramsDataKey] = resForm[paramsDataKey];
        }
      } else {
        if (resForm[paramsDataKey]) {
          params[paramsDataKey] = resForm[paramsDataKey];
        }
      }
    }
    if (route.query.courseId) {
      params.courseId = Number(route.query.courseId);
    }
    if (route.query.draftId || Number(useCourseStore.draftId)) {
      params.draftId =
        Number(route.query.draftId) || Number(useCourseStore.draftId);
    }
    if (!params?.courseFiles?.length) {
      delete params?.courseFiles;
    } else {
      params.courseFiles = courseForm.courseFiles.map((it, index) => {
        return {
          sortOrder: index + 1,
          fileIdentifier: it.fileIdentifier,
          fileType: it.fileType || "PHOTO"
        };
      });
    }
    if (!params?.coursePeriodFiles?.length) {
      delete params?.coursePeriodFiles;
    } else {
      params.coursePeriodFiles = ruleForm.coursePeriodFiles.map((it, index) => {
        return {
          sortOrder: index + 1,
          fileIdentifier: it.fileIdentifier,
          fileType: it.fileType || "PHOTO"
        };
      });
    }
    let coursePeriodTimes = [];
    if (tableData.value?.length) {
      tableData.value.forEach(item => {
        if (item?.openTime) {
          const times =
            item.openTime + " " + dayjs(item.classStart).format("HH:mm:ss");
          if (!dayjs(times).valueOf()) {
            ElMessage.error("请选择开课时间");
            submitLoading.value = false;
            isNeedTip.value = true;
            return;
          }
          if (
            dayjs(times).valueOf() &&
            dayjs(times).valueOf() <= dayjs().valueOf()
          ) {
            ElMessage.error("开课时间不能早于或等于当前时间");
            submitLoading.value = false;
            isNeedTip.value = true;
            return;
          }
          const signUpDeadline =
            dayjs(times).valueOf() - item.signUpDeadline * 60 * 60 * 1000;

          coursePeriodTimes.push({
            openTime: dayjs(times).valueOf(),
            signUpDeadline
          });
        } else {
          isNeedTip.value = true;
        }
      });
      if (isNeedTip.value) {
        isNeedTip.value = false;
        submitLoading.value = false;
        return;
      }
    } else {
      ElMessage.error("请设置开课时间！");
      submitLoading.value = false;
      return;
    }
    if (coursePeriodTimes?.length) {
      params.coursePeriodTimes = coursePeriodTimes;
    }
    if (ruleForm.prefix) {
      if (ruleForm.prefix === "课期数") {
        params.prefixRule = "TERM_NUMBER";
        delete params?.prefix;
      } else if (ruleForm.prefix === "无") {
        delete params?.prefix;
      } else if (ruleForm.prefix === "开课日期") {
        params.prefixRule = "OPEN_TIME";
        delete params?.prefix;
      } else {
        params.prefixRule = "OTHER";
      }
    }
    if (ruleForm.suffix) {
      if (ruleForm.suffix === "课期数") {
        params.suffixRule = "TERM_NUMBER";
        delete params?.suffix;
      } else if (ruleForm.suffix === "无") {
        delete params?.suffix;
      } else if (ruleForm.suffix === "开课日期") {
        params.suffixRule = "OPEN_TIME";
        delete params?.suffix;
      } else {
        params.suffixRule = "OTHER";
      }
    }
    if (ruleForm.leaders && ruleForm.leaders.length) {
      // console.log(
      //   "🐳ruleForm.leaders------------------------------>",
      //   ruleForm.leaders
      // );
      params.leaders = ruleForm.leaders.map(it => {
        return { userId: it.id || it, userType: "ORGANIZATION_ADMIN" };
      });
    } else {
      delete params.leaders;
    }
    if (ruleForm.lecturers && ruleForm.lecturers.length) {
      // console.log(
      //   "🦄ruleForm.lecturers------------------------------>",
      //   ruleForm.lecturers
      // );
      params.lecturers = ruleForm.lecturers.map(it => {
        return { userId: it.id || it, userType: "ORGANIZATION_ADMIN" };
      });
    } else {
      delete params.lecturers;
    }
    if (formPeriodData.value[5].tags && formPeriodData.value[5].tags.length) {
      params.coursePeriodTags = formPeriodData.value[5].tags;
    } else {
      delete params.coursePeriodTags;
    }
    // if (formPeriodData.value[3].tags && formPeriodData.value[3].tags.length) {
    //   params.teachers = formPeriodData.value[3].tags.map(it => {
    //     return { userId: it.id, userType: "TEACHER" };
    //   });
    // } else {
    //   delete params.teachers;
    // }

    if (formData.value[2].tags && formData.value[2].tags.length) {
      params.courseTags = formData.value[2].tags;
    } else {
      delete params.courseTags;
    }
    if (ruleForm.maxPeopleNumber > 0) {
      params.maxPeopleNumber = ruleForm.maxPeopleNumber;
    } else {
      delete params.maxPeopleNumber;
    }
    if (params.courseTypeId && params.courseTypeId.length) {
      params.courseTypeId = params.courseTypeId[params.courseTypeId.length - 1];
    }
    params.isCourseCover = openCourseImg.value;
    if (typeof ruleForm.complexId === "number" && !isNaN(ruleForm.complexId)) {
      params.complexId = ruleForm.complexId;
    } else {
      params.complexId = complexObj.value?.id;
    }
    const operateLog = {
      operateLogType: "COURSE_MANAGEMENT",
      operateType: ruleForm.coursePeriodName
        ? `将"${ruleForm.coursePeriodName}"课期中的基本信息保存在草稿箱`
        : `将基本信息保存在草稿箱`
    };
    // console.log("🎉params------------555555----333-------------->", params);
    // return;
    if (val === true) return;
    let [err, res] = await to(nextBasicInformation(params, operateLog));
    // console.log("🎉res--------55555---------------------->", res);
    if (res.code === 200) {
      ElMessage.success("当前资料已保存到草稿箱");
      let info = {
        infoShow: "课期行程",
        draftId: res.data.draftId,
        complete: true,
        periodName: ruleForm.coursePeriodName
      };
      emites("baseInfo", info);
      useCourseStore.saveDraftId(res.data.draftId);
      saveCurrentFormToStore();
    } else {
      ElMessage.error(`失败，${res.msg}`);
    }
  } else {
    submitLoading.value = false;
  }
  submitLoading.value = false;
};
const saveLoading = ref(false);
const saveDraft = async () => {
  if (saveLoading.value) return;
  saveLoading.value = true;
  let paramsData = {};
  let resForm = {};
  if (route.query.type === "createPeriod") {
    resForm = {
      ...ruleForm
    };
    paramsData.courseId = Number(route.query.courseId);
  } else {
    resForm = {
      ...ruleForm,
      ...courseForm
    };
  }
  for (const paramsDataKey in resForm) {
    let isArray = Array.isArray(resForm[paramsDataKey]);
    if (isArray) {
      if (resForm[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = resForm[paramsDataKey];
      }
    } else {
      if (resForm[paramsDataKey]) {
        paramsData[paramsDataKey] = resForm[paramsDataKey];
      }
    }
  }
  //草稿箱无数据不调保存接口
  if (
    paramsData &&
    Object.keys(paramsData).length === 2 &&
    paramsData.hasOwnProperty("suffix") &&
    paramsData.hasOwnProperty("prefix")
  ) {
    saveLoading.value = false;
    return;
  }
  let coursePeriodTimes = [];
  if (tableData.value?.length) {
    tableData.value.forEach(item => {
      if (item?.openTime) {
        const times =
          item.openTime + " " + dayjs(item.classStart).format("HH:mm:ss");
        if (!dayjs(times).valueOf()) {
          ElMessage.error("请选择开课时间");
          saveLoading.value = false;
          isNeedTip.value = true;
          return;
        }
        if (
          dayjs(times).valueOf() &&
          dayjs(times).valueOf() <= dayjs().valueOf()
        ) {
          ElMessage.error("开课时间不能早于或等于当前时间");
          saveLoading.value = false;
          isNeedTip.value = true;
          return;
        }
        const signUpDeadline =
          dayjs(times).valueOf() - item.signUpDeadline * 60 * 60 * 1000;

        coursePeriodTimes.push({
          openTime: dayjs(times).valueOf(),
          signUpDeadline
        });
      } else {
        isNeedTip.value = true;
      }
    });
    if (isNeedTip.value) {
      isNeedTip.value = false;
      saveLoading.value = false;
      return;
    }
  }
  if (coursePeriodTimes?.length) {
    paramsData.coursePeriodTimes = coursePeriodTimes;
  }
  if (route.query.draftId || useCourseStore.draftId) {
    paramsData.draftId =
      Number(route.query.draftId) || Number(useCourseStore.draftId);
  }
  if (formPeriodData.value[5].tags && formPeriodData.value[5].tags.length) {
    paramsData.coursePeriodTags = formPeriodData.value[5].tags;
  } else {
    delete paramsData.coursePeriodTags;
  }
  // if (formPeriodData.value[3].tags && formPeriodData.value[3].tags.length) {
  //   paramsData.teacherTags = formPeriodData.value[3].tags;
  // } else {
  //   delete paramsData.teacherTags;
  // }
  if (ruleForm.leaders && ruleForm.leaders.length) {
    // console.log(
    //   "🐳ruleForm.leaders------------------------------>",
    //   ruleForm.leaders
    // );
    paramsData.leaders = ruleForm.leaders.map(it => {
      return { userId: it.id || it, userType: "ORGANIZATION_ADMIN" };
    });
  } else {
    delete paramsData.leaders;
  }
  if (ruleForm.lecturers && ruleForm.lecturers.length) {
    // console.log(
    //   "🦄ruleForm.lecturers------------------------------>",
    //   ruleForm.lecturers
    // );
    paramsData.lecturers = ruleForm.lecturers.map(it => {
      return { userId: it.id || it, userType: "ORGANIZATION_ADMIN" };
    });
  } else {
    delete paramsData.lecturers;
  }
  if (formData.value[2].tags && formData.value[2].tags.length) {
    paramsData.courseTags = formData.value[2].tags;
  } else {
    delete paramsData.courseTags;
  }

  if (ruleForm.prefix) {
    if (ruleForm.prefix === "课期数") {
      paramsData.prefixRule = "TERM_NUMBER";
      delete paramsData.prefix;
    } else if (ruleForm.prefix === "无") {
      delete paramsData?.prefix;
    } else if (ruleForm.prefix === "开课日期") {
      paramsData.prefixRule = "OPEN_TIME";
      delete paramsData.prefix;
    } else {
      paramsData.prefixRule = "OTHER";
    }
  }
  if (ruleForm.suffix) {
    if (ruleForm.suffix === "课期数") {
      paramsData.suffixRule = "TERM_NUMBER";
      delete paramsData.suffix;
    } else if (ruleForm.suffix === "无") {
      delete paramsData?.suffix;
    } else if (ruleForm.suffix === "开课日期") {
      paramsData.suffixRule = "OPEN_TIME";
      delete paramsData.suffix;
    } else {
      paramsData.suffixRule = "OTHER";
    }
  }
  if (ruleForm.maxPeopleNumber > 0) {
    paramsData.maxPeopleNumber = ruleForm.maxPeopleNumber;
  } else {
    delete paramsData.maxPeopleNumber;
  }
  if (paramsData.courseTypeId && paramsData.courseTypeId.length) {
    paramsData.courseTypeId =
      paramsData.courseTypeId[paramsData.courseTypeId.length - 1];
  }
  paramsData.isCourseCover = openCourseImg.value;
  if (!isEmpty(ruleForm.complexId)) {
    if (typeof ruleForm.complexId === "number" && !isNaN(ruleForm.complexId)) {
      paramsData.complexId = ruleForm.complexId;
    } else {
      paramsData.complexId = complexObj.value?.id;
    }
  } else {
    delete paramsData.complexId;
  }
  const operateLog = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `将"${ruleForm.coursePeriodName}"课期中的基础信息保存在草稿箱`
  };
  // console.log("🐬paramsData------------------------------>", paramsData);
  // return;
  let [err, res] = await to(saveBasicInformation(paramsData, operateLog));
  if (res.code === 200) {
    ElMessage.success("当前资料已保存到草稿箱");
    let info = {
      infoShow: "基础信息",
      draftId: res.data.draftId,
      complete: true,
      periodName: ruleForm.coursePeriodName
    };
    useCourseStore.saveDraftId(res.data.draftId);
    emites("baseInfo", info);
  } else {
    ElMessage.error(`保存失败，${res.msg}`);
    saveLoading.value = false;
  }
  if (err) {
    ElMessage.error(`保存失败,${err}`);
    saveLoading.value = false;
  }
  saveLoading.value = false;
};
const courseTypeChange = val => {
  ruleForm.courseTypeId = val[val.length - 1];
};
const srcList = ref([]);
// 获取课程基础信息（存在courseId）
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    id: route.query?.courseId || 0
  };
  const [err, result] = await requestTo(courseFindId(paramsData));
  if (result) {
    // console.log("🦄result---------------44444--------------->", result);
    courseForm.maxAge = result.maxAge;
    courseForm.minAge = result.minAge;
    if (result.tags) {
      courseForm.courseTags = result.tags;
      formData.value[2].tags = result.tags;
    }
    termPeriodNumber.value = result.termNumber || 0;
    courseForm.courseName = result.name;
    courseForm.courseTypeId = result.courseType?.id;
    if (result?.files?.length) {
      result?.files.map(item => {
        courseForm.courseFiles.push(item.uploadFile);
        fileList.value.push(item.uploadFile);
        srcList.value.push(item.uploadFile.url);
      });
    }

    // 初始化封面数据结构
    initializeCoverData();
  }
  if (err) {
    ElMessage.error(err);
  }
  getListLoading.value = false;
};
// 获取课程草稿信息
const getListing = ref(false);
const getCourseData = async data => {
  if (getListing.value) {
    return;
  }
  getListing.value = true;
  let paramsData = {
    draftId:
      route.query.type === "draft"
        ? Number(route.query.draftId)
        : Number(useCourseStore.draftId)
  };
  const [err, result] = await requestTo(findByCourseDraftId(paramsData));
  if (result) {
    // console.log("🐳result---------3333--333------------------->", result);
    courseForm.courseName = result.name;
    courseForm.maxAge = result.maxAge;
    courseForm.minAge = result.minAge;
    if (result.tags) {
      courseForm.courseTags = result.tags;
      formData.value[2].tags = result.tags;
    }
    courseForm.courseTypeId = result.courseType?.id;
    courseForm.Maximum = result.maxPeopleNumber;
    if (result?.files?.length) {
      result?.files.map(item => {
        courseForm.courseFiles.push(item.uploadFile);
        fileList.value.push(item.uploadFile);
      });
    }
  }
  if (err) {
    ElMessage.error(err);
  }
  getListing.value = false;
};
const complexObj = ref({}); //课期查询的基地对象(解决回显的基地已被删除问题)
const leaderList = ref([]); //储存课期查询的领队数据
const lecturerList = ref([]); //储存课期查询的讲师数据
// 获取课期草稿信息
const getPerioding = ref(false);
const getCoursePeriodData = async data => {
  if (getPerioding.value) {
    return;
  }
  getPerioding.value = true;
  let paramsData = {
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId)
  };
  const [err, result] = await requestTo(findByCoursePeriodDraftId(paramsData));
  if (result) {
    // console.log("🐳result---------444444--55555------------------>", result);
    openCourseImg.value = !!result.isCourseCover;
    if (result.tags) {
      ruleForm.coursePeriodTags = result.tags;
      formPeriodData.value[5].tags = result.tags;
      periodSuffixOptions.value = [
        ...periodSuffixOptions.value,
        ...formPeriodData.value[5].tags?.map((label, index) => ({
          value: periodSuffixOptions.value.length + index + 1,
          label
        }))
      ];
      periodPrefixOptions.value = [
        ...periodPrefixOptions.value,
        ...formPeriodData.value[5].tags?.map((label, index) => ({
          value: periodSuffixOptions.value.length + index + 1,
          label
        }))
      ];
    }
    if (result.teacherTags) {
      ruleForm.teacherTags = result.teacherTags;
      formPeriodData.value[3].tags = result.teacherTags;
    }
    if (result.prefix) {
      ruleForm.prefix = result.prefix;
    } else {
      ruleForm.prefix = suffFn(result.prefixRule);
      prefixRule.value = result.prefixRule;
    }
    if (result.suffix) {
      ruleForm.suffix = result.suffix;
    } else {
      ruleForm.suffix = suffFn(result.suffixRule);
      suffixRule.value = result.suffixRule;
    }
    if (result.prefix && result.suffix && result.name) {
      periodName.value = `${result.prefix}-${result.name}-${result.suffix}`;
    }
    ruleForm.coursePeriodName = result.name;
    emites("baseInfoPeriod", result.name);
    ruleForm.complexId = result.complex?.name || result.complex;
    complexObj.value = result.complex;
    if (result.maxPeopleNumber > 0) {
      ruleForm.maxPeopleNumber = result.maxPeopleNumber;
    }
    ruleForm.lecturers = result.lecturers?.map(it => ({
      id: it.id,
      name: it.name,
      value: it.id,
      label: it.label
    }));
    lecturerList.value = result.lecturers;
    ruleForm.leaders = result.leaders?.map(it => ({
      id: it.id,
      name: it.name,
      value: it.id,
      label: it.label
    }));
    leaderList.value = result.leaders;
    if (result?.files?.length) {
      result?.files.map(item => {
        ruleForm.coursePeriodFiles.push(item.uploadFile);
        fileListCoursePeriod.value.push(item.uploadFile);
      });

      // 初始化封面数据结构
      initializeCoverData();
    }
  }
  if (result?.draftCoursePeriodTimes && result?.draftCoursePeriodTimes.length) {
    tableData.value = result.draftCoursePeriodTimes?.map(item => {
      return {
        openTime: dayjs(item.openTime).format("YYYY-MM-DD"),
        weekDay: dayjs(item.openTime).format("dddd"),
        classStart: item.openTime,
        signUpDeadline: (item.openTime - item.signUpDeadline) / 60 / 60 / 1000,
        sortTime: item.openTime
      };
    });
  }
  if (err) {
    ElMessage.error(err);
  }
  getPerioding.value = false;
};
// 回显前后缀
const suffFn = val => {
  if (val === "TERM_NUMBER") {
    return "课期数";
  } else if (val === "OPEN_TIME") {
    return "开课日期";
  } else {
    return "无";
  }
};
// 添加日期
const setData = () => {
  selectedDates.value?.forEach(item => {
    tableData.value.push({
      openTime: dayjs(item).format("YYYY-MM-DD"),
      weekDay: dayjs(item).format("dddd"),
      classStart: "",
      signUpDeadline: 0,
      sortTime: dayjs(item).valueOf()
    });
  });
};
const addDAte = () => {
  dialogVisiblesc.value = true;
};
const isSelected = dateString => {
  return selectedDates.value.some(
    d => dayjs(d).format("YYYY-MM-DD") === dateString
  );
};
const toggleDate = dateString => {
  const date = new Date(dateString);
  const index = selectedDates.value.findIndex(
    d => dayjs(d).format("YYYY-MM-DD") === dateString
  );
  if (route.query.type === "new") {
    if (index === -1) {
      selectedDates.value = [date];
    } else {
      selectedDates.value.splice(index, 1);
    }
  } else {
    if (index === -1) {
      selectedDates.value.push(date);
    } else {
      selectedDates.value.splice(index, 1);
    }
  }
};
// 修改这里：用空格代替逗号分隔
const formattedDates = computed(() => {
  return selectedDates.value.map(d => dayjs(d).format("YYYY-MM-DD")).join("  "); // 改为空格分隔
});
// 清空选择日期
const handleCancle = () => {
  selectedDates.value = [];
  dialogVisiblesc.value = false;
};
// 删除课期
const getDelted = val => {
  tableData.value.splice(val, 1);
};
function disabledDate(time) {
  const date1 = new Date(time + "T00:00:00"); // 禁用今天之前的日期
  return date1.getTime() < new Date().setHours(0, 0, 0, 0);
}
// 复制
const copyData = ref({});
const getCopy = val => {
  copyData.value = { ...val };
};
// 粘贴
const getPaste = val => {
  tableData.value[val].classStart = copyData.value.classStart;
  tableData.value[val].signUpDeadline = copyData.value.signUpDeadline;
};
// 选择日期确认
const setChooseDate = () => {
  if (selectedDates.value?.length) {
    setData();
    if (tableData.value?.length) {
      // 去掉重复项
      tableData.value = tableData.value.filter(
        (item, index, self) =>
          index === self.findIndex(t => t.sortTime === item.sortTime)
      );
      // 升序排序
      tableData.value = tableData.value.sort(function (a, b) {
        return a.sortTime - b.sortTime;
      });
    }
  } else {
    ElMessage.error("请先选择日期");
  }
  handleCancle();
};
// 保存当前表单数据到store
const saveCurrentFormToStore = () => {
  // 保存课期基本信息
  if (ruleForm.complexId) {
    if (isNumber(ruleForm.complexId)) {
      useCourseStore.saveBaseInfo({ id: ruleForm.complexId });
    } else {
      useCourseStore.saveBaseInfo({ id: complexObj.value.id });
    }
  }
  if (ruleForm.leaders && ruleForm.leaders.length) {
    // console.log(
    //   "🐳ruleForm.leaders----cunchu-------------------------->",
    //   ruleForm.leaders
    // );
    let ids = ruleForm.leaders.map(it => ({
      id: it.id,
      name: it.name,
      label: it.label,
      value: it.id
    }));
    useCourseStore.saveLeaderInfo(ids);
  }
  if (ruleForm.lecturers && ruleForm.lecturers.length) {
    // console.log(
    //   "🐳ruleForm.lecturers----cunchu-------------------------->",
    //   ruleForm.lecturers
    // );
    let ids = ruleForm.lecturers.map(it => ({
      id: it.id,
      name: it.name,
      label: it.label,
      value: it.id
    }));
    useCourseStore.saveLecturerInfo(ids);
  }
  useCourseStore.saveteachersInfo(ruleForm.teacherTags);
  useCourseStore.savePeriodName(ruleForm.coursePeriodName);
  useCourseStore.savePeriodMaxPeopleNumber(ruleForm.maxPeopleNumber);
  useCourseStore.saveCoverInfo(ruleForm.coursePeriodFiles);
  useCourseStore.savePeriodTableData(tableData.value);
  useCourseStore.savePeriodPrefix(ruleForm.prefix);
  useCourseStore.savePeriodSuffix(ruleForm.suffix);
  useCourseStore.savePeriodPrefixRule(prefixRule.value);
  useCourseStore.savePeriodSuffixRule(suffixRule.value);
  useCourseStore.savePeriodPrefixOptions(periodPrefixOptions.value);
  useCourseStore.savePeriodSuffixOptions(periodSuffixOptions.value);
  useCourseStore.saveOpenCourseImg(openCourseImg.value);
  if (formPeriodData.value[5].tags && formPeriodData.value[5].tags.length) {
    useCourseStore.savePeriodTags(formPeriodData.value[5].tags);
  }
  //  保存课程基本信息
  if (formData.value[2].tags && formData.value[2].tags.length) {
    courseForm.courseTags = formData.value[2].tags;
  }
  useCourseStore.saveCourseBaseDraft({ ...courseForm });
};
// 从store中获取并更新表单数据
const updateFormFromStore = () => {
  if (route.query.create === "create") {
    if (useCourseStore.lecturerInfo && useCourseStore.lecturerInfo.length) {
      // console.log(
      //   "🎁useCourseStore.lecturerInfo------------------------------>",
      //   useCourseStore.lecturerInfo
      // );
      ruleForm.lecturers = useCourseStore.lecturerInfo?.map(it => ({
        id: it.id,
        name: it.name,
        label: it.label,
        value: it.id
      }));
    }
    if (useCourseStore.leaderInfo && useCourseStore.leaderInfo.length) {
      // console.log(
      //   "🐬useCourseStore.leaderInfo------------------------------>",
      //   useCourseStore.leaderInfo
      // );
      ruleForm.leaders = useCourseStore.leaderInfo?.map(it => ({
        id: it.id,
        name: it.name,
        label: it.label,
        value: it.id
      }));
    }
    if (useCourseStore.baseInfo) {
      ruleForm.complexId = useCourseStore.baseInfo?.id;
    }
    // 从store中恢复课期名称和人数上限
    ruleForm.coursePeriodName = useCourseStore.periodName;
    ruleForm.maxPeopleNumber = useCourseStore.periodMaxPeopleNumber;
    ruleForm.teachers = useCourseStore.teachersInfo;
    if (useCourseStore.teachersInfo && useCourseStore.teachersInfo.length) {
      formPeriodData.value[3].tags = useCourseStore.teachersInfo;
    }

    if (useCourseStore.periodPrefix) {
      ruleForm.prefix = useCourseStore.periodPrefix;
    } else {
      ruleForm.prefix = suffFn(useCourseStore.prefixRule);
    }
    if (useCourseStore.periodSuffix) {
      ruleForm.suffix = useCourseStore.periodSuffix;
    } else {
      ruleForm.suffix = suffFn(useCourseStore.suffixRule);
    }
    // formPeriodData.value[5].options = formPeriodData.value[5].options.unshift(useCourseStore.teachersInfo)
    // console.log(
    //   "🌵 formPeriodData.value[5].options------------------------------>"
    // );
    // formPeriodData.value[4].options = formPeriodData.value[4].options.unshift(useCourseStore.teachersInfo)
    // console.log(
    //   "🍪  formPeriodData.value[4].options------------------------------>"
    // );
    periodPrefixOptions.value = useCourseStore.periodPrefixOptions;
    periodSuffixOptions.value = useCourseStore.periodSuffixOptions;
    ruleForm.coursePeriodTags = useCourseStore.periodTags;
    formPeriodData.value[5].tags = useCourseStore.periodTags;
    openCourseImg.value = useCourseStore.openCourseImg;
    // 从store中恢复封面图和开课时间设置
    if (useCourseStore.coverInfo && useCourseStore.coverInfo.length) {
      ruleForm.coursePeriodFiles = useCourseStore.coverInfo;
      fileListCoursePeriod.value = useCourseStore.coverInfo;
    }
    if (
      useCourseStore.periodTableData &&
      useCourseStore.periodTableData.length
    ) {
      tableData.value = useCourseStore.periodTableData;
    }
    formPeriodData.value[3].tags = useCourseStore.teachersInfo;
    courseForm.courseName = useCourseStore.courseBaseDraft.courseName;
    courseForm.courseTypeId = useCourseStore.courseBaseDraft.courseTypeId;
    courseForm.courseFiles = useCourseStore.courseBaseDraft.courseFiles;
    fileList.value = useCourseStore.courseBaseDraft.courseFiles;
    courseForm.maxAge = useCourseStore.courseBaseDraft.maxAge;
    courseForm.minAge = useCourseStore.courseBaseDraft.minAge;
    courseForm.courseTags = useCourseStore.courseBaseDraft.courseTags;
    formData.value[2].tags = useCourseStore.courseBaseDraft.courseTags;

    // 初始化封面数据结构
    initializeCoverData();
  }
};
const clearFromStore = () => {
  useCourseStore.saveLeaderInfo([]);
  useCourseStore.saveLecturerInfo([]);
  useCourseStore.saveBaseInfo({});
  useCourseStore.saveCourseBaseDraft({});
  useCourseStore.savePeriodName("");
  useCourseStore.savePeriodMaxPeopleNumber("");
  useCourseStore.saveCoverInfo([]);
  useCourseStore.savePeriodTableData([]);
  useCourseStore.savePeriodPrefix("");
  useCourseStore.savePeriodSuffix("");
  useCourseStore.savePeriodPrefixRule("");
  useCourseStore.savePeriodSuffixRule("");
  useCourseStore.savePeriodTags([]);
  useCourseStore.saveOpenCourseImg("");
  useCourseStore.saveteachersInfo([]);
};
// 添加师资删除
const handleteacherClose = it => {
  formPeriodData.value[3].tags = formPeriodData.value[3].tags.filter(
    it1 => it1.id !== it.id
  );
};
// 点击新建基地领队讲师
const clickEvt = item => {
  const currentPath = router.currentRoute.value.path;
  const isFromCopy = currentPath.includes("periodCopy");
  // 无论点击哪个类型的新建按钮，都保存当前表单数据
  saveCurrentFormToStore();

  if (item.text === "新建基地") {
    router.push({
      path: "/courseCreate/baseAdd",
      query: {
        type: route.query.type,
        copyId: route.query.copyId,
        id: route.query.id,
        name: route.query.name,
        courseId: route.query.courseId,
        termNumber: route.query.termNumber,
        draftId: route.query.draftId || useCourseStore.draftId
      }
    });
  } else if (item.text === "新建领队") {
    router.push({
      path: "/courseCreate/leaderCreate",
      query: {
        type: "new",
        text: "leader",
        roleId: 3,
        copyId: route.query.copyId,
        type: route.query.type,
        id: route.query.id,
        courseId: route.query.courseId,
        name: route.query.name,
        termNumber: route.query.termNumber,
        draftId: route.query.draftId || useCourseStore.draftId
      }
    });
  } else if (item.text === "新建讲师") {
    router.push({
      path: "/courseCreate/lecturerCreate",
      query: {
        type: "new",
        text: "teacher",
        roleId: 2,
        copyId: route.query.copyId,
        type: route.query.type,
        courseId: route.query.courseId,
        id: route.query.id,
        name: route.query.name,
        termNumber: route.query.termNumber,
        draftId: route.query.draftId || useCourseStore.draftId
      }
    });
  } else if (item.text === "添加师资") {
    router.push({
      path: "/courseCreate/teachers",
      query: {
        copyId: route.query.copyId,
        type: route.query.type,
        id: route.query.id,
        courseId: route.query.courseId,
        name: route.query.name,
        termNumber: route.query.termNumber,
        draftId: route.query.draftId || useCourseStore.draftId
      }
    });
  }
};
router.afterEach((to, from) => {
  // console.log("🎁to------------------------------>", to);
  if (whitePath.includes(to.path)) {
    clearFromStore();
  }
});
// 自动保存
const autoSaveInterval = ref(null); // 新增：用于存储定时器引用
const startAutoSave = () => {
  clearInterval(autoSaveInterval.value); // 清除已有定时器以防重复
  autoSaveInterval.value = setInterval(
    () => {
      saveDraft(); // 调用保存草稿的方法
    },
    5 * 60 * 1000
  ); // 每5分钟执行一次
};

// 领队讲师查询 type为2讲师 type为3领队
const leaderFindApi = async type => {
  const params = {
    roleId: type
  };
  let [err, res] = await requestTo(leaderLecturerFind(params));
  if (res) {
    if (type == 2) {
      formPeriodData.value[4].options = res.map(it => {
        return {
          ...it,
          label: it.name,
          value: it.id
        };
      });
    } else {
      formPeriodData.value[3].options = res.map(it => {
        return {
          ...it,
          label: it.name,
          value: it.id
        };
      });
    }
  } else {
    console.log("🌵-----err-----", err);
  }
};
//  基地查询不分页
const complexIdApi = async () => {
  let [err, res] = await requestTo(complexId());
  if (res) {
    formPeriodData.value[1].options = res.map(it => {
      return {
        ...it,
        label: it.name,
        value: it.id
      };
    });
  }
};
// 查询课程分类
const courseTypeFindApi = async () => {
  let [err, res] = await requestTo(findAllCourseType());
  if (res) {
    formData.value[1].options = transformArray(res);
  }
};
function transformArray(inputArray) {
  return inputArray.map(item => {
    const newItem = {
      value: item.id,
      label: item.name
    };
    // 如果有 children，则递归转换
    if (item.children && item.children.length > 0) {
      newItem.children = transformArray(item.children);
    }
    return newItem;
  });
}
// 检查课程封面是否有图片，决定是否允许切换
const beforeSwitchChange = () => {
  // 如果已经有提示框在显示，直接阻止后续操作
  if (isPromptVisible.value) {
    return Promise.reject(new Error("操作过于频繁"));
  }

  // 如果是要关闭开关，直接允许
  if (openCourseImg.value) {
    return Promise.resolve(true);
  }

  // 检查课程封面是否有图片
  if (!courseForm.courseFiles || courseForm.courseFiles.length === 0) {
    isPromptVisible.value = true;
    ElMessage.warning("请先上传课程封面图片，再启用此功能");

    // 设置延迟，防止短时间内连续触发
    setTimeout(() => {
      isPromptVisible.value = false;
    }, 1500);

    return Promise.reject(new Error("没有课程封面图片"));
  }

  // 有课程封面图片，允许切换
  switchLoading.value = true;
  return new Promise(resolve => {
    setTimeout(() => {
      switchLoading.value = false;
      resolve(true);
    }, 0);
  });
};

// 打开使用课程图片开关
const openImgChange = val => {
  if (val) {
    // 使用课程图片，清空课期自己的图片
    ownCoursePeriodCover.value = [];

    // 完整复制课程图片
    courseCopyCover.value = courseForm.courseFiles.map(file =>
      JSON.parse(JSON.stringify(file))
    );

    // 更新课期文件为课程图片的副本
    ruleForm.coursePeriodFiles = [...courseCopyCover.value];

    // 更新显示列表
    fileListCoursePeriod.value = [...courseCopyCover.value];
  } else {
    // 关闭同步，清空所有课期图片
    courseCopyCover.value = [];
    ownCoursePeriodCover.value = [];
    ruleForm.coursePeriodFiles = [];
    fileListCoursePeriod.value = [];
  }
};

const btnData = ref([
  { id: 1, name: "放弃新建" },
  { id: 2, name: "保存到草稿箱" }
]);
const deleteDraft = async () => {
  let operateLogDraft = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: route.query.draftId
      ? `删除了草稿id为"${route.query.draftId}"的草稿数据`
      : `删除了草稿id为"${Number(useCourseStore.draftId)}"的草稿数据`
  };
  const [err, res] = await to(
    draftDelete(
      { id: Number(route.query.draftId) || Number(useCourseStore.draftId) },
      operateLogDraft
    )
  );
  if (res.code === 200) {
    ElMessage.success("删除成功");
  } else {
    ElMessage.error("删除失败");
  }
};
const dropdownBtn = val => {
  let titlt =
    val.id === 1
      ? `请确认是否清除当前编辑和提交的所有资料，并删除当前编辑资料在草稿箱中对应的草稿条目`
      : `请确认是否将当前编辑和提交的所有资料保存到草稿箱，并退出编辑页面`;
  ElMessageBox.confirm(`${titlt}`, `退出并${val.name}`, {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(async () => {
      if (val.id === 1) {
        if (Number(route.query.draftId) || Number(useCourseStore.draftId)) {
          deleteDraft();
        }
        if (route.query.type === "draft") {
          router.replace("/course/drafts");
        } else if (route.query.type === "edite") {
          router.replace("/course/courseDetails");
        } else {
          router.go(-1);
        }
      } else if (val.id === 2) {
        saveDraft();
        if (route.query.type === "draft") {
          router.replace("/course/drafts");
        } else if (route.query.type === "edite") {
          router.replace("/course/courseDetails");
        } else {
          router.go(-1);
        }
      }
    })
    .catch(() => {});
};
const mergelabel = (label, value) => {
  if (typeof label === "number") {
    if (value === "leaders") {
      const item = leaderList.value?.find(it => it.id === label);
      if (item) {
        return item.name;
      } else {
        const itemOption = formPeriodData.value[3]?.options?.find(
          it => it.id === label
        );
        return itemOption?.name;
      }
    } else {
      const item = lecturerList.value?.find(it => it.id === label);
      if (item) {
        return item.name;
      } else {
        const itemOption = formPeriodData.value[4]?.options?.find(
          it => it.id === label
        );
        return itemOption?.name;
      }
    }
  } else {
    return label;
  }
};
onMounted(async () => {
  complexIdApi();
  courseTypeFindApi();
  leaderFindApi(3); //领队
  leaderFindApi(2); //讲师
  if (route.query.create === "create") {
    updateFormFromStore();
  } else {
    if (route.query?.type === "createPeriod") {
      getTableList();
    }
    if (route.query.type === "draft") {
      if (route.query.courseId) {
        getTableList();
      } else {
        getCourseData();
      }
      getCoursePeriodData();
    }
    if (Number(useCourseStore.draftId)) {
      getCourseData();
      getCoursePeriodData();
    }
  }
  startAutoSave(); // 开始自动保存
});
onUnmounted(() => {
  clearInterval(autoSaveInterval.value); // 组件卸载时清除定时器
});
// 表格宽度
const tableWidth = ref("calc(100vw - 486px)");
const submitFormatt = async () => {
  const tre = ref(false);
  submitLoading.value = true;
  let params = {};
  const courseSubForm = async formEl => {
    if (!formEl) return;
    return await formEl._value.validate(valid => {
      if (valid) {
      }
    });
  };
  const coursePeriodForm = async formEl => {
    if (!formEl) return;
    return await formEl._value.validate(valid => {
      if (valid) {
      }
    });
  };
  const formRes = await Promise.all([
    courseSubForm(courseFormRef),
    coursePeriodForm(ruleFormRef)
  ]);
  //判断当前并行返回的校验
  if (formRes[0] == true && formRes[1] == true) {
    let resForm = { ...courseForm, ...ruleForm };
    for (const paramsDataKey in resForm) {
      let isArray = Array.isArray(resForm[paramsDataKey]);
      if (isArray) {
        if (resForm[paramsDataKey].length > 0) {
          params[paramsDataKey] = resForm[paramsDataKey];
        }
      } else {
        if (resForm[paramsDataKey]) {
          params[paramsDataKey] = resForm[paramsDataKey];
        }
      }
    }
    if (route.query.courseId) {
      params.courseId = Number(route.query.courseId);
    }
    if (route.query.draftId || Number(useCourseStore.draftId)) {
      params.draftId =
        Number(route.query.draftId) || Number(useCourseStore.draftId);
    }
    if (!params?.courseFiles?.length) {
      delete params?.courseFiles;
    } else {
      params.courseFiles = courseForm.courseFiles.map((it, index) => {
        return {
          sortOrder: index + 1,
          fileIdentifier: it.fileIdentifier,
          fileType: it.fileType || "PHOTO"
        };
      });
    }
    if (!params?.coursePeriodFiles?.length) {
      delete params?.coursePeriodFiles;
    } else {
      params.coursePeriodFiles = ruleForm.coursePeriodFiles.map((it, index) => {
        return {
          sortOrder: index + 1,
          fileIdentifier: it.fileIdentifier,
          fileType: it.fileType || "PHOTO"
        };
      });
    }
    let coursePeriodTimes = [];
    if (tableData.value?.length) {
      tableData.value.forEach(item => {
        if (item?.openTime) {
          const times =
            item.openTime + " " + dayjs(item.classStart).format("HH:mm:ss");
          if (!dayjs(times).valueOf()) {
            ElMessage.error("请选择开课时间");
            submitLoading.value = false;
            isNeedTip.value = true;
            return;
          }
          if (
            dayjs(times).valueOf() &&
            dayjs(times).valueOf() <= dayjs().valueOf()
          ) {
            ElMessage.error("开课时间不能早于或等于当前时间");
            submitLoading.value = false;
            isNeedTip.value = true;
            return;
          }
          const signUpDeadline =
            dayjs(times).valueOf() - item.signUpDeadline * 60 * 60 * 1000;

          coursePeriodTimes.push({
            openTime: dayjs(times).valueOf(),
            signUpDeadline
          });
        } else {
          isNeedTip.value = true;
        }
      });
      if (isNeedTip.value) {
        isNeedTip.value = false;
        submitLoading.value = false;
        return;
      }
    } else {
      ElMessage.error("请设置开课时间！");
      submitLoading.value = false;
      return;
    }
    if (coursePeriodTimes?.length) {
      params.coursePeriodTimes = coursePeriodTimes;
    }
    if (ruleForm.prefix) {
      if (ruleForm.prefix === "课期数") {
        params.prefixRule = "TERM_NUMBER";
        delete params?.prefix;
      } else if (ruleForm.prefix === "无") {
        delete params?.prefix;
      } else if (ruleForm.prefix === "开课日期") {
        params.prefixRule = "OPEN_TIME";
        delete params?.prefix;
      } else {
        params.prefixRule = "OTHER";
      }
    }
    if (ruleForm.suffix) {
      if (ruleForm.suffix === "课期数") {
        params.suffixRule = "TERM_NUMBER";
        delete params?.suffix;
      } else if (ruleForm.suffix === "无") {
        delete params?.suffix;
      } else if (ruleForm.suffix === "开课日期") {
        params.suffixRule = "OPEN_TIME";
        delete params?.suffix;
      } else {
        params.suffixRule = "OTHER";
      }
    }
    if (formPeriodData.value[5].tags && formPeriodData.value[5].tags.length) {
      params.coursePeriodTags = formPeriodData.value[5].tags;
    } else {
      delete params.coursePeriodTags;
    }
    if (ruleForm.leaders && ruleForm.leaders.length) {
      params.leaders = ruleForm.leaders.map(it => {
        return { userId: it.id || it, userType: "ORGANIZATION_ADMIN" };
      });
    } else {
      delete params.leaders;
    }
    if (ruleForm.lecturers && ruleForm.lecturers.length) {
      params.lecturers = ruleForm.lecturers.map(it => {
        return { userId: it.id || it, userType: "ORGANIZATION_ADMIN" };
      });
    } else {
      delete params.lecturers;
    }
    if (formData.value[2].tags && formData.value[2].tags.length) {
      params.courseTags = formData.value[2].tags;
    } else {
      delete params.courseTags;
    }
    if (ruleForm.maxPeopleNumber > 0) {
      params.maxPeopleNumber = ruleForm.maxPeopleNumber;
    } else {
      delete params.maxPeopleNumber;
    }
    if (params.courseTypeId && params.courseTypeId.length) {
      params.courseTypeId = params.courseTypeId[params.courseTypeId.length - 1];
    }
    params.isCourseCover = openCourseImg.value;
    if (typeof ruleForm.complexId === "number" && !isNaN(ruleForm.complexId)) {
      params.complexId = ruleForm.complexId;
    } else {
      params.complexId = complexObj.value?.id;
    }
    const operateLog = {
      operateLogType: "COURSE_MANAGEMENT",
      operateType: `将"${ruleForm.coursePeriodName}"课期中的基本信息保存在草稿箱`
    };
    let [err, res] = await to(nextBasicInformation(params, operateLog));
    if (res.code === 200) {
      ElMessage.success("当前资料已保存到草稿箱");
      useCourseStore.saveDraftId(res.data.draftId);
      let info = {
        draftId: res.data.draftId,
        complete: true,
        periodName: ruleForm.coursePeriodName
      };
      emites("basedraftId", info);
      tre.value = true;
    } else {
      ElMessage.error(`失败，${res.msg}`);
      tre.value = false;
    }
  } else {
    submitLoading.value = false;
  }
  submitLoading.value = false;
  return tre.value;
};
// 用于草稿箱点击基础信息校验
defineExpose({
  submitFormatt,
  submitForm
});

// 初始化课程和课期封面的数据结构
const initializeCoverData = () => {
  // 先清空所有数据
  courseCopyCover.value = [];
  ownCoursePeriodCover.value = [];

  if (openCourseImg.value) {
    // 完整复制课程图片，保持原顺序
    if (courseForm.courseFiles && courseForm.courseFiles.length) {
      courseForm.courseFiles.forEach(file => {
        courseCopyCover.value.push(JSON.parse(JSON.stringify(file)));
      });

      // 使用课程图片作为课期图片
      ruleForm.coursePeriodFiles = [...courseCopyCover.value];
      fileListCoursePeriod.value = [...courseCopyCover.value];
    }
  } else {
    // 如果关闭同步，保留课期原有图片
    if (ruleForm.coursePeriodFiles && ruleForm.coursePeriodFiles.length) {
      ownCoursePeriodCover.value = [...ruleForm.coursePeriodFiles];
      ruleForm.coursePeriodFiles = [...ownCoursePeriodCover.value];
      fileListCoursePeriod.value = [...ownCoursePeriodCover.value];
    }
  }
};
</script>

<template>
  <div class="base-info">
    <div class="base-container">
      <el-divider content-position="left">课程基础信息</el-divider>
      <div class="course-info">
        <div class="course-des">
          <el-form
            ref="courseFormRef"
            :model="courseForm"
            :rules="rules"
            label-width="auto"
            label-position="right"
            class="course-form"
          >
            <!-- 课程信息 -->
            <el-form-item
              v-for="(item, index) in formData"
              :key="index"
              :prop="item.prop"
              :label="item.label"
              :inline-message="false"
              :show-message="true"
              error-placement="right"
            >
              <!-- input输入 -->
              <template v-if="item.type === 'input'">
                <el-input
                  v-model.trim="courseForm[item.prop]"
                  :placeholder="item.placeholder"
                  :style="{ width: item.width }"
                  :disabled="route.query.courseId ? true : false"
                  :maxlength="item.maxLength"
                  :show-word-limit="item.maxLength"
                />
              </template>
              <!-- cascader级联选择 -->
              <template v-if="item.type === 'cascader'">
                <el-cascader
                  v-model="courseForm[item.prop]"
                  :options="item.options"
                  :show-all-levels="false"
                  style="width: 350px"
                  :disabled="route.query.courseId ? true : false"
                  @change="courseTypeChange"
                />
              </template>
              <!-- input年龄段输入 -->
              <template v-if="item.type === 'inputToInput'">
                <el-input
                  v-model="courseForm.minAge"
                  placeholder="输最小年龄"
                  style="width: 110px"
                  :disabled="route.query.courseId ? true : false"
                  @change="handleChangeMin"
                  @input="filterInputMin($event)"
                />
                <span class="separator">-</span>
                <el-input
                  v-model="courseForm.maxAge"
                  placeholder="输最大年龄"
                  style="width: 110px"
                  :disabled="route.query.courseId ? true : false"
                  @change="handleChangeMax"
                  @input="filterInputMax($event)"
                />
              </template>
              <!-- 动态添加tag -->
              <template v-if="item.type === 'tag'">
                <el-tag
                  v-for="tag in item.tags"
                  :key="tag"
                  style="margin: 0 10px 4px 0"
                  :closable="route.query.courseId ? false : true"
                  :disable-transitions="false"
                  @close="handleClose(tag)"
                >
                  {{ tag }}
                </el-tag>
                <div
                  v-if="!route.query.courseId"
                  style="display: block; margin-top: -6px"
                >
                  <el-input
                    v-if="inputVisible"
                    ref="InputRef"
                    v-model.trim="inputValue"
                    class="w-20"
                    size="small"
                    :disabled="route.query.courseId ? true : false"
                    @keyup.enter="handleInputConfirm"
                    @blur="handleInputConfirm"
                  />
                  <el-button
                    v-else
                    class="button-new-tag"
                    size="small"
                    @click="showInput"
                  >
                    +
                  </el-button>
                </div>
              </template>
            </el-form-item>
          </el-form>
        </div>
        <div v-if="!route.query.courseId" class="course-img">
          <div class="upload img-title">封面图</div>
          <el-upload
            v-model:file-list="fileList"
            action="#"
            :http-request="() => {}"
            list-type="picture-card"
            accept=".jpg,.jpeg,.png"
            :before-upload="beforeUpload"
            :on-remove="handleRemove"
            :on-preview="handlePictureCardPreview"
            :class="{ hideUploadBtn: fileList?.length >= 9 }"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
          <el-dialog v-model="dialogVisible">
            <img class="w-full" :src="dialogImageUrl" alt="Preview Image">
          </el-dialog>
          <div class="img-text">{{ imgText }}</div>
        </div>
        <div v-else class="course-img">
          <div class="upload img-title">封面图</div>
          <div class="img">
            <el-image
              :src="srcList[0]"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="srcList"
              :hide-on-click-modal="true"
              show-progress
              :initial-index="0"
              fit="cover"
              class="img-pic"
            />
          </div>
        </div>
      </div>
      <el-divider content-position="left">课期基础信息</el-divider>
      <div class="course-period-info">
        <div class="course-des">
          <el-form
            ref="ruleFormRef"
            :model="ruleForm"
            :rules="rule"
            label-width="auto"
            label-position="right"
            class="period-form"
          >
            <!-- 课期信息 -->
            <el-form-item
              v-for="(item, index) in formPeriodData"
              :key="index"
              :prop="item.prop"
              :label="item.label"
              :inline-message="false"
              :show-message="true"
              error-placement="right"
            >
              <!-- input输入 -->
              <template v-if="item.type === 'input'">
                <el-input
                  v-model.trim="ruleForm[item.prop]"
                  :placeholder="item.placeholder"
                  :style="{ width: item.width }"
                  :type="item.typeInput"
                  :maxlength="item.maxLength"
                  :show-word-limit="item.maxLength"
                />
              </template>
              <!-- 多选 -->
              <template v-if="item.type === 'selectMultiple'">
                <el-select
                  v-model="ruleForm[item.prop]"
                  multiple
                  :placeholder="item.placeholder"
                  :style="{ width: item.width }"
                >
                  <template #label="{ label }">
                    <span>{{ mergelabel(label, item.prop) }} </span>
                  </template>
                  <el-option
                    v-for="it in item.options"
                    :key="it"
                    :label="it.label"
                    :value="it"
                  />
                </el-select>
                <el-button
                  type="primary"
                  style="margin-left: 10px"
                  @click="clickEvt(item)"
                >
                  {{ item.text }}
                </el-button>
              </template>
              <!-- 单选 -->
              <template v-if="item.type === 'select'">
                <el-select
                  v-model="ruleForm[item.prop]"
                  :placeholder="item.placeholder"
                  :style="{ width: item.width }"
                >
                  <el-option
                    v-for="it in item.options"
                    :key="it.value"
                    :label="it.label"
                    :value="it.value"
                  />
                </el-select>
                <el-button
                  type="primary"
                  style="margin-left: 10px"
                  @click="clickEvt(item)"
                >
                  {{ item.text }}
                </el-button>
              </template>
              <!-- 动态添加tag -->
              <template v-if="item.type === 'tag'">
                <el-tag
                  v-for="tag in item.tags"
                  :key="tag"
                  style="margin: 0 10px 4px 0"
                  closable
                  :disable-transitions="false"
                  @close="handlePeriodClose(tag)"
                >
                  {{ tag }}
                </el-tag>
                <div style="display: block; margin-top: -6px">
                  <el-input
                    v-if="inputPeriodVisible"
                    ref="periodInputRef"
                    v-model.trim="inputPeriodValue"
                    class="w-20"
                    size="small"
                    @keyup.enter="handlePeriodInputConfirm"
                    @blur="handlePeriodInputConfirm"
                  />
                  <el-button
                    v-else
                    class="button-new-tag"
                    size="small"
                    @click="showPeriodInput"
                  >
                    +
                  </el-button>
                </div>
              </template>
              <template v-if="item.type === 'tagTab'">
                <el-tag
                  v-for="it in item.tags"
                  :key="it"
                  closable
                  type="primary"
                  style="margin-right: 10px; height: 32px"
                  @close="handleteacherClose(it)"
                >
                  {{ it.name }}
                </el-tag>
                <el-button
                  class="button-new-tag"
                  type="primary"
                  @click="clickEvt(item)"
                >
                  {{ item.text }}
                </el-button>
              </template>
            </el-form-item>
          </el-form>
        </div>
        <div class="course-img">
          <div class="upload">
            <div class="title">封面图</div>
            <div class="switch" style="margin-right: 50px">
              <el-switch
                v-model="openCourseImg"
                :loading="switchLoading"
                :before-change="beforeSwitchChange"
                @change="openImgChange"
              /><span style="margin-left: 10px">使用课程封面图</span>
            </div>
          </div>
          <el-upload
            v-model:file-list="fileListCoursePeriod"
            action="#"
            :http-request="() => {}"
            list-type="picture-card"
            accept=".jpg,.jpeg,.png"
            :before-upload="beforeUploadCoursePeriod"
            :on-remove="handleCoursePeriodRemove"
            :on-preview="handleCoursePeriodPreview"
            :class="[
              {
                hideUploadBtn: fileListCoursePeriod.length >= 9 || openCourseImg
              },
              { 'course-sync-active': openCourseImg }
            ]"
          >
            <el-icon v-if="!openCourseImg"><Plus /></el-icon>
          </el-upload>
          <el-dialog v-model="dialogCoursePeriodVisible">
            <img
              class="w-full"
              :src="dialogCorsePeriodUrl"
              alt="Preview Image"
            >
          </el-dialog>
          <div class="img-text">{{ imgText }}</div>
        </div>
      </div>
      <el-divider content-position="left">课期命名设置</el-divider>
      <div class="period-name-setting">
        <div class="select-period">
          <div class="name">前缀</div>
          <el-select
            v-model="ruleForm.prefix"
            placeholder="请选择"
            style="width: 200px"
          >
            <el-option
              v-for="item in periodPrefixOptions"
              :key="item.value"
              :label="item.label"
              :value="item.label"
            />
          </el-select>
        </div>
        <div class="select-period">
          <div class="name">后缀</div>
          <el-select
            v-model="ruleForm.suffix"
            placeholder="请选择"
            style="width: 200px"
          >
            <el-option
              v-for="item in periodSuffixOptions"
              :key="item.value"
              :label="item.label"
              :value="item.label"
            />
          </el-select>
        </div>
        <div class="select-period">
          <div class="name">课期名示意：</div>
          <div class="text">{{ periodName }}</div>
        </div>
      </div>
      <el-divider content-position="left">课期开课时间</el-divider>
      <div class="lesson-open">
        <el-button type="primary" @click="addDAte">添加日期</el-button>
        <!-- 弹窗 -->
        <el-dialog
          v-model="dialogVisiblesc"
          title="选择日期"
          width="700px"
          :close-on-click-modal="false"
          :show-close="false"
        >
          <!-- 日历组件 -->
          <div class="chooseDates">
            <div class="demo-container">
              <el-calendar v-model="currentDatesed">
                <template #date-cell="{ data }">
                  <el-button
                    link
                    :disabled="disabledDate(data.day)"
                    class="cell-content"
                    :class="{ 'selected-date': isSelected(data.day) }"
                    @click.stop="toggleDate(data.day)"
                  >
                    {{ data.day.split("-")?.slice(2)?.join("-") }}
                    <span v-if="isSelected(data.day)" class="checkmark">✓</span>
                  </el-button>
                </template>
              </el-calendar>
            </div>
            <span style="font-weight: bold"> 已添加的日期 </span>
            <div class="selected-dates">{{ formattedDates }}</div>
            <div class="btns">
              <el-button @click="handleCancle">取消</el-button>
              <el-button type="primary" @click="setChooseDate">
                确定
              </el-button>
            </div>
          </div>
        </el-dialog>
        <div v-if="tableData?.length" class="tables">
          <el-table :data="tableData" :style="{ width: tableWidth }">
            <el-table-column prop="openTime" label="开课日期" min-width="100">
              <template #default="scope">
                {{ scope.row.openTime || "--" }}
              </template>
            </el-table-column>
            <el-table-column prop="weekDay" label="星期" align="center">
              <template #default="scope">
                <div>
                  {{ scope.row.weekDay || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="classStart"
              label="开课时间"
              align="left"
              min-width="220"
            >
              <template #default="scope">
                <div class="time_picker">
                  <el-time-picker
                    v-model="scope.row.classStart"
                    placeholder="请选择时间"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column
              width="180px"
              prop="signUpDeadline"
              label="提前关闭报名时间"
            >
              <template #default="scope">
                <div style="display: flex; align-items: center">
                  <el-input-number
                    v-model="scope.row.signUpDeadline"
                    :min="0"
                    :step="0.5"
                    :precision="1"
                    step-strictly
                    @change="handleChange"
                  />
                  <span class="hour">小时</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="left"
              min-width="140"
            >
              <template #default="scope">
                <div style="display: flex; align-items: center">
                  <div class="btnse" @click="getPaste(scope.$index)">粘贴</div>
                  <div class="btnse" @click="getCopy(scope.row)">复制</div>
                  <div class="btnse c_red" @click="getDelted(scope.$index)">
                    删除
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div class="buttons">
      <el-dropdown>
        <el-button style="margin-right: 10px">退出</el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item
              v-for="it in btnData"
              :key="it.id"
              @click="dropdownBtn(it)"
            >
              {{ it.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <el-button
        type="primary"
        class="create"
        :loading="saveLoading"
        @click="saveDraft"
      >
        {{ "保存草稿" }}
      </el-button>
      <el-button
        type="primary"
        class="create"
        :loading="submitLoading"
        @click="submitForm"
      >
        {{ "下一步" }}
      </el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.base-info {
  width: 100%;
  height: 100%;
  position: relative;
  .base-container {
    width: 100%;
    height: calc(100% - 50px);
    overflow-y: auto;
  }
  .course-info {
    display: flex;
    width: 100%;
    .course-des {
      width: 50%;
      margin-right: 20px;
    }
  }
  .course-period-info {
    display: flex;
    width: 100%;
    .course-des {
      width: 50%;
      margin-right: 20px;
    }
  }
  .course-form {
    margin-left: 55px;
  }
  .period-form {
    margin-left: 68px;
  }
  .period-name-setting {
    width: 60%;
    display: flex;
    justify-content: space-between;
    white-space: nowrap;
    .select-period {
      display: flex;
      align-items: center;
      .name {
        margin-right: 10px;
      }
    }
  }
  .star {
    margin-right: 3px;
    color: red;
  }
  .buttons {
    display: flex;
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
  }
  .range-input {
    display: flex;
    align-items: center;
  }
  .separator {
    margin: 0 10px;
    color: #606266;
  }
  .course-img {
    width: 50%;
  }
  .upload {
    width: 100%;
    display: flex;
    justify-content: space-between;
    .switch {
      display: flex;
      align-items: center;
    }
  }
  .img-title {
    margin-bottom: 10px;
  }
  .img {
    height: 68px;
    width: 110px;
    flex-shrink: 0;
    transition:
      opacity 0.3s ease,
      width 0.3s ease,
      margin-left 0.3s ease;
    border-radius: 10px;
    border: 1px solid #dcdfe6;
    .img-pic {
      width: 100%;
      height: 100%;
      border-radius: 5px;
      object-fit: cover;
    }
  }
}
:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 110px;
  height: 68px;
}
:deep(.el-upload--picture-card) {
  height: 68px;
  width: 110px;
}
.chooseDates {
  padding: 30px;
  box-sizing: border-box;
  background-color: #fff;
  height: 90%;
}
.demo-container {
  max-width: 100%;
  background-color: #ccc;
  box-shadow: 0px 4px 13px 0px #ccc;
  margin-bottom: 20px;
}
.cell-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s;
}
/* 修改选中样式为勾选标志 */
.selected-date {
  color: #409eff;
  font-weight: bold;
}
.checkmark {
  position: absolute;
  right: 2px;
  bottom: 2px;
  font-size: 12px;
  color: #67c23a;
  font-weight: bold;
}
.selected-dates {
  margin-top: 20px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  white-space: pre-wrap; /* 允许空格换行 */
  font-weight: bold;
  min-height: 44px;
}
:deep(.el-calendar-table .el-calendar-day) {
  height: 50px;
}
.btnse {
  color: #409eff;
  cursor: pointer;
  min-width: fit-content;
  font-weight: bold;
  margin-right: 15px;
}
.c_red {
  color: #f56c6c;
}
//滚动条的宽度
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: #e4e4e4;
  border-radius: 3px;
}
::-webkit-scrollbar-thumb {
  background-color: #eee;
  border-radius: 3px;
}
.date_picker {
  width: 200px;
}
.hour {
  min-width: fit-content;
  margin-left: 10px;
}
.btns {
  margin-top: 20px;
  width: 100%;
  display: flex;
  justify-content: flex-end;
}
:deep(.el-form-item__label) {
  color: #303133;
  font-weight: normal;
}
.img-text {
  font-size: 12px;
  color: #8c939d;
}
:deep(.el-button:focus-visible) {
  display: none;
}
:deep(.hideUploadBtn .el-upload--picture-card) {
  display: none;
}
:deep(.course-sync-active .el-upload-list__item .el-upload-list__item-delete) {
  display: none !important; /* 隐藏删除按钮 */
}
</style>
