<script setup>
import {
  onMounted,
  ref,
  onActivated,
  nextTick,
  onUnmounted,
  onDeactivated
} from "vue";
import { formatTime } from "@/utils/index";
import {
  courseFindAll,
  courseIsFreeze,
  findAllCourseType
} from "@/api/course.js";
// import { courseTypeFindAllNotPage } from "@/api/courseClassify.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";

defineOptions({
  name: "CourseManagementIndex"
});
onActivated(() => {
  getTableList();
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});
onMounted(() => {
  courseTypeFindApi();
  getTableList();
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});

onUnmounted(() => {
  window.removeEventListener("resize", calculateTableHeight);
});

onDeactivated(() => {
  window.removeEventListener("resize", calculateTableHeight);
});

const router = useRouter();

// 计算表格高度
const searchFormHeight = ref(0);
const tableHeight = ref("88vh"); // 初始默认值，会被 calculateTableHeight 覆盖

const calculateTableHeight = async () => {
  await nextTick();
  const searchForm = document.querySelector(".search .con_search");
  if (searchForm) {
    searchFormHeight.value = searchForm.offsetHeight;
    if (isSelection.value) {
      tableHeight.value = `calc(88vh - 140px - ${searchFormHeight.value}px - 125px)`;
    } else {
      tableHeight.value = `calc(88vh - 140px - ${searchFormHeight.value}px - 100px)`;
    }
  }
};

// 搜索表单
const form = ref({
  startTime: "",
  endTime: "",
  name: "",
  organizationName: "",
  courseTypeId: "",
  freeze: 0
});
// 课程状态选项
const stateOptions = ref([
  {
    name: "全部",
    id: 0
  },
  {
    name: "正常",
    id: 1
  },
  {
    name: "冻结",
    id: 2
  }
]);
// 表格数据
const tableData = ref([]);
const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});
const courseTypeoptions = ref([{ value: 0, label: "全部" }]);
const courseTypeFindApi = async () => {
  let [err, res] = await requestTo(findAllCourseType());
  // console.log("🐠res-----------3333------------------->", res);
  if (res) {
    courseTypeoptions.value = courseTypeoptions.value.concat(
      transformArray(res)
    );
  }
};
function transformArray(inputArray) {
  return inputArray?.map(item => {
    const newItem = {
      value: item.id,
      label: item.name
    };

    // 如果有 children，则递归转换
    if (item.children && item.children.length > 0) {
      newItem.children = transformArray(item.children);
    }

    return newItem;
  });
}
const courseTypeChange = val => {
  form.value.courseTypeId = val[val.length - 1];
};
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  // console.log("🍧-----paramsData-----", paramsData);
  if (paramsData.freeze && form.value.freeze === 1) {
    paramsData.freeze = false;
  } else if (paramsData.freeze && form.value.freeze === 2) {
    paramsData.freeze = true;
  }
  const [err, result] = await requestTo(courseFindAll(paramsData));
  // console.log("🎁-----result-----", result);
  if (result) {
    tableData.value = result?.content;

    params.value.totalElements = result.totalElements;
    await nextTick();
    calculateTableHeight();
  } else {
    ElMessage.error(err);
  }
  getListLoading.value = false;
};
//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTableList();
};
const handleSizeChange = val => {
  params.value.page = 1;
  params.value.size = val;
  getTableList();
};
//搜索
const searchData = () => {
  params.value.page = 1;
  getTableList();
};
// 重置
const setData = () => {
  form.value = {};
  params.value.page = 1;
  value1.value = [];
  getTableList();
};
// 清除数据
const clearEvt = val => {
  if (val === "name") {
    form.value.name = "";
  } else if (val === "time") {
    form.value.startTime = "";
    form.value.endTime = "";
  } else if (val === "organizationName") {
    form.value.organizationName = "";
  } else if (val === "courseTypeId") {
    form.value.courseTypeId = "";
  }
  // params.value.page = 1;
  getTableList();
};
// 选择时间
const timeChange = value => {
  if (value) {
    form.value.startTime = new Date(value[0])?.getTime();
    // form.value.endTime = new Date(value[1])?.getTime();
    const endDate = new Date(value[1]);
    endDate.setHours(23, 59, 59, 999); // 关键修改
    form.value.endTime = endDate.getTime();
  }
};

const value1 = ref([]);
const activeName = ref(0);
const tabTitle = ref([
  { id: 0, name: "评价审核" },
  { id: 1, name: "回复审核" }
]);
// 切换tab
const handleClick = (item, index) => {
  activeName.value = item.props.name;
  // console.log(
  //   "🌵 activeName.value------------------------------>",
  //   activeName.value
  // );
  let obj = {
    index: item.props.name,
    name: item.props.label
  };
};
const activeOption = ref(2);
const tabTitleOption = ref([
  { id: 2, name: "待处理" },
  { id: 3, name: "已通过" },
  { id: 4, name: "已驳回" }
]);
// 切换tab
const handleClickOption = (item, index) => {
  activeOption.value = item.props.name;
  // console.log(
  //   "🌵 activeName.value------------------------------>",
  //   activeName.value
  // );
  let obj = {
    index: item.props.name,
    name: item.props.label
  };
};
const isSelection = ref(false);
const handleBatchAudit = () => {
  isSelection.value = true;
  if (isSelection.value) {
    tableHeight.value = `calc(88vh - 140px - ${searchFormHeight.value}px - 125px)`;
  } else {
    tableHeight.value = `calc(88vh - 140px - ${searchFormHeight.value}px - 100px)`;
  }
  console.log("💗handleBatchAudit---------->");
};
const getRowKeys = row => {
  return row.id;
};
const batchDeleteArr = ref([]);
const handleSelectionChange = val => {
  batchDeleteArr.value = [];
  ids.value = [];
  course.value = [];
  if (!val.length) return;
  if (val.length > 0) {
    val.forEach(item => {
      batchDeleteArr.value.push(item);
    });
  } else {
    batchDeleteArr.value = val[0];
  }
};
</script>

<template>
  <div class="containers">
    <div class="search">
      <div class="con_search">
        <el-form :model="form" :inline="true">
          <el-form-item label="创建时间">
            <el-date-picker
              v-model.trim="value1"
              type="daterange"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              @change="timeChange"
              @clear="clearEvt('time')"
            />
          </el-form-item>
          <el-form-item label="课程名">
            <el-input
              v-model.trim="form.name"
              placeholder="请输入"
              style="width: 180px"
              clearable
              @clear="clearEvt('name')"
            />
          </el-form-item>
          <el-form-item label="机构">
            <el-input
              v-model.trim="form.organizationName"
              placeholder="请输入"
              style="width: 180px"
              clearable
              @clear="clearEvt('organizationName')"
            />
          </el-form-item>
          <el-form-item label="课程类型">
            <el-cascader
              v-model.trim="form.courseTypeId"
              :options="courseTypeoptions"
              :show-all-levels="false"
              @change="courseTypeChange"
              @clear="clearEvt('courseTypeId')"
            />
          </el-form-item>
          <el-form-item label="课程状态">
            <el-select
              v-model="form.freeze"
              style="width: 120px"
              placeholder="请选择"
              value-key="id"
            >
              <el-option
                v-for="item in stateOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label=" ">
            <div class="flex">
              <el-button type="primary" @click="searchData">搜索</el-button>
              <el-button @click="setData">重置</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <div class="main">
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane
          v-for="(item, index) in tabTitle"
          :key="index"
          :label="item.name"
          :name="index"
        />
      </el-tabs>
      <div class="tab-line">
        <el-tabs
          v-model="activeOption"
          class="demo-tabs"
          @tab-click="handleClickOption"
        >
          <el-tab-pane
            v-for="(item, index) in tabTitleOption"
            :key="index"
            :label="item.name"
            :name="index"
          />
        </el-tabs>
      </div>
      <div>
        <el-button
          type="primary"
          style="margin-bottom: 10px"
          @click="handleBatchAudit"
          >
批量审核
</el-button>
      </div>

      <el-scrollbar class="scrollbar" :style="{ height: tableHeight }">
        <el-table
          :data="tableData"
          :header-cell-style="{
            backgroundColor: '#fafafa',
            color: '#565353'
          }"
          table-layout="fixed"
          :max-height="tableHeight"
          :row-key="getRowKeys"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            v-if="isSelection === true"
            :selectable="selectable"
            :reserve-selection="true"
            type="selection"
            width="55"
          />
          <el-table-column
            prop="name"
            label="评价人"
            width="300"
            align="left"
            show-overflow-tooltip
            fixed
          >
            <template #default="scope">
              {{ scope.row.name || "--" }}
            </template>
          </el-table-column>
          <el-table-column
            prop="rant"
            label="分数"
            align="left"
            width="300"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{ scope.row.rant || "--" }}
            </template>
          </el-table-column>
          <el-table-column prop="termNumber" label="评价内容" align="left">
            <template #default="scope">
              <div>
                {{ scope.row.termNumber || 0 }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            width="250"
            prop="createdAt"
            label="评价时间"
            align="left"
          >
            <template #default="scope">
              <div>
                {{
                  formatTime(scope.row?.createdAt, "YYYY-MM-DD HH:mm:ss") ||
                  "--"
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            align="left"
            width="200px"
          >
            <template #default="{ row }">
              <el-button
                type="primary"
                link
                @click="
                  router.push({
                    path: '/invitation/details',
                    query: { type: 'invite', id: row.teacherId }
                  })
                "
              >
                通过
              </el-button>
              <el-button
                type="danger"
                link
                @click="
                  router.push({
                    path: '/course/current/detail',
                    query: { periodId: row.coursePeriodId }
                  })
                "
              >
                驳回
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-scrollbar>
      <div v-if="isSelection === true" class="examine-btn">
        <el-button type="primary" @click="approve">通过</el-button>
        <el-button type="danger" @click="approve">驳回</el-button>
      </div>
      <div class="con_pagination">
        <!-- 分页 -->
        <el-pagination
          v-model:current-page="params.page"
          v-model:page-size="params.size"
          :class="isSelection === true ? 'con_pagination-big' : 'pagination'"
          background
          :page-sizes="[15, 30, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="params.totalElements"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.scrollbar {
  // padding-top: 20px;
  // padding: 20px;
  // height: calc(100vh - 181px);
  // height: calc(100vh - 329px);

  background-color: #fff;
}
// :deep(.el-table .cell) {
//   padding: 0;
// }
.containers {
  display: flex;
  flex-direction: column;
  height: 88vh; /* Or adjust based on your global layout, e.g., calc(100vh - GlobalHeaderHeight) */
  overflow: hidden;
  box-sizing: border-box;
  .search {
    box-sizing: border-box;
    padding: 20px 20px 2px 20px;
    background-color: #fff;
    margin-bottom: 20px;
    .con_search {
      display: flex;
      align-items: center;
      width: 100%;
      height: fit-content;
      // .input_width {
      //   width: 200px;
      // }
    }
  }

  .main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* Prevents .main from expanding due to its own content */
    background: #fff;
    padding: 20px 20px 0 20px;
    box-sizing: border-box;
    // height: 100%; /* Removed as height is now controlled by flex:1 */
  }

  .con_pagination {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    margin-top: 15px;
  }
  //   .con_pagination-big {
  //     display: flex;
  //     justify-content: flex-end;
  //     width: 100%;
  //     // margin-top: 15px;
  //   }
}
.tab-line {
  :deep(.el-tabs__active-bar) {
    background-color: transparent;
  }
}
:deep(.el-tabs__nav-wrap:after) {
  background-color: transparent;
}
:deep(.el-tabs) {
  --el-tabs-header-height: 20px;
}
</style>
