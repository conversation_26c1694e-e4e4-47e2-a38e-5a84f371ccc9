<script setup>
import { ref, onMounted, reactive, onBeforeMount, onBeforeUnmount } from "vue";
import { useRouter, useRoute } from "vue-router";
import RichEditor from "@/components/Base/RichEditor.vue";
import { complexFindById } from "@/api/institution";
import { ElMessage } from "element-plus";
import { formatTime } from "@/utils/index";
import { Hide, View } from "@element-plus/icons-vue";
import { decrypt, encryption } from "@/utils/SM4.js";

const router = useRouter();
const route = useRoute();
const form = ref({});

const formRef = ref(null);
const richFlag = ref(false);
const adminId = ref(null);
onMounted(() => {
  adminId.value = route.query.id;
  getData();
  richFlag.value = true;
});

const formData = ref([
  {
    label: "基地名称",
    type: "text",
    prop: "name",
    check: true,
    placeholder: "请输入机构名称",
    width: "200px"
  },
  {
    label: "机构",
    type: "text",
    // check: true,
    prop: "organizationName",
    placeholder: "请输入机构别名",
    width: "200px"
  },
  {
    label: "创建时间",
    type: "text",
    // check: true,
    prop: "createdAt",
    placeholder: "请输入机构别名",
    width: "200px",
    rowspan: 2
  },
  {
    label: "详细地址",
    type: "text",
    // check: true,
    prop: "detailedAddress",
    placeholder: "请输入机构别名",
    width: "200px",
    rowspan: 2
  },
  {
    label: "基地联系人",
    type: "text",
    // check: true,
    prop: "emergencyPeople",
    placeholder: "请输入客服热线",
    width: "200px"
  },
  {
    label: "基地联系电话",
    type: "text",
    // check: true,
    prop: "emergencyPhone",
    placeholder: "请输入客服热线",
    width: "200px"
  },
  {
    label: "基地介绍",
    type: "editor",
    // check: true,
    prop: "introduction",
    placeholder: "",
    width: "200px"
  }
]);
const newData = ref();

// 根据id查询
const getData = async () => {
  let params = { id: route.query.id };
  try {
    const { code, data, msg } = await complexFindById(params);
    // console.log("🎁-----data-----", code, data, msg);
    if (code == 200 && data) {
      form.value = data;
      newData.value = JSON.parse(JSON.stringify(data));
    }
  } catch (error) {
    console.log("catch-----error-----", error);
  }
};
// 编辑
const edit = () => {
  router.push({
    path: "/institution/baseEdit",
    query: { id: adminId.value }
  });
};
//返回上一页
const cancel = () => {
  router.go(-1);
};
const isView = ref(true);
const isViewFn = () => {
  isView.value = !isView.value;
  if (isView.value) {
    form.value.emergencyPhone = newData.value.emergencyPhone;
  } else {
    form.value.emergencyPhone = decrypt(newData.value.emergencyPhoneCt);
  }
};
</script>

<template>
  <div class="main">
    <el-scrollbar class="scrollbar">
      <div>
        <!-- <el-form ref="formRef" :model="form" :rules="rules"> -->
        <el-descriptions title="" :column="2" border :label-width="'200px'">
          <el-descriptions-item
            v-for="(item, index) in formData"
            :key="index"
            label-align="center"
            label-class-name="my-label"
            :span="item.rowspan || 1"
          >
            <template #label>
              {{ item.label }}
            </template>
            <el-form-item
              :prop="item.prop"
              :inline-message="item.check"
              style="margin-bottom: 0"
              :show-message="true"
              error-placement="right"
            >
              <!-- text -->
              <template v-if="item.type === 'text'">
                <div
                  style=""
                  :class="{
                    cell_item: item.prop === 'emergencyPhone' && form[item.prop]
                  }"
                >
                  {{
                    item.prop === "createdAt"
                      ? formatTime(form[item.prop], "YYYY-MM-DD HH:mm:ss")
                      : form[item.prop] || "--"
                  }}
                  <span
                    v-if="item.prop === 'emergencyPhone' && form[item.prop]"
                    class="icon"
                  >
                    <el-icon
                      v-if="isView"
                      style="cursor: pointer"
                      @click="isViewFn"
                    >
                      <Hide />
                    </el-icon>
                    <el-icon v-else style="cursor: pointer" @click="isViewFn">
                      <View />
                    </el-icon>
                  </span>
                </div>
              </template>
              <!-- 富文本 -->
              <template v-else-if="item.type === 'editor'">
                <div style="width: 100%">
                  <RichEditor
                    v-model="form[item.prop]"
                    height="200px"
                    :isOpen="false"
                    :readOnly="true"
                  />
                </div>
              </template>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-scrollbar>

    <div class="account_management">
      <el-button @click="cancel">返回</el-button>
      <el-button type="primary" @click="edit">编辑信息</el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.scrollbar {
  // padding-top: 20px;
  // padding: 20px;
  // padding-bottom: 0px;

  height: calc(100vh - 190px);
  background-color: #fff;
}
.main {
  padding: 20px;
  background: #fff;
  .cell_item {
    display: flex;
    min-width: 110px;
    /* margin-left: 20px; */
    justify-content: space-around;
    align-items: center;
    .icon {
      margin-left: 10px;
    }
  }
}

.header {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 20px;

  .title_left {
    display: flex;

    :nth-child(2) {
      margin-left: 40px;
    }
  }

  .title_rigth {
    display: flex;

    :nth-child(2) {
      margin-left: 40px;
    }
  }
}

.account_management {
  margin: 20px 0 0 0px;
  display: flex;
  justify-content: flex-end;
  :nth-child(2) {
    margin-left: 20px;
  }
}

:deep(.my-label) {
  background: #e1f5ff !important;
}
</style>
