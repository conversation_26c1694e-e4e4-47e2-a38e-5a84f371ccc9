<script setup>
import {
  ref,
  onMounted,
  computed,
  watchEffect,
  nextTick,
  onActivated
} from "vue";
import { useRouter, useRoute } from "vue-router";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  ledeterList,
  ledeterinfoList,
  isFreeze
} from "@/api/leaderLecturer.js";
import { formatTime } from "@/utils/index";
import { findAllCourseType } from "@/api/course.js";
import Eye from "@/components/Base/eyes.vue";
import { decrypt, encryption } from "@/utils/SM4.js";

defineOptions({
  name: "TeacherDetails"
});
const router = useRouter();
const route = useRoute();
onMounted(() => {
  getTableList(); //头部表格
  getListbottom(); //底部表单
  getClasscour(); //获取课程分类
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});

onActivated(() => {
  getTableList(); //头部表格
  getListbottom(); //底部表单
  getClasscour(); //获取课程分类
  calculateTableHeight();
});

// 计算表格高度
const searchFormHeight = ref(0);
const tabHeastyleHeight = ref(0);
const tableHeight = ref("calc(100vh - 310px)");

const calculateTableHeight = async () => {
  await nextTick();
  const searchForm = document.querySelector(".con_search");
  const tabHeastyle = document.querySelector(".tabHeastyle");
  if (searchForm || tabHeastyle) {
    searchFormHeight.value = searchForm.offsetHeight;
    tabHeastyleHeight.value = tabHeastyle.offsetHeight;
    // 动态计算表格高度，减去表单高度、页面其他元素高度和边距
    tableHeight.value = `calc(100vh - 308px - ${searchFormHeight.value}px - ${tabHeastyleHeight.value}px)`;
  }
};

// 表头
const tableHeader = ref([
  { label: "机构", value: "", width: "11%", key: "organizationName" },
  { label: "", value: "", width: "11%", key: "id" },
  { label: "创建时间", value: "", width: "11%", key: "createdAt" },
  { label: "姓名", value: "", width: "11%", key: "name" },
  { label: "账号", value: "", width: "11%", key: "account" },
  { label: "身份证号", value: "", width: "11%", key: "idNumber", valueCt: "" },
  { label: "手机号", value: "", width: "11%", key: "phone", valueCt: "" },
  { label: "邮箱", value: "", width: "11%", key: "email" },
  { label: "微信绑定", value: "", width: "11%", key: "" }
]);
watchEffect(() => {
  const title = route.query.title;
  // console.log("🍭-----title-----", title);
  tableHeader.value[1].label = title === "ldMang" ? "领队ID" : "讲师ID";
});
// 冻结状态
const isFrozen = ref(false);
const isFrozenLoading = ref(false);

// 领队讲师详情
const getTableList = async data => {
  const [err, result] = await requestTo(ledeterList({ id: route.query?.id }));
  if (err) {
    console.error("获取数据失败:", err);
    return;
  }

  // 获取冻结状态
  isFrozen.value = result?.freeze || false;

  tableHeader.value[0].value = result?.organizationName || "--";
  tableHeader.value[1].value = result?.id || "--";
  tableHeader.value[2].value = result?.createdAt
    ? formatTime(result?.createdAt)
    : "--";
  tableHeader.value[3].value = result?.name || "--";
  tableHeader.value[4].value = result?.account || "--";
  tableHeader.value[5].value = decrypt(result?.idNumberCt) || "--";
  tableHeader.value[5].valueCt = result?.idNumber || "--";
  tableHeader.value[6].value = decrypt(result?.phoneCt) || "--";
  tableHeader.value[6].valueCt = result?.phone || "--";
  tableHeader.value[7].value = result?.email || "--";
  tableHeader.value[8].value = result?.isBindWx ? "是" : "否";

  console.log("🌵账号管理详情", tableHeader.value);
};
// 表格
const tableData = ref([
  // {
  //   id: 0,
  //   createdAt: 0,
  //   updatedAt: 0,
  //   name: "123",
  //   organizationName: "dd ",
  //   courseTypeName: "fff",
  //   termNumber: 0
  // }
]);
// 查询分类课程
const getClasscour = async () => {
  const [err, result] = await requestTo(findAllCourseType({}));
  Tablist.value.forEach(item => {
    if (item.prop === "courseTypeId") {
      item.option = result;
    }
  });
};
const props = {
  label: "name",
  value: "id",
  children: "children"
};
//搜索
const Tablist = ref([
  {
    tab: 1,
    name: "创建时间",
    textareavalue: [],
    prop: ["startTime", "endTime"],
    type: "daterange",
    disabled: true,
    width: "310px"
  },
  {
    tab: 2,
    name: "课程名",
    type: "input",
    placeholder: "请输入",
    textareavalue: "",
    prop: "courseName",
    width: "120px"
  },
  // {
  //   tab: 3,
  //   name: "课程类型",
  //   type: "selectvalue",
  //   selectvalue: "",
  //   width: "200px",
  //   prop: "buyType",
  //   option: []
  // },
  {
    tab: 3,
    name: "课程类型",
    type: "cascader",
    width: "200px",
    prop: "courseTypeId",
    option: [],
    textareavalue: []
  },
  {
    tab: 4,
    name: "课程状态",
    type: "selectvalue",
    placeholder: "请输入",
    textareavalue: "",
    prop: "coursePeriodState",
    width: "200px",
    option: [
      {
        label: "全部",
        value: "all"
      },
      {
        value: "ONLINE",
        label: "上架"
      },
      {
        value: "NOT_LISTED",
        label: "未上架"
      },
      {
        value: "ONLINE_UNDER_REVIEW",
        label: "上架审核"
      },
      {
        value: "OFFLINE",
        label: "下架"
      },
      {
        value: "OFFLINE_UNDER_REVIEW",
        label: "下架审核"
      },
      {
        value: "COMPLETED",
        label: "已完成"
      }
    ]
  }
  // {
  //   tab: 5,
  //   name: "数量",
  //   textareavalue: "22",
  //   // prop: "amount",
  //   type: "input",
  //   width: "120px",
  //   ignore: true,
  //   disabled: true
  // }
]);
const form = ref({
  startTime: null,
  endTime: null
});
const timeChange = async e => {
  form.value.startTime = e ? new Date(e[0])?.getTime() : "";
  form.value.endTime = e ? new Date(e[1])?.getTime() : "";
  await nextTick();
  calculateTableHeight();
};
//搜索
const searchData = () => {
  params.value.page = 1;
  getListbottom();
};
// 重置
const setData = () => {
  // params.value.page = 1;
  // getTableList();
  Tablist.value.forEach(item => {
    if (item.type === "selectvalue") {
      item.selectvalue = ""; // 清空下拉框
    } else {
      item.textareavalue = ""; // 清空输入框
    }
  });
};

// 领队讲师/课期
const getListLoading = ref(false);
const getListbottom = async data => {
  const titleMap = {
    jsMang: "lecturersId",
    ldMang: "leadersId"
  };
  const dynamicKey = titleMap[route.query.title];
  const dynamicParams = dynamicKey ? { [dynamicKey]: route.query.id } : {};
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort,
    ...dynamicParams
  };
  Tablist.value.forEach(item => {
    if (!item.prop) return;
    // if (!item.prop || item.prop === "amount") return;
    if (item.type === "input" || item.type === "selectvalue") {
      if (item.textareavalue || item.selectvalue) {
        paramsData[item.prop] = item.textareavalue || item.selectvalue;
      }
    } else if (
      item.type === "daterange" &&
      Array.isArray(item.textareavalue) &&
      item.textareavalue.length === 2
    ) {
      const [startKey, endKey] = item.prop;
      paramsData[startKey] = new Date(item.textareavalue[0])?.getTime();
      paramsData[endKey] = new Date(item.textareavalue[1])?.getTime();
    }
    if (item.prop === "courseTypeId" && item.textareavalue) {
      console.log("🍪-----item.textareavalue-----", item.textareavalue);
      const lastSelectedId = item.textareavalue[item.textareavalue.length - 1];
      if (lastSelectedId) {
        paramsData[item.prop] = lastSelectedId;
      }
    }
  });
  // console.log("🍧-课期----paramsData-----", paramsData);
  // return;
  const [err, result] = await requestTo(ledeterinfoList(paramsData));
  // console.log("🎁-----result-----", result);
  if (result) {
    tableData.value = result?.content;
    params.value.totalElements = result.totalElements;

    await nextTick();
    calculateTableHeight();
  } else {
    ElMessage.error(err);
  }
  getListLoading.value = false;
};
//分页
const params = ref({
  page: 1,
  size: 10,
  sort: "createdAt,desc",
  totalElements: 0
});
const handleCurrentChange = e => {
  params.value.page = e;
  getListbottom();
};
// 讲师详情or领队详情
const pageTitle = computed(() => {
  const titleMap = {
    jsMang: "讲师详情",
    ldMang: "领队详情"
  };
  return titleMap[route.query.title] || "";
});
// 资质文件
const turnfile = () => {
  if (pageTitle.value === "领队详情") {
    router.push({
      path: "/accountNumber/components/qualificationsFile",
      query: { id: route.query?.id }
    });
  } else {
    router.push({
      path: "/lecturerNumber/components/qualificationsFile",
      query: { id: route.query?.id }
    });
  }
};
// 编辑信息
const editinfo = () => {
  if (pageTitle.value === "领队详情") {
    router.push({
      path: "/accountNumber/components/editInformation",
      query: { id: route.query?.id, title: route.query?.title }
    });
  } else {
    router.push({
      path: "/lecturerNumber/components/editInformation",
      query: { id: route.query?.id, title: route.query?.title }
    });
  }
};
// 课程状态
const stateOptions = [
  {
    value: "all",
    label: "全部"
  },
  {
    value: "ONLINE",
    label: "上架"
  },
  {
    value: "NOT_LISTED",
    label: "未上架"
  },
  {
    value: "ONLINE_UNDER_REVIEW",
    label: "上架审核"
  },
  {
    value: "OFFLINE",
    label: "下架"
  },
  {
    value: "OFFLINE_UNDER_REVIEW",
    label: "下架审核"
  },
  {
    value: "COMPLETED",
    label: "已完成"
  }
];
// 获取课程状态
const getSatte = val => {
  let res = "";
  stateOptions?.map(item => {
    if (item.value === val) {
      res = item.label;
    }
  });
  return res;
};
const getInfoid = item => {
  // console.log(item.id);
  // periodId：34（课期id）
  // return;
  router.push({
    path: "/course/management/current/details",
    query: { periodId: item.id, text: "course" }
  });
};

// 获取冻结/解冻按钮文本
const getButtonText = isFrozen => {
  return isFrozen ? "解冻" : "冻结";
};

// 冻结/解冻操作
const isSubmitting = ref(false);
const handleFreeze = async () => {
  const willFreeze = !isFrozen.value;
  const confirmText = willFreeze
    ? "你确定要冻结该账户吗?"
    : "你确定要解冻该账户吗?";
  const confirmTitle = willFreeze ? "确认冻结" : "确认解冻";
  const successMessage = willFreeze ? "已冻结" : "已解冻";

  try {
    await ElMessageBox.confirm(confirmText, confirmTitle, {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: ""
    });

    // 如果已经在提交中，则不再重复发起请求
    if (isSubmitting.value) return;
    isSubmitting.value = true;
    isFrozenLoading.value = true;

    const params = {
      id: route.query?.id,
      freeze: willFreeze
    };
    const operateLog = {
      operateLogType:
        route.query.title === "ldMang"
          ? "LEADER_MANAGEMENT"
          : "LECTURER_MANAGEMENT",
      operateType: willFreeze ? "冻结了" : "解冻了",
      operatorTarget: tableHeader.value[3].value
    };
    const { code, msg } = await isFreeze(params, operateLog);
    if (code === 200) {
      isFrozenLoading.value = false;
      ElMessage({
        message: successMessage,
        type: "success"
      });
      isFrozen.value = willFreeze;
    } else {
      ElMessage.error(msg);
    }
  } catch (error) {
    // 操作取消
  } finally {
    // 无论请求成功或失败，都重置提交状态
    isSubmitting.value = false;
    isFrozenLoading.value = false;
  }
};
</script>

<template>
  <div class="containers">
    <div class="content_top">
      <!-- <div class="con_top">
        <div class="titles">{{ pageTitle }}</div>
      </div> -->
      <div class="tabHeastyle">
        <el-descriptions
          title=""
          :column="3"
          border
          :style="{ flex: 1, minWidth: '0' }"
        >
          <template v-for="(item, index) in tableHeader" :key="index">
            <el-descriptions-item label-align="center">
              <template #label>
                <div class="cell-item">{{ item.label }}</div>
              </template>
              <Eye
                v-if="
                  (item.label === '身份证号' || item.label === '手机号') &&
                  item.value !== '--'
                "
                :data="item.value"
                :dataCt="item.valueCt"
                :label="item.label"
              />
              <div v-else>{{ item.value }}</div>
            </el-descriptions-item>
          </template>
        </el-descriptions>
        <div class="tabtn">
          <div class="btn-row">
            <el-text v-if="isFrozen" type="danger" class="frozen-tip">
              本账号已冻结
            </el-text>
            <el-button
              type="primary"
              plain
              style="width: 100px"
              :loading="isFrozenLoading"
              @click="handleFreeze"
            >
              {{ getButtonText(isFrozen) }}
            </el-button>
          </div>
          <div class="btn-row">
            <el-button
              v-code="['620']"
              class="btn_style"
              type="primary"
              @click="turnfile"
            >
              资质文件
            </el-button>
            <el-button
              v-code="['621']"
              class="btn_style"
              type="primary"
              @click="editinfo"
            >
              编辑信息
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="content_bottom">
      <div class="con_search">
        <el-form :inline="true">
          <el-form-item
            v-for="item in Tablist"
            :key="item.tab"
            :label="item.name"
          >
            <el-date-picker
              v-if="item.type === 'daterange'"
              v-model="item.textareavalue"
              type="daterange"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              :style="{ width: item.width || '100%' }"
              @change="timeChange"
            />
            <el-input
              v-else-if="item.type === 'input'"
              v-model="item.textareavalue"
              :placeholder="item.placeholder"
              clearable
              :disabled="item.disabled"
              :style="{ width: item.width || '100%' }"
            />
            <el-cascader
              v-else-if="item.type === 'cascader'"
              v-model="item.textareavalue"
              :options="item.option"
              :props="props"
              :show-all-levels="false"
              clearable
            />
            <el-select
              v-else-if="item.type === 'selectvalue'"
              v-model="item.selectvalue"
              clearable
              :style="{ width: item.width || '100%' }"
            >
              <el-option
                v-for="option in item.option"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label=" ">
            <div class="btn_search">
              <el-button type="primary" @click="searchData">搜索</el-button>
              <el-button class="search" @click="setData">重置</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="content_top">
      <el-scrollbar :style="{ height: tableHeight }">
        <div class="con_table">
          <el-table
            :data="tableData"
            table-layout="fixed"
            :header-cell-style="{
              backgroundColor: '#fafafa',
              color: '#565353'
            }"
            highlight-current-row
            :max-height="tableHeight"
          >
            <el-table-column prop="name" label="课程名" min-width="120">
              <template #default="scope">
                <el-text>
                  {{ scope.row.name || "--" }}
                </el-text>
              </template>
            </el-table-column>
            <el-table-column prop="termNumber" label="期号" align="left">
              <template #default="scope">
                <div>
                  {{ scope.row.termNumber || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="openTime" label="上课时间" align="left">
              <template #default="scope">
                <div>
                  {{
                    formatTime(scope.row.openTime, "YYYY-MM-DD HH:mm") || "--"
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="createdAt" label="创建时间" align="left">
              <template #default="scope">
                <div>
                  {{
                    formatTime(
                      scope.row.organization.createdAt,
                      "YYYY-MM-DD HH:mm"
                    ) || "--"
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="complex" label="基地" align="left">
              <template #default="scope">
                <div>
                  <div>
                    <!-- {{ scope.row.complex || "--" }} -->
                    {{ scope.row.complex.name || "--" }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="lecturers" label="讲师" align="left">
              <template #default="scope">
                <template v-if="scope.row.lecturers?.length">
                  <div
                    v-for="(item, index) in scope.row.lecturers"
                    :key="index"
                    style="display: inline-block"
                  >
                    {{ item.name || "--" }}
                    <span v-if="index !== scope.row.lecturers.length - 1">、</span>
                  </div>
                </template>
              </template>
            </el-table-column>
            <el-table-column
              width="200px"
              prop="coursePeriodState"
              label="课程状态"
            >
              <template #default="scope">
                <div>
                  {{ getSatte(scope.row.coursePeriodState) || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="address"
              fixed="right"
              label="操作"
              align="center"
            >
              <template #default="scope">
                <div class="operate">
                  <el-button type="primary" link @click="getInfoid(scope.row)">
                    详情
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-scrollbar>
      <div class="con_pagination">
        <!-- 分页 -->
        <el-pagination
          v-model:current-page="params.page"
          v-model:page-size="params.size"
          class="pagination"
          background
          layout="total, prev, pager, next, jumper"
          :total="params.totalElements"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.scrollbar {
  margin-bottom: 20px;
  height: calc(100vh - 480px);
  background-color: #fff;
}

.containers {
  box-sizing: border-box;
  width: calc(100% - 48px);
  height: 100%;
  // padding: 24px;
  background: #f0f2f5;

  .content_top {
    box-sizing: border-box;
    padding: 20px;
    background-color: #fff;

    .con_top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: fit-content;
      margin-bottom: 24px;

      .titles {
        font-size: 20px;
        font-weight: bold;
        color: #606266;
      }
    }

    .tabHeastyle {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: space-between;
      gap: 20px;

      @media (max-width: 1450px) {
        // flex-direction: column;
        align-items: flex-start;
      }

      .description-table {
        flex: 1;
        min-width: 300px;
        max-width: 1300px;
        width: 100%;
      }
    }

    .tabtn {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 80px;
      margin: 10px 0;

      @media (max-width: 1450px) {
        flex-direction: row;
        height: auto;
        width: 100%;
        gap: 20px;
      }

      @media (max-width: 768px) {
        flex-direction: column;
        height: auto;
        gap: 15px;
      }

      .btn-row {
        display: flex;
        align-items: center;
        gap: 10px;

        @media (max-width: 480px) {
          flex-direction: column;
          align-items: flex-start;
        }
      }

      .frozen-tip {
        margin-left: 8px;
        margin-right: 8px;
      }

      .btn_style {
        width: 100px;
      }
    }
  }

  .content_bottom {
    box-sizing: border-box;
    padding: 20px 20px 2px;
    margin: 20px 0px;
    background-color: #fff;
    // height: 74%;
  }

  :deep(.el-button + .el-button) {
    margin: 0;
  }
  // :deep(.el-button--primary) {
  //   width: 100px;
  // }
}

.con_search {
  display: flex;
  align-items: center;
  width: 100%;
  height: fit-content;
  // margin-top: 40px;

  .btn_search {
    display: flex;
    justify-content: space-between;
    width: 140px;
  }
}

:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}
// 搜索
.el-form--inline .el-form-item {
  margin-right: 20px;
}

// .con_table {
//   // width: calc(100% - 25px);
//   // min-height: 500px;
//   margin-bottom: 24px;
//   // margin-left: 25px;
// }

.con_pagination {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  margin-top: 20px;
}
</style>
