<script setup>
import { ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import RichEditor from "@/components/Base/RichEditor.vue";
import { complexSave } from "@/api/institution.js";
import { requestTo } from "@/utils/http/tool";
import { decrypt, encryption } from "@/utils/SM4.js";
import { courseStore } from "@/store/modules/course.js";
import { debounce } from "@iceywu/utils";
import Map from "./components/Map.vue";
import ImgPos from "@/assets/pos.png";
const useCourseStore = courseStore();
const router = useRouter();
const route = useRoute();

// 表单
const form = ref({
  name: "", //基地名称
  contacts: "",
  phoneNumber: "",
  introduction: "", //基地介绍
  detailedAddress: "", // 详细地址
  emergencyPeople: "", // 紧急联系人
  emergencyPhone: "", // 紧急联系人电话
  latitude: "",
  longitude: ""
});
// 提交
const formRef = ref(null);
const formData = ref([
  {
    label: "基地名称",
    type: "input",
    prop: "name",
    check: true,
    placeholder: "请输入基地名称",
    width: "400px",
    maxLength: 20
  },
  {
    label: "详细地址",
    type: "map",
    prop: "detailedAddress",
    check: true,
    placeholder: "请输入详细地址",
    width: "400px"
  },
  {
    label: "基地联系人",
    type: "input",
    prop: "emergencyPeople",
    // check: true,
    placeholder: "请输入基地联系人",
    width: "400px",
    maxLength: 10
  },
  {
    label: "基地联系电话",
    type: "input",
    prop: "emergencyPhone",
    // check: true,
    placeholder: "请输入基地联系电话",
    width: "400px",
    maxLength: 15
  },
  {
    label: "基地介绍",
    type: "editor",
    prop: "introduction",
    check: true,
    placeholder: "请输入基地介绍",
    width: "400px"
  }
]);
// 自定义校验方法
const validateIntroduction = (rule, value, callback) => {
  const errorMsg = "基地介绍不能为空";

  // 检查是否为空
  if (!value) {
    return callback(new Error(errorMsg));
  }

  // 移除HTML标签和HTML空格字符，并去除前后空格
  let cleanValue = value
    .replace(/<[^>]*>/g, "")
    .replace(/&nbsp;|&ensp;|&emsp;|&thinsp;/g, "")
    .trim();

  return cleanValue === "" ? callback(new Error(errorMsg)) : callback();
};
// 校验规则
const rules = ref({
  name: [
    { required: true, message: "基地名称不能为空", trigger: "blur" }
    // { min: 2, max: 10, message: "姓名长度应在2-10个字符之间", trigger: "blur" }
  ],
  detailedAddress: [
    { required: true, message: "详细地址不能为空", trigger: "blur" }
  ],
  introduction: [
    { required: true, validator: validateIntroduction, trigger: ["blur"] }
  ],
  emergencyPhone: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (!value) {
          callback();
          return;
        }
        if (isValidHotline(value)) {
          callback();
          return;
        }

        if (!/^1\d+$/.test(value)) {
          callback("联系电话格式不正确");
          return;
        }
        if (value.length !== 11) {
          callback("联系电话长度应为11位");
          return;
        }
        callback();
      },
      trigger: ["change", "blur"]
    }
  ]
});

function isValidHotline(phone) {
  const cleanedPhone = phone.replace(/[-\s.]/g, "");
  const pattern = /^(?:400\d{7}|800\d{7}|95\d{3,4}|96\d{4,5}|0\d{9,11})$/;
  return pattern.test(cleanedPhone);
}

const submitForm = debounce(
  () => {
    formRef.value.validate(valid => {
      if (valid) {
        console.log("表单数据:", form.value);
        addbase();
      } else {
        console.log("表单校验失败");
      }
    });
  },
  1000,
  { immediate: true }
);
//新建基地
const getListLoading = ref(false);
const addbase = async () => {
  if (getListLoading.value) return;
  getListLoading.value = true;
  let paramsData = {};
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  if (form.value.emergencyPhone) {
    paramsData.emergencyPhone = encryption(form.value.emergencyPhone);
  }
  console.log("🦄-----paramsData-----", paramsData);

  const operateLog = {
    operateLogType: "COMPLEX_MANAGEMENT",
    operateType: "新增了",
    operatorTarget: `一个名为"${paramsData.name}"的基地`
  };
  const { code, data } = await complexSave(paramsData, operateLog);
  if (code === 200) {
    ElMessage({
      type: "success",
      message: "新增成功"
    });
    if (route.query.type || route.query.periodId) {
      useCourseStore.saveBaseInfo({ id: data.id, name: data.name });
    }
    cancelForm();
  } else {
    ElMessage({
      type: "error",
      message: "新增失败"
    });
  }
  getListLoading.value = false;
};
// 取消
const cancelForm = () => {
  const currentPath = router.currentRoute.value.path;
  const isFromCopy = currentPath.includes("periodCopy");

  if (
    route.query.type === "create" ||
    route.query.type === "draft" ||
    route.query?.type === "createPeriod"
  ) {
    // 从课期创建页面进入的新建基地
    router.replace({
      path: "/course/courseCreate",
      query: {
        type: route.query.type,
        courseId: route.query.courseId,
        create: "create",
        name: route.query.name,
        termNumber: route.query.termNumber,
        draftId: route.query.draftId
      }
    });
  } else if (route.query.type === "edite") {
    router.replace({
      path: "/course/coursePeriodEdite",
      query: {
        type: route.query.type,
        periodId: route.query.periodId,
        courseId: route.query.courseId,
        fromPage: route.query.fromPage,
        create: "create",
        name: route.query.name,
        termNumber: route.query.termNumber
      }
    });
  } else {
    router.push({
      path: "/institution/baseManage"
    });
  }
};
const dialogTableVisible = ref(false);
const locationPostion = ref([116.3912757, 39.906217]);
const selectedLocation = ref(null); // 存储用户选择的位置信息
const teacherTimeInfo = {
  location_range: 1000
};
const handleMapConfirm = data => {
  console.log("🎉-----handleMapConfirm-----", data);
  form.value.detailedAddress = data.address;
  form.value.latitude = data.point.lat;
  form.value.longitude = data.point.lng;

  // 保存选择的位置信息，下次打开时会自动定位到这里
  selectedLocation.value = data;
};
</script>

<template>
  <div class="containers">
    <div class="table_content">
      <el-form ref="formRef" :model="form" :rules="rules">
        <el-scrollbar class="scrollbar">
          <el-descriptions title="" label-width="200px" :column="1" border>
            <el-descriptions-item
              v-for="(item, index) in formData"
              :key="index"
              label-align="center"
              label-class-name="my-label"
            >
              <template #label>
                <span v-if="item.check" class="star">*</span>{{ item.label }}
              </template>
              <el-form-item
                :prop="item.prop"
                :inline-message="item.check"
                style="margin-bottom: 0"
                :show-message="true"
                error-placement="right"
              >
                <!-- input输入 -->
                <template v-if="item.type === 'input'">
                  <el-input
                    v-model.trim="form[item.prop]"
                    :placeholder="item.placeholder"
                    :style="{ width: item.width }"
                    :maxlength="item.maxLength"
                    :show-word-limit="true"
                  />
                </template>
                <!-- 富文本 -->
                <template v-else-if="item.type === 'editor'">
                  <div style="width: 100%">
                    <RichEditor v-model="form[item.prop]" height="200px" />
                  </div>
                </template>
                <!-- 地图 -->
                <template v-else-if="item.type === 'map'">
                  <div class="selsect-pos">
                    <div class="cover" @click="dialogTableVisible = true" />
                    <el-input
                      v-model="form[item.prop]"
                      class="input-part"
                      placeholder="请选择地址"
                      :style="{ width: item.width }"
                      :tabindex="-1"
                    >
                      <template #suffix>
                        <div class="pos-icon">
                          <img :src="ImgPos" class="wfull h-full" alt="">
                        </div>
                      </template>
                    </el-input>
                  </div>
                </template>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
        </el-scrollbar>

        <div class="table_bottom">
          <el-button type="default" @click="cancelForm"> 取消 </el-button>
          <el-button type="primary" @click="submitForm"> 确认新建 </el-button>
        </div>
      </el-form>
    </div>
    <Map
      v-if="dialogTableVisible"
      v-model="dialogTableVisible"
      :center="locationPostion"
      :selected-location="selectedLocation"
      :checkInResult="teacherTimeInfo"
      @confirm="handleMapConfirm"
    />
  </div>
</template>

<style lang="scss" scoped>
.scrollbar {
  // padding-top: 20px;
  // padding: 20px;
  // padding-bottom: 0px;

  height: calc(100vh - 200px);
  background-color: #fff;
}
.containers {
  box-sizing: border-box;
  width: calc(100% - 48px);
  height: 100%;
  //   padding: 24px;
  background: #f0f2f5;

  .table_top {
    box-sizing: border-box;
    padding: 20px;
    background-color: #fff;
  }

  .table_content {
    box-sizing: border-box;
    padding: 20px;
    margin-top: 20px;
    background-color: rgb(255 255 255);

    .star {
      margin-right: 3px;
      color: red;
    }

    .el-descriptions-item {
      display: flex;
      align-items: center;
    }

    .Vacode {
      margin-left: 20px;
    }

    .isQR {
      margin-right: 240px;
    }

    .codeQR {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }

  .table_bottom {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
  }
}

:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
  //   width: 240px;
}

:deep(.my-label) {
  background: #e1f5ff !important;
}

:deep(.my-content) {
  background: var(--el-color-danger-light-9);
}
.selsect-pos {
  position: relative;
  // width: 490px;
  // height: 60px;
  // background: red;
  .cover {
    cursor: pointer;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    // background: red;
    width: 100%;
    height: 100%;
  }

  .pos-icon {
    width: 24px;
    height: 24px;
  }
}
</style>
