<script setup>
import { useRouter, useRoute } from "vue-router";
import { onMounted, ref } from "vue";
import { formatTime } from "@/utils/index";
import { findCommentsId, commentsReply } from "@/api/period.js";
import { requestTo } from "@/utils/http/tool";
import { to } from "@iceywu/utils";
import { ElMessage, ElMessageBox } from "element-plus";
import ImgList from "@/components/Base/list1.vue";
import DescriptionList from "./descriptionList.vue";

const router = useRouter();
const route = useRoute();
const detailShow = ref(false);
const deteilInfo = ref({ textarea: "", content: "" });
// 表头
const tableHeader = ref([
  {
    id: "1",
    label: "课程评分",
    value: "--",
    width: "107px"
  },
  {
    id: "2",
    label: "领队评分",
    value: "--",
    width: "107px"
  },
  {
    id: "3",
    label: "场地评分",
    value: "--",
    width: "107px"
  },
  {
    id: "4",
    label: "讲师评分",
    value: "--",
    width: "107px"
  }
]);
const fileListAPi = ref([]);
const srcList = ref([]);
const srcId = ref(0);
const showPreview = ref(false);
// 获取数据
const getFindCommentsId = async () => {
  const params = {
    id: Number(route.query.id)
  };
  const [err, result] = await to(findCommentsId(params));
  // console.log("🦄result----111-------------------------->", result);
  if (result?.code === 200) {
    deteilInfo.value = result?.data;
    tableHeader.value[0].value = result?.data?.courseScore;
    tableHeader.value[1].value = result?.data?.leaderScore;
    tableHeader.value[2].value = result?.data?.siteScore;
    tableHeader.value[3].value = result?.data?.lecturerScore;
    deteilInfo.value.textarea = result?.data?.replies?.content;
    if (result?.data?.files?.length) {
      result?.data?.files?.map(item => {
        fileListAPi.value.push(item?.uploadFile);
        srcList.value.push(item?.uploadFile?.url);
      });
    }
  } else {
    console.log("获取失败");
  }
}; // 图片预览
const handleClick = id => {
  showPreview.value = true;
  srcId.value = id;
};
const periodName = ref("");
const periodNameEvt = val => {
  periodName.value = val;
};
const submitLoading = ref(false);
// 评论回复
const saveEvt = async () => {
  if (submitLoading.value) return;
  submitLoading.value = true;
  if (!deteilInfo.value.textarea) {
    ElMessage.error("请输入回复内容");
    return;
  }
  const params = {
    commentsId: Number(route.query.id),
    content: deteilInfo.value.textarea
  };
  const operateLog = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `回复了“${periodName.value}”课期中的“${deteilInfo?.value?.parent?.name}”用户的评价`
    // operatorTarget: form.value.name,
  };
  const [err, result] = await to(commentsReply(params, operateLog));
  //   console.log("🦄result------------------------------>", result);
  if (result?.code === 200) {
    ElMessage.success("保存成功");
    detailShow.value = !detailShow.value;
    getFindCommentsId();
  } else {
    ElMessage.error(`保存失败,${result?.msg}`);
  }
  submitLoading.value = false;
};
// 取消
const cancel = () => {
  if (route.query.periodId) {
    router.replace({
      path: "/course/courseDetails/currentDetails",
      query: {
        infoShow: "用户评价",
        periodId: route.query.periodId,
        courseId: route.query.courseId
      }
    });
  } else {
    router.replace({
      path: "/course/allEvaluate",
      query: {
        infoShow: "用户评价",
        courseId: route.query.courseId
      }
    });
  }
};
onMounted(() => {
  getFindCommentsId();
});
</script>

<template>
  <div>
    <DescriptionList
      :periodId="Number(route.query.periodId)"
      :courseId="Number(route.query.courseId)"
      @name="periodNameEvt"
    />
    <div class="evaluate-detail">
      <div class="user-comments">
        <div class="title">
          <div class="text">
            <div class="name">{{ deteilInfo?.parent?.name || "" }}</div>
            <div class="number">
              {{
                deteilInfo?.parent?.termNumber
                  ? `期数${deteilInfo?.parent?.termNumber}`
                  : ""
              }}
            </div>
          </div>
          <div class="text">
            {{
              deteilInfo?.createdAt
                ? formatTime(deteilInfo?.createdAt, "YYYY-MM-DD HH:mm:ss")
                : ""
            }}
          </div>
        </div>
        <div class="evaluate">
          <el-input
            v-model="deteilInfo.content"
            type="textarea"
            resize="none"
            disabled
            class="area"
          />
          <div class="pic">
            <ImgList
              v-if="fileListAPi?.length"
              :imgList="fileListAPi"
              :srcList="srcList"
              :itemCount="fileListAPi?.length"
            />
            <!-- <div v-if="fileListAPi?.length" class="banner">
            <el-carousel
              :interval="4000"
              type="card"
              height="200px"
              :autoplay="false"
            >
              <el-carousel-item
                v-for="(item, index) in fileListAPi"
                :key="item"
              >
                <img
                  :src="item.url"
                  class="h-full"
                  @click="handleClick(index)"
                >
              </el-carousel-item>
            </el-carousel>
            <el-image-viewer
              v-if="showPreview"
              :url-list="srcList"
              show-progress
              :initial-index="srcId"
              @close="showPreview = false"
            />
          </div> -->
            <el-empty v-else style="height: 200px" description="暂无配图" />
          </div>
        </div>
      </div>
      <div class="rating">
        <div class="title">
          <div class="name">{{ "评分" }}</div>
          <div class="number">{{ deteilInfo?.rating || 0 }}</div>
        </div>
        <div class="content">
          <el-descriptions class="margin-top" title="" :column="2" border>
            <template v-for="(item, index) in tableHeader" :key="index">
              <el-descriptions-item width="120px">
                <template #label>
                  <div class="cell-item">{{ item.label }}</div>
                </template>
                {{ item.value }}
              </el-descriptions-item>
            </template>
          </el-descriptions>
        </div>
      </div>
      <div class="replies">
        <div class="title">
          <div class="text">
            {{ "机构回复" }}
          </div>
          <div class="text">
            {{
              deteilInfo?.replies?.createdAt
                ? formatTime(
                    deteilInfo?.replies?.createdAt,
                    "YYYY-MM-DD HH:mm:ss"
                  )
                : ""
            }}
          </div>
        </div>
        <div class="content">
          <div class="text-area">
            <el-input
              v-model="deteilInfo.textarea"
              type="textarea"
              :placeholder="
                deteilInfo.textarea
                  ? deteilInfo.textarea
                  : detailShow
                    ? '请输入'
                    : '暂无数据'
              "
              resize="none"
              :disabled="!detailShow"
            />
          </div>
          <div v-if="detailShow" class="buttons">
            <!-- <div class="create save" @click="saveEvt">
            {{ "保存" }}
          </div> -->
            <!-- <div
            class="cancel"
            @click="
              router.replace({
                path: '/course/courseDetails/currentDetails',
                query: {
                  infoShow: '用户评价',
                  periodId: route.query.periodId,
                  courseId: route.query.courseId
                }
              })
            "
          >
            取消
          </div> -->
            <el-button
              type="primary"
              style="margin-bottom: 10px"
              :loading="submitLoading"
              @click="saveEvt"
            >
              {{ "保存" }}
            </el-button>
            <el-button @click="cancel"> 取消 </el-button>
          </div>
          <div v-else class="buttons">
            <!-- <div class="create" @click="detailShow = !detailShow">
            {{ "编辑回复" }}
          </div> -->
            <el-button type="primary" @click="detailShow = !detailShow">
              {{ "编辑回复" }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.evaluate-detail {
  font-size: 14px;
  background-color: #fff;
  width: 100%;
  height: 600px;
  padding: 20px 20px;
  .user-comments {
    width: 100%;
    margin-bottom: 25px;
    .title {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      .text {
        display: flex;
      }
      .name {
        margin-right: 50px;
      }
    }
    .evaluate {
      width: 100%;
      display: flex;
      justify-content: space-between;

      .area {
        width: 49%;
        height: 120px;
      }
      :deep(.el-textarea__inner) {
        height: 120px;
      }
      .pic {
        width: 49%;
        height: 120px;
        :deep(.el-carousel--horizontal) {
          height: 120px;
        }
      }
    }
  }
  .rating {
    width: 100%;
    margin-bottom: 25px;
    .title {
      width: 100%;
      display: flex;
      margin-bottom: 10px;
      .name {
        margin-right: 30px;
      }
    }
    .content {
      width: 49%;
    }
  }
  .replies {
    width: 100%;
    .title {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }
    .content {
      width: 100%;
      display: flex;
      justify-content: space-between;
      .text-area {
        width: 93%;
        :deep(.el-textarea__inner) {
          height: 110px;
        }
      }
      .buttons {
        // display: flex;
        // justify-content: space-between;
        // width: 95%;
        // margin: 0 auto;
        // margin-top: 28vh;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        justify-content: flex-end;
        .cancel {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100px;
          height: 36px;
          color: rgb(255 255 255 / 100%);
          cursor: pointer;
          background-color: rgb(230 152 58 / 100%);
          border-radius: 6px;
        }

        .create {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100px;
          height: 36px;
          color: rgb(255 255 255 / 100%);
          cursor: pointer;
          background-color: rgb(64 149 229 / 100%);
          border-radius: 6px;
        }
        .save {
          margin-bottom: 20px;
        }
      }
    }
  }
  :deep(.el-carousel__item) {
    margin: 0;
    line-height: 200px;
    text-align: center;
  }
}
</style>
