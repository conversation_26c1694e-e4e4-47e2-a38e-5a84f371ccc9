<script setup>
import { ref } from "vue";
import { Hide, View } from "@element-plus/icons-vue";
import RichEditor from "@/components/Base/RichEditor.vue";

const form = ref({
  startTime: "",
  endTime: "",
  name: "",
  ordersId: "",
  courseName: "",
  orderStatus: ""
});
const formData = ref([
  {
    label: "基地名",
    type: "text",
    // check: true,
    prop: "orderFile",
    placeholder: "",
    width: "200px"
  },
  {
    label: "机构",
    type: "text",
    // check: true,
    prop: "orderFile",
    placeholder: "",
    width: "200px"
  }
]);
const formDataTow = ref([
  {
    label: "申请时间",
    type: "text",
    // check: true,
    prop: "orderFile",
    placeholder: "",
    width: "200px"
  },
  {
    label: "审核状态",
    type: "text",
    // check: true,
    prop: "orderFile",
    placeholder: "",
    width: "200px"
  },
  {
    label: "审核类型",
    type: "text",
    // check: true,
    prop: "orderFile",
    placeholder: "",
    width: "200px"
  }
]);
// 表格数据
const tableData = ref([
  {
    id: 0,
    name: "基地简介",
    File: "*****************",
    FileH: "*****************"
  },
  {
    id: 2,
    name: "机构简介",
    type: "editor",
    File: "111",
    FileH: "2222"
  }
]);
const textarea = ref("");
</script>

<template>
  <div>
    <div class="header">
      <el-descriptions title="" :column="3" border :label-width="'200px'">
        <el-descriptions-item
          v-for="(item, index) in formData"
          :key="index"
          label-align="center"
          label-class-name="my-label"
        >
          <template #label>
            {{ item.label }}
          </template>
          <el-form-item
            :prop="item.prop"
            :inline-message="item.check"
            style="margin-bottom: 0"
            :show-message="true"
            error-placement="right"
          >
            <!-- text -->
            <template v-if="item.type === 'text'">
              <div class="cell_item">
                <div style="color: #a8a8a8">
                  <!-- {{
                    item.prop === "createdAt"
                      ? formatTime(form[item.prop], "YYYY-MM-DD HH:mm:ss")
                      : form[item.prop] || "--"
                  }} -->
                  {{ form[item.prop] || "--" }}
                </div>
                <span v-if="form[item.prop] && item.isEye" class="icon">
                  <el-icon
                    v-if="item.isView"
                    style="cursor: pointer"
                    @click="isViewFn(index)"
                  >
                    <Hide />
                  </el-icon>
                  <el-icon
                    v-else
                    style="cursor: pointer"
                    @click="isViewFn(index)"
                  >
                    <View />
                  </el-icon>
                </span>
              </div>
            </template>
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="" :column="3" border :label-width="'200px'">
        <el-descriptions-item
          v-for="(item, index) in formDataTow"
          :key="index"
          label-align="center"
          label-class-name="my-label"
        >
          <template #label>
            {{ item.label }}
          </template>
          <el-form-item
            :prop="item.prop"
            :inline-message="item.check"
            style="margin-bottom: 0"
            :show-message="true"
            error-placement="right"
          >
            <!-- text -->
            <template v-if="item.type === 'text'">
              <div class="cell_item">
                <div style="color: #a8a8a8">
                  <!-- {{
                    item.prop === "createdAt"
                      ? formatTime(form[item.prop], "YYYY-MM-DD HH:mm:ss")
                      : form[item.prop] || "--"
                  }} -->
                  {{ form[item.prop] || "--" }}
                </div>
                <span v-if="form[item.prop] && item.isEye" class="icon">
                  <el-icon
                    v-if="item.isView"
                    style="cursor: pointer"
                    @click="isViewFn(index)"
                  >
                    <Hide />
                  </el-icon>
                  <el-icon
                    v-else
                    style="cursor: pointer"
                    @click="isViewFn(index)"
                  >
                    <View />
                  </el-icon>
                </span>
              </div>
            </template>
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>
      <div class="opinion">
        <div class="opinion_item">
          <p>审核意见</p>
          <el-input
            v-model="textarea"
            style="width: 100%"
            :rows="2"
            type="textarea"
            placeholder=""
          />
        </div>
        <div class="opinion_btn">
          <el-button type="primary">驳回</el-button>
          <el-button type="primary">通过</el-button>
        </div>
      </div>
    </div>
    <div class="mian">
      <p style="font-weight: 600">修改内容</p>
      <el-table :data="tableData" height="450">
        <el-table-column prop="name" width="120px" label="项目" align="center">
          <template #default="scope">
            <div>
              {{ scope.row.name || "--" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="File" label="修改前" align="center">
          <template #default="scope">
            <div v-if="scope.row.type !== 'editor'">
              {{ scope.row.File || "--" }}
            </div>
            <div v-else style="background: #f5f5f5">
              <div style="width: 100%; background: #f5f5f5">
                <el-carousel
                  indicator-position="outside"
                  height="200px"
                  motion-blur
                >
                  <el-carousel-item v-for="item in 4" :key="item">
                    <h3 class="small justify-center" text="2xl">{{ item }}</h3>
                  </el-carousel-item>
                </el-carousel>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="FileH" label="修改后" align="center">
          <template #default="scope">
            <div v-if="scope.row.type !== 'editor'">
              {{ scope.row.FileH || "--" }}
            </div>
            <div v-else>
              <div style="width: 100%; background: #f5f5f5">
                <el-carousel
                  indicator-position="outside"
                  height="200px"
                  motion-blur
                >
                  <el-carousel-item v-for="item in 4" :key="item">
                    <h3 class="small justify-center" text="2xl">{{ item }}</h3>
                  </el-carousel-item>
                </el-carousel>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.header {
  padding: 20px;
  background: #fff;
  .opinion {
    margin-top: 10px;
    display: flex;
    .opinion_item {
      width: 80%;
    }
    .opinion_btn {
      margin-left: 20px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      :nth-child(2) {
        margin-top: 10px;
        margin-left: 0px;
      }
    }
  }
}
:deep(.my-label) {
  background: #e1f5ff !important;
}
.mian {
  padding: 20px;
  background: #fff;
  margin-top: 20px;
}
</style>
