<script setup>
import { ref, onMounted, onActivated, nextTick, computed, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { coursePeriodFind } from "@/api/course.js";
import { coursePeriodCopy } from "@/api/period.js";
import { usePopup } from "vue-hooks-pure";
import { requestTo } from "@/utils/http/tool";
import dayjs from "dayjs";
import { courseStore } from "@/store/modules/course.js";
import { to, removeEmptyValues } from "@iceywu/utils";
import { COURSE_PERIOD_ENUM, AUDIT_ENUM } from "@/utils/enum.js";
import { ElMessage, ElMessageBox } from "element-plus";
import { Warning, ArrowDown, ArrowUp } from "@element-plus/icons-vue";

defineOptions({
  name: "CopyCourseSchedule"
});

onActivated(() => {
  getCoursePeriodFind();
});

const useCourseStore = courseStore();
const route = useRoute();
const router = useRouter();
const formContainerRef = ref();

const coursePeriodInfo = ref({});
const form = ref({
  prefix: "无",
  suffix: "无"
});
const options = ref([
  { value: "无", label: "无" },
  { value: "课期数", label: "课期数" },
  { value: "开课日期", label: "开课日期" }
]);

// 表头
const tableHeader = ref([
  {
    id: "1",
    label: "课期名",
    value: "",
    width: "107px"
  },
  {
    id: "2",
    label: "期号",
    value: "",
    width: "107px"
  },
  {
    id: "3",
    label: "课期ID",
    value: "",
    width: "107px"
  },
  {
    id: "4",
    label: "创建时间",
    value: "",
    width: "107px"
  },
  {
    id: "5",
    label: "领队",
    value: "",
    width: "107px"
  },
  {
    id: "6",
    label: "讲师",
    value: "",
    width: "107px"
  },
  {
    id: "7",
    label: "开课时间",
    value: "",
    width: "107px"
  },
  {
    id: "8",
    label: "报名截止",
    value: "",
    width: "107px"
  },
  {
    id: "9",
    label: "基地",
    value: "",
    width: "107px"
  },
  {
    id: "10",
    label: "购买类型",
    value: "",
    width: "107px"
  },
  {
    id: "11",
    label: "课期状态",
    value: "",
    width: "107px",
    state: ""
  },
  {
    id: "12",
    label: "审核状态",
    value: "",
    width: "107px",
    state: "",
    opinion: ""
  }
]);

// 修改这里：用空格代替逗号分隔
const formattedDates = computed(() => {
  return selectedDates.value.map(d => dayjs(d).format("YYYY-MM-DD")).join("  "); // 改为空格分隔
});
const selectedDates = ref([]);
const currentDatesed = ref(new Date());
const isSelected = dateString => {
  return selectedDates.value.some(
    d => dayjs(d).format("YYYY-MM-DD") === dateString
  );
};
const toggleDate = dateString => {
  const date = new Date(dateString);
  const index = selectedDates.value.findIndex(
    d => dayjs(d).format("YYYY-MM-DD") === dateString
  );
  const date1 = new Date(dateString + "T00:00:00");
  const timestamp = date1.getTime();
  if (index === -1) {
    // if (coursePeriodInfo.value.openTime <= timestamp) {
    selectedDates.value.push(date);
    // } else {
    //   return ElMessage.error("开课时间不能早于或等于当前时间");
    // }
  } else {
    selectedDates.value.splice(index, 1);
  }
};

function disabledDate(time) {
  // 禁用今天及之前的日期（time 是 Date 对象）
  const date1 = new Date(time + "T00:00:00");
  return date1.getTime() < new Date().setHours(0, 0, 0, 0);
}

function prefixA(val) {
  let obj = ref({
    prefixRule: "",
    suffixRule: "",
    prefix: "",
    suffix: ""
  });
  if (val.prefix !== "无") {
    if (val.prefix === "课期数" || val.prefix === "开课日期") {
      obj.value.prefixRule =
        val.prefix === "课期数" ? "TERM_NUMBER" : "OPEN_TIME";
    } else {
      obj.value.prefixRule = "OTHER";
      obj.value.prefix = val.prefix;
    }
  }

  if (val.suffix !== "无") {
    if (val.suffix === "课期数" || val.suffix === "开课日期") {
      obj.value.suffixRule =
        val.suffix === "课期数" ? "TERM_NUMBER" : "OPEN_TIME";
    } else {
      obj.value.suffixRule = "OTHER";
      obj.value.suffix = val.suffix;
    }
  }
  return obj.value;
}

const prefixRuleA = ref({});
// 复制并编辑
const copyEditing = async text => {
  const operateLog = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `复制了“${coursePeriodInfo.value.name}”课期`
    // operatorTarget: form.value.name,
  };

  const preTime = dayjs(coursePeriodInfo.value.openTime).format("HH:mm:ss");
  const sufTime = dayjs(coursePeriodInfo.value.signUpDeadline).format(
    "HH:mm:ss"
  );

  let coursePeriodTimes = ref([]);
  if (selectedDates.value?.length) {
    selectedDates.value.forEach(item => {
      const oTime = dayjs(item).format("YYYY-MM-DD");
      coursePeriodTimes.value.push({
        openTime: dayjs(`${oTime} ${preTime}`).valueOf(),
        signUpDeadline: dayjs(`${oTime} ${sufTime}`).valueOf()
      });
    });
  } else {
    ElMessage.error("请设置开课时间！");
    return;
  }
  prefixRuleA.value = prefixA(form.value);
  const courseCover = !!coursePeriodInfo.value.isCourseCover;
  const covers = coursePeriodInfo.value?.cover.map(it => {
    return {
      fileIdentifier: it.uploadFile?.fileIdentifier,
      sortOrder: it.sortOrder,
      fileType: it.fileType
    };
  });
  const params = {
    courseId: coursePeriodInfo.value.course.id,
    courseTypeId: coursePeriodInfo.value.courseType.id,
    name: coursePeriodInfo.value.name,
    complexId: coursePeriodInfo.value.complex.id,
    leaderIds: coursePeriodInfo.value.leaders?.map(it => it.id) || "",
    lecturerIds: coursePeriodInfo.value.lecturers?.map(it => it.id) || "",
    minPeopleNumber: coursePeriodInfo.value.minPeopleNumber,
    maxPeopleNumber: coursePeriodInfo.value.maxPeopleNumber,
    cover: covers,
    isCourseCover: courseCover,
    coursePeriodTimes: coursePeriodTimes.value,
    copyCoursePeriodId: coursePeriodInfo.value.id,
    prefixRule: prefixRuleA.value?.prefixRule,
    suffixRule: prefixRuleA.value?.suffixRule,
    prefix: prefixRuleA.value?.prefix,
    suffix: prefixRuleA.value?.suffix
  };
  const newList = removeEmptyValues(params);
  const [err, res] = await to(coursePeriodCopy(newList, operateLog));
  if (res.code === 200) {
    ElMessage.success("复制成功");
    if (text) {
      router.go(-1);
    } else {
      router.push({
        path: "/course/coursePeriodEdite",
        query: {
          periodId: res.data.coursePeriodId,
          type: "edite",
          courseId: res.data.courseId,
          fromPage: "courseDetail"
        }
      });
    }
  } else {
    ElMessage.error(`复制失败,${res.msg}`);
  }
  if (err) {
    ElMessage.error("复制失败");
  }
};

// 取消 确认复制
const cancel = () => {
  form.value = {};
  formattedDates.value = [];
  selectedDates.value = [];
  return router.go(-1);
};

const url = ref();
const srcList = ref([]);
const state = ref();
const freezeCourse = ref(false);
const oldTermNumber = ref();
const oldPeriodName = ref("");
// 查询课期详情
const getCoursePeriodFind = async () => {
  let [err, res] = await requestTo(
    coursePeriodFind({ id: route.query.periodId })
  );
  if (res) {
    // console.log("🐬-----res1111--333---", res);
    coursePeriodInfo.value = res || {};
    freezeCourse.value = res.courseIsFreeze || false;
    tableHeader.value[0].value = res.name || "--";
    oldPeriodName.value = res.originalName || res.name.split("-")[1];
    tableHeader.value[1].value = res.termNumber || "0";
    tableHeader.value[2].value = res.id || "--";
    tableHeader.value[3].value =
      dayjs(res.createdAt).format("YYYY-MM-DD HH:mm:ss") || "--";
    tableHeader.value[9].value =
      res.buyType === "ORDINARY"
        ? "普通单"
        : res.buyType === "PRIVATE_DOMAIN_GROUP_ORDER"
          ? "团购单"
          : "--";
    // tableHeader.value[5].value = res.organization?.name || "--";
    tableHeader.value[8].value = res.complex?.name || "--";
    tableHeader.value[6].value =
      dayjs(res.openTime).format("YYYY-MM-DD HH:mm:ss") || "--";
    tableHeader.value[7].value =
      dayjs(res.signUpDeadline).format("YYYY-MM-DD HH:mm:ss") || "--";
    if (res.leaders?.length) {
      let learderList = [];
      res.leaders.map(item => {
        learderList.push(item.name);
      });
      tableHeader.value[4].value = learderList.join(" 、") || "--";
    }
    if (res.lecturers?.length) {
      let lecturersList = [];
      res.lecturers.map(item => {
        lecturersList.push(item.name);
      });
      tableHeader.value[5].value = lecturersList.join(" 、") || "--";
    }
    tableHeader.value[10].value =
      COURSE_PERIOD_ENUM[res.coursePeriodState]?.label || "--";
    tableHeader.value[10].state = res.offlineType || "--";
    tableHeader.value[11].value = AUDIT_ENUM[res.reviewState]?.label || "无";
    tableHeader.value[11].state = res.reviewState || "NONE";
    tableHeader.value[11].opinion = res.opinion || "无";
    if (res.tags) {
      let nameOptions = res.tags;
      options.value = [
        ...options.value,
        ...nameOptions.map((label, index) => ({
          value: label,
          label
        }))
      ];
    }
    oldTermNumber.value = res.termNumber;
    state.value = res.coursePeriodState;
    useCourseStore.savePeriodState(state.value);
    if (res.cover?.length) {
      res.cover.map(item => {
        srcList.value.push(item?.uploadFile?.url);
      });
      url.value = res.cover[0]?.uploadFile?.url;
    }
  } else {
    console.log("🐳-----err-----", err);
  }
};
// 课期命名示意
const periodName = ref("");
watch(
  () => [
    form.value.prefix,
    form.value.suffix,
    selectedDates.value,
    oldPeriodName.value
  ],
  ([newPrefix, newSuffix, newData, newName]) => {
    let prefixLabel =
      options.value.find(i => i.label === newPrefix)?.label || "";
    let suffixLabel =
      options.value.find(i => i.label === newSuffix)?.label || "";
    let prefixText = "";
    let suffixText = "";
    if (prefixLabel === "课期数") {
      if (oldTermNumber.value) {
        prefixText = `第${oldTermNumber.value + 1}期`;
      } else {
        prefixText = `第1期`;
      }
    } else if (prefixLabel === "无") {
      prefixText = ``;
    } else if (prefixLabel === "开课日期") {
      if (newData?.length > 0) {
        let openDate = dayjs(newData[0]).format("MMDD");
        prefixText = `${openDate}`;
      } else {
        prefixText = ``;
      }
    } else {
      prefixText = `${prefixLabel}`;
    }
    if (suffixLabel === "课期数") {
      if (oldTermNumber.value) {
        suffixText = `第${oldTermNumber.value + 1}期`;
      } else {
        suffixText = `第1期`;
      }
    } else if (suffixLabel === "无") {
      suffixText = ``;
    } else if (suffixLabel === "开课日期") {
      if (newData?.length > 0) {
        let openDate = dayjs(newData[0]).format("MMDD");
        suffixText = `${openDate}`;
      } else {
        suffixText = ``;
      }
    } else {
      suffixText = `${suffixLabel}`;
    }
    // 拼接新的课期名
    if (prefixText && oldPeriodName.value && suffixText) {
      periodName.value = `${prefixText}-${oldPeriodName.value}-${suffixText}`;
    } else if (prefixText && oldPeriodName.value) {
      periodName.value = `${prefixText}-${oldPeriodName.value}`;
    } else if (oldPeriodName.value && suffixText) {
      periodName.value = `${oldPeriodName.value}-${suffixText}`;
    } else if (oldPeriodName.value) {
      periodName.value = `${oldPeriodName.value}`;
    } else {
      periodName.value = "";
    }
  },
  { immediate: true, deep: true }
);
onMounted(() => {
  // console.log("🌈-----route.query-----", route.query);
  nextTick();
  getCoursePeriodFind();
});
</script>

<template>
  <div class="examine-detail">
    <div ref="formContainerRef" class="info">
      <!-- 完整的表格(展开状态) -->
      <div class="curse-table">
        <el-descriptions
          title=""
          :column="4"
          border
          class="descriptions-table full-descriptions"
        >
          <template v-for="(item, index) in tableHeader" :key="index">
            <el-descriptions-item width="120px" label-align="center">
              <template #label>
                <div class="cell-item">{{ item.label }}</div>
              </template>
              <div
                v-if="
                  (item.label === '审核状态' &&
                    item.state === 'OFFLINE_REJECT') ||
                  item.state === 'ONLINE_REJECT'
                "
              >
                <el-tooltip
                  class="box-item"
                  title=""
                  :content="item.opinion ? item.opinion : '无'"
                  placement="bottom"
                  effect="light"
                >
                  <div class="warning">
                    {{ item.value }}
                    <el-icon style="color: red; margin-left: 3px">
                      <Warning />
                    </el-icon>
                  </div>
                </el-tooltip>
              </div>
              <div
                v-else-if="
                  (item.label === '课期状态' &&
                    item.state === 'PLATFORM_OFFLINE') ||
                  item.state === 'PLATFORM_CLOSE_GROUP'
                "
              >
                <el-tooltip
                  class="box-item"
                  title=""
                  :content="
                    item.state === 'PLATFORM_OFFLINE'
                      ? '课期已被强制下架，请联系平台客服了解情况'
                      : '课期已被强制取消定制，请联系平台客服了解情况'
                  "
                  placement="bottom"
                  effect="light"
                >
                  <div class="warning">
                    {{ item.value }}
                    <el-icon style="color: red; margin-left: 3px">
                      <Warning />
                    </el-icon>
                  </div>
                </el-tooltip>
              </div>
              <div v-else>
                {{ item.value }}
              </div>
            </el-descriptions-item>
          </template>
        </el-descriptions>
        <div class="img">
          <!-- <img src="@/assets/user.jpg" alt="" /> -->
          <el-image
            :src="url"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="srcList"
            :hide-on-click-modal="true"
            show-progress
            :initial-index="0"
            fit="cover"
            class="img-pic"
          />
        </div>
      </div>
    </div>

    <div class="info-table">
      <div class="leftbox">
        <el-scrollbar height="calc(100vh - 310px)">
          <div class="lefttitle">
            <div class="title">课期名命设置</div>
            <div class="xian" />
          </div>
          <div class="leftform">
            <el-form :model="form" :inline="true">
              <el-form-item label="前缀">
                <el-select
                  v-model="form.prefix"
                  placeholder="请选择"
                  style="width: 140px"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="后缀">
                <el-select
                  v-model="form.suffix"
                  placeholder="请选择"
                  style="width: 140px"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-form>
            <div>
              课期名示意：
              <!-- <span v-if="form.prefix !== '无'">{{ form.prefix }}-</span>
              <span v-if="coursePeriodInfo.originalName">{{
                coursePeriodInfo.originalName
              }}</span>
              <span v-else> {{ coursePeriodInfo.name }}</span>
              <span v-if="form.suffix !== '无'">-{{ form.suffix }}</span> -->
              <span> {{ periodName }}</span>
            </div>
          </div>

          <div class="lefttitle">
            <div class="title">已选择的日期</div>
            <div class="xian" />
          </div>
          <div class="selected-dates">{{ formattedDates }}</div>
        </el-scrollbar>

        <div>
          <el-button type="primary" @click="copyEditing('confirm')">
            确认复制
          </el-button>
          <el-button
            v-if="selectedDates.length === 1"
            type="primary"
            @click="copyEditing()"
          >
            复制并编辑
          </el-button>
          <el-button type="primary" @click="cancel('cancel')">取消</el-button>
        </div>
      </div>
      <div class="rightbox">
        <div class="righttitle">在日历中选择复制课期的开课日期</div>
        <div class="chooseDates">
          <div class="demo-container">
            <el-calendar
              v-model="currentDatesed"
              style="width: 100%; height: 100%"
            >
              <template #date-cell="{ data }">
                <el-button
                  link
                  :disabled="disabledDate(data.day)"
                  class="cell-content"
                  :class="{ 'selected-date': isSelected(data.day) }"
                  @click.stop="toggleDate(data.day)"
                >
                  {{ data.day.split("-").slice(2).join("-") }}
                  <span v-if="isSelected(data.day)" class="checkmark">✓</span>
                </el-button>
              </template>
            </el-calendar>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.examine-detail {
  display: flex;
  flex-direction: column;
  position: relative;
  height: calc(100vh - 100px);

  .info {
    background-color: #fff;
    box-sizing: border-box;
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    opacity: 1;
    overflow: hidden;

    &.info-collapsed {
      padding-top: 10px;
      padding-bottom: 0;

      .con_top {
        display: none;
      }
    }

    .con_top {
      display: flex;
      justify-content: flex-end;
      margin-bottom: 10px;
      flex-wrap: wrap;
      gap: 10px;
    }

    .curse-table {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      width: 100%;
      opacity: 1;
      transition: opacity 0.3s ease;

      .descriptions-table {
        width: calc(100% - 165px);
        flex-shrink: 0;
        transition: width 0.3s ease;
      }

      .img {
        width: 145px;
        height: 120px;
        margin-left: 20px;
        flex-shrink: 0;
        transition:
          opacity 0.3s ease,
          width 0.3s ease,
          margin-left 0.3s ease;

        .img-pic {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      &.collapsed-table {
        // 收缩状态下的表格
        .descriptions-table {
          width: 100%; // 图片已隐藏，表格占据全宽
        }
      }

      .warning {
        display: flex;
        align-items: center;
      }
    }
  }

  .info-table {
    width: 100%;
    flex: 1;
    overflow: auto;
    background-color: #fff;
    display: flex;
    padding: 20px;
    .leftbox {
      width: 50%;
      height: calc(100% - 30px);
      margin-right: 10px;
      .lefttitle {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .title {
          width: 140px;
        }
        .xian {
          border: solid 1px var(--el-border-color);
          width: 100%;
          height: 1px;
          // margin-left: 20px;
        }
      }
      .leftform {
        margin: 20px 0px;
      }
      .selected-dates {
        margin-top: 20px;
        padding: 10px;
        background-color: #f8f9fa;
        border-radius: 4px;
        white-space: pre-wrap; /* 允许空格换行 */
        font-weight: bold;
        min-height: 44px;
      }
    }
    .rightbox {
      display: flex;
      flex-direction: column;
      width: 50%;
      height: 100%;
      margin-left: 10px;
      .righttitle {
        margin: 0px 0px 20px;
      }
      .chooseDates {
        flex: 1;
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        background-color: #fff;
        // height: 200px;
        .demo-container {
          max-width: 100%;
          max-height: 100%;
          // background-color: #ccc;
          box-shadow: 0px 4px 13px 0px #ccc;
          margin-bottom: 10px;
        }
        .cell-content {
          position: relative;
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          transition: background-color 0.3s;
        }
        .selected-date {
          color: #409eff;
          font-weight: bold;
        }
        .checkmark {
          position: absolute;
          right: 2px;
          bottom: 2px;
          font-size: 12px;
          color: #67c23a;
          font-weight: bold;
        }
      }
    }
  }
}

:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}
:deep(.el-calendar) {
  --el-calendar-cell-width: 85px;
}

:deep(.el-calendar-table .el-calendar-day) {
  width: 100%; //每一个的高
  height: 65px;
  text-align: center;
}
// 使用 v-show 而不是 display:none 来获得更好的过渡动画
.curse-table {
  transition: opacity 0.3s ease;
}
</style>
