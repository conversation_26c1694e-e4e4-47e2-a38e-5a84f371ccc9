// import { $t } from "@/plugins/i18n";
import { ganttastic } from "@/router/enums.js";
import { coCodesList, reCodesList } from "@/router/accidCode.js";
import CourseIcon from "@/assets/home/<USER>";
import CourseIconActive from "@/assets/home/<USER>";
const CurrentDetail = () => import("@/views/course/currentDetails.vue");
export default {
  path: "/course",
  redirect: "/course/courseManage",
  meta: {
    icon: "ri:information-line",
    title: "课程",
    imgIcon: CourseIcon,
    imgIconActive: CourseIconActive,
    rank: ganttastic,
    idCode: coCodesList.baseCode
  },
  children: [
    {
      path: "/course/courseManage",
      name: "courseManage",
      redirect: "/course/courseManage/index",
      // component: () => import("@/views/course/courseManage.vue"),
      meta: {
        title: "课程管理"
        // idCode: coCodesList.courseManage,
      },
      children: [
        {
          path: "/course/courseManage/index",
          name: "CourseManageIndex",
          component: () => import("@/views/course/courseManage.vue"),
          meta: {
            title: "课程管理",
            idCode: coCodesList.courseManage,
            keepAlive: true
          }
          // children: [
          //   {
          //     path: "/course/drafts",
          //     name: "Drafts",
          //     component: () => import("@/views/course/draftsList.vue"),
          //     meta: {
          //       title: "草稿箱",
          //       idCode: coCodesList.drafts,
          //       showLink: false
          //     },
          //     children: [
          //       {
          //         path: "/course/evaluateContent",
          //         name: "evaluateContent",
          //         component: () =>
          //           import("@/views/course/components/evaluateDetail.vue"),
          //         meta: {
          //           title: "评价详情",
          //           idCode: reCodesList.evaluateContent
          //         }
          //       }
          //     ]
          //   }
          // ]
        },
        {
          path: "/course/courseDetails",
          name: "CourseDetails",
          component: () => import("@/views/course/courseDetails.vue"),
          meta: {
            title: "课程详情",
            idCode: reCodesList.courseDetails,
            showLink: false,
            keepAlive: true
          },
          children: [
            {
              path: "/course/courseDetails/currentDetails",
              name: "CurrentDetails",
              component: () => import("@/views/course/currentDetails.vue"),
              // redirect: "/course/courseDetails/currentDetails/tableList",
              meta: {
                title: "当期详情",
                idCode: reCodesList.currentDetails
                // keepAlive: true
              },
              children: [
                {
                  path: "/course/currentDetails/orderDetails",
                  name: "currentOrderDetails",
                  component: () => import("@/views/course/orderDetails.vue"),
                  meta: {
                    title: "订单详情",
                    idCode: reCodesList.currentOrderDetails,
                    showLink: false
                  }
                },
                {
                  path: "/course/periodEdite",
                  name: "periodEdite",
                  component: () => import("@/views/course/periodInfoEdite.vue"),
                  meta: {
                    title: "课期信息编辑",
                    idCode: reCodesList.periodEdite
                  },
                  children: [
                    {
                      path: "/course/periodEdite/baseAdd",
                      name: "periodBaseAdd",
                      component: () =>
                        import("@/views/institution/baseAdd.vue"),
                      meta: {
                        title: "新建基地",
                        idCode: reCodesList.periodBaseAdd,
                        showLink: false
                      }
                    },
                    {
                      path: "/course/periodEdite/leaderCreate",
                      name: "periodLeaderCreate",
                      component: () =>
                        import("@/views/course/accountCreate.vue"),
                      meta: {
                        title: "新建领队",
                        idCode: reCodesList.periodLeaderCreate,
                        showLink: false
                      }
                    },
                    {
                      path: "/course/periodEdite/lecturerCreate",
                      name: "periodLecturerCreate",
                      component: () =>
                        import("@/views/course/accountCreate.vue"),
                      meta: {
                        title: "新建讲师",
                        idCode: reCodesList.periodLecturerCreate,
                        showLink: false
                      }
                    }
                  ]
                },
                {
                  path: "/course/currentDetails/tripEdite",
                  name: "tripEdite",
                  component: () =>
                    import("@/views/course/components/tripEdite.vue"),
                  meta: {
                    title: "行程编辑",
                    idCode: reCodesList.tripEdite
                  },
                  children: [
                    {
                      path: "/course/currentDetails/itineraryAdd",
                      name: "itineraryAdd",
                      component: () =>
                        import("@/views/course/components/itineraryAdd.vue"),
                      meta: {
                        title: "新增编辑行程点",
                        idCode: reCodesList.itineraryAdd
                      }
                    }
                  ]
                },

                {
                  path: "/course/currentDetails/introductionEdite",
                  name: "introductionEdite",
                  component: () =>
                    import("@/views/course/components/introductionEdite.vue"),
                  meta: {
                    title: "课程介绍",
                    idCode: reCodesList.introductionEdite
                  }
                },
                {
                  path: "/course/currentDetails/groupOrder",
                  name: "groupOrder",
                  component: () =>
                    import("@/views/course/components/groupOrder.vue"),
                  meta: {
                    title: "团购分享",
                    idCode: reCodesList.groupOrder
                  }
                },
                {
                  path: "/course/currentDetails/freeEdite",
                  name: "freeEdite",
                  component: () =>
                    import("@/views/course/components/freeEdite.vue"),
                  meta: {
                    title: "费用退款",
                    idCode: reCodesList.freeEdite
                  }
                },
                {
                  path: "/course/currentDetails/priceEdite",
                  name: "priceEdite",
                  component: () =>
                    import("@/views/course/components/priceEdite.vue"),
                  meta: {
                    title: "价格编辑",
                    idCode: reCodesList.priceEdite
                  }
                },
                {
                  path: "/course/currentDetails/reportEdite",
                  name: "reportEdite",
                  component: () =>
                    import("@/views/course/components/reportEdite.vue"),
                  meta: {
                    title: "课程报告编辑",
                    idCode: reCodesList.reportEdite
                  }
                },
                {
                  path: "/course/currentDetails/evaluateDetail",
                  name: "evaluateDetail",
                  component: () =>
                    import("@/views/course/components/evaluateDetail.vue"),
                  meta: {
                    title: "用户评价详情",
                    idCode: reCodesList.evaluateDetail
                  }
                },
                {
                  path: "/course/currentDetails/homeWorkEdited",
                  name: "homeWorkEdited",
                  component: () =>
                    import("@/views/course/components/homeWorkEdited.vue"),
                  meta: {
                    title: "实践感悟编辑",
                    idCode: reCodesList.homeWorkEdited
                  }
                },
                {
                  path: "/course/currentDetails/workDetail",
                  name: "workDetail",
                  component: () =>
                    import("@/views/course/components/workDetail.vue"),
                  meta: {
                    title: "实践感悟详情",
                    idCode: reCodesList.workDetail
                  }
                },
                {
                  path: "/course/currentDetails/relatedOrder",
                  name: "relatedOrder",
                  component: () =>
                    import("@/views/course/components/relatedOrders.vue"),
                  meta: {
                    title: "关联订单",
                    idCode: reCodesList.relatedOrder
                  }
                }
              ]
            },
            {
              path: "/course/coursePeriodEdite",
              name: "coursePeriodEdite",
              component: () => import("@/views/course/coursePeriodEdite.vue"),
              meta: {
                title: "编辑",
                idCode: reCodesList.coursePeriodEdite,
                showLink: false
              },
              children: [
                {
                  path: "/coursePeriodEdite/baseAdd",
                  name: "periodEditeBaseAdd",
                  component: () => import("@/views/institution/baseAdd.vue"),
                  meta: {
                    title: "新建基地",
                    idCode: reCodesList.periodBaseAdd,
                    showLink: false
                  }
                },
                {
                  path: "/coursePeriodEdite/leaderCreate",
                  name: "periodEditeLeaderCreate",
                  component: () => import("@/views/course/accountCreate.vue"),
                  meta: {
                    title: "新建领队",
                    idCode: reCodesList.periodLeaderCreate,
                    showLink: false
                  }
                },
                {
                  path: "/coursePeriodEdite/lecturerCreate",
                  name: "periodEditeLecturerCreate",
                  component: () => import("@/views/course/accountCreate.vue"),
                  meta: {
                    title: "新建讲师",
                    idCode: reCodesList.periodLecturerCreate,
                    showLink: false
                  }
                },
                {
                  path: "/coursePeriodEdite/teachersCreate",
                  name: "periodEditeTeachersCreate",
                  component: () =>
                    import("@/views/course/components/addTeachers.vue"),
                  meta: {
                    title: "添加师资",
                    idCode: reCodesList.periodTeachersCreate,
                    showLink: false
                  }
                }
              ]
            },
            {
              path: "/course/copyCourseSchedule",
              name: "CopyCourseSchedule",
              component: () => import("@/views/course/copyCourseSchedule.vue"),
              meta: {
                title: "复制课期",
                idCode: reCodesList.coursePeriodEdite,
                showLink: false
              }
            },
            {
              path: "/course/detailInformation",
              name: "detailInformation",
              component: () => import("@/views/course/detailInformation.vue"),
              meta: {
                title: "详细资料",
                idCode: reCodesList.detailInformation
              }
            },
            {
              path: "/course/coursePeriod/Create",
              name: "coursePeriodCreate",
              component: () => import("@/views/course/courseCreate.vue"),
              meta: {
                title: "新建课期",
                idCode: reCodesList.courseCreate,
                showLink: false
              }
            },
            {
              path: "/course/courseEdite",
              name: "courseEdite",
              component: () => import("@/views/course/courseEdite.vue"),
              meta: {
                title: "编辑基本信息",
                idCode: reCodesList.courseEdite
              }
            },
            {
              path: "/course/periodCreate",
              name: "PeriodCreate",
              component: () => import("@/views/course/coursePeriodCreate.vue"),
              meta: {
                title: "课期创建",
                idCode: reCodesList.periodCreate
                // keepAlive: true
              },
              children: [
                {
                  path: "/course/periodCreate/baseAdd",
                  name: "periodCreateBaseAdd",
                  component: () => import("@/views/institution/baseAdd.vue"),
                  meta: {
                    title: "新建基地",
                    idCode: reCodesList.periodBaseAdd,
                    showLink: false
                  }
                },
                {
                  path: "/course/periodCreate/leaderCreate",
                  name: "PeriodLeaderCreate",
                  component: () => import("@/views/course/accountCreate.vue"),
                  meta: {
                    title: "新建领队",
                    idCode: reCodesList.periodLeaderCreate,
                    showLink: false
                  }
                },
                {
                  path: "/course/periodCreate/lecturerCreate",
                  name: "PeriodLecturerCreate",
                  component: () => import("@/views/course/accountCreate.vue"),
                  meta: {
                    title: "新建讲师",
                    idCode: reCodesList.periodLecturerCreate,
                    showLink: false
                  }
                }
              ]
            },
            {
              path: "/course/periodCopy",
              name: "periodCopy",
              component: () => import("@/views/course/copyPeriod.vue"),
              meta: {
                title: "复制课期",
                idCode: reCodesList.periodCopy
              },
              children: [
                {
                  path: "/course/periodCopy/create",
                  name: "periodCopyCreate",
                  component: () =>
                    import("@/views/course/coursePeriodCreate.vue"),
                  meta: {
                    title: "课期创建",
                    idCode: reCodesList.periodCreate,
                    keepAlive: true
                  },
                  children: [
                    {
                      path: "/course/periodCopy/create/baseAdd",
                      name: "periodCopyBaseAdd",
                      component: () =>
                        import("@/views/institution/baseAdd.vue"),
                      meta: {
                        title: "新建基地",
                        idCode: reCodesList.periodBaseAdd,
                        showLink: false
                      }
                    },
                    {
                      path: "/course/periodCopy/create/leaderCreate",
                      name: "periodCopyLeaderCreate",
                      component: () =>
                        import("@/views/course/accountCreate.vue"),
                      meta: {
                        title: "新建领队",
                        idCode: reCodesList.periodLeaderCreate,
                        showLink: false
                      }
                    },
                    {
                      path: "/course/periodCopy/create/lecturerCreate",
                      name: "periodCopyLecturerCreate",
                      component: () =>
                        import("@/views/course/accountCreate.vue"),
                      meta: {
                        title: "新建讲师",
                        idCode: reCodesList.periodLecturerCreate,
                        showLink: false
                      }
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          path: "/course/courseCreate",
          name: "courseCreate",
          component: () => import("@/views/course/courseCreate.vue"),
          meta: {
            title: "新建课程",
            idCode: reCodesList.courseCreate,
            showLink: false
          },
          children: [
            {
              path: "/courseCreate/baseAdd",
              name: "courseCreateBaseAdd",
              component: () => import("@/views/institution/baseAdd.vue"),
              meta: {
                title: "新建基地",
                idCode: reCodesList.periodBaseAdd,
                showLink: false
              }
            },
            {
              path: "/courseCreate/leaderCreate",
              name: "courseCreateleaderCreate",
              component: () => import("@/views/course/accountCreate.vue"),
              meta: {
                title: "新建领队",
                idCode: reCodesList.periodLeaderCreate,
                showLink: false
              }
            },
            {
              path: "/courseCreate/lecturerCreate",
              name: "courseCreateLecturerCreate",
              component: () => import("@/views/course/accountCreate.vue"),
              meta: {
                title: "新建讲师",
                idCode: reCodesList.periodLecturerCreate,
                showLink: false
              }
            },
            {
              path: "/courseCreate/teachers",
              name: "courseCreateTeachers",
              component: () =>
                import("@/views/course/components/addTeachers.vue"),
              meta: {
                title: "添加师资",
                idCode: reCodesList.periodTeachersCreate,
                showLink: false
              },
              children: [
                {
                  path: "/courseCreate/teachers/details",
                  name: "courseCreateTeachersDetails",
                  component: () =>
                    import(
                      "@/views/teacherResource/teachersResourceDetail.vue"
                    ),
                  meta: {
                    title: "详情",
                    showLink: false
                    // idCode: reCodesList.joinCourse
                  }
                }
              ]
            }
          ]
        },
        {
          path: "/course/roleCreate",
          name: "roleCreate",
          component: () => import("@/views/course/accountCreate.vue"),
          meta: {
            title: "角色创建",
            idCode: reCodesList.roleCreate,
            showLink: false
          }
        },
        {
          path: "/course/allEvaluate",
          name: "allEvaluate",
          component: () => import("@/views/course/allEvaluate.vue"),
          meta: {
            title: "用户评价",
            idCode: coCodesList.allEvaluate,
            showLink: false
          },
          children: [
            {
              path: "/course/evaluateContent",
              name: "evaluateContent",
              component: () =>
                import("@/views/course/components/evaluateDetail.vue"),
              meta: {
                title: "评价详情",
                idCode: reCodesList.evaluateContent
              }
            }
          ]
        },
        {
          path: "/course/drafts",
          name: "Drafts",
          component: () => import("@/views/course/draftsList.vue"),
          meta: {
            title: "草稿箱",
            idCode: coCodesList.drafts,
            showLink: false
          },
          children: [
            {
              path: "/course/evaluateContent",
              name: "evaluateContent",
              component: () =>
                import("@/views/course/components/evaluateDetail.vue"),
              meta: {
                title: "评价详情",
                idCode: reCodesList.evaluateContent
              }
            }
          ]
        }
      ]
    },
    {
      path: "/course/course",
      name: "order",
      redirect: "/course/orderManage",

      // component: () => import("@/views/course/orderManage.vue"),
      meta: {
        title: "订单管理"
        // idCode: coCodesList.orderManage
      },
      children: [
        {
          path: "/course/orderManage",
          name: "OrderManage",
          component: () => import("@/views/course/orderManage.vue"),

          meta: {
            title: "订单管理",
            idCode: coCodesList.orderManage,
            keepAlive: true
          }
        },
        {
          path: "/course/orderDetails",
          name: "courseOrderDetails",
          component: () => import("@/views/course/orderDetails.vue"),
          meta: {
            title: "订单详情",
            idCode: reCodesList.courseOrderDetails,
            showLink: false
          },
          children: [
            {
              path: "/course/currentDetails/relatedCourse",
              name: "relatedCourse",
              component: () => import("@/views/course/currentDetails.vue"),
              // redirect: "/course/courseDetails/currentDetails/tableList",
              meta: {
                title: "关联课程",
                idCode: reCodesList.relatedCourse,
                keepAlive: true
              },
              children: [
                {
                  path: "/course/currentDetails/evaluateDetail",
                  name: "evaluateDetail",
                  component: () =>
                    import("@/views/course/components/evaluateDetail.vue"),
                  meta: {
                    title: "用户评价详情",
                    idCode: reCodesList.evaluateDetail
                  }
                },
                {
                  path: "/course/currentDetails/workDetail",
                  name: "workDetail",
                  component: () =>
                    import("@/views/course/components/workDetail.vue"),
                  meta: {
                    title: "实践感悟详情",
                    idCode: reCodesList.workDetail
                  }
                }
              ]
            }
          ]
        },
        {
          path: "/course/orderDetails",
          name: "orderDetails",
          component: () => import("@/views/course/orderDetails.vue"),
          meta: {
            title: "关联订单",
            idCode: reCodesList.orderDetails,
            showLink: false
          }
        }
      ]
    },
    {
      path: "/course/finance",
      name: "financeManage",
      redirect: "/course/financeManage",

      // component: () => import("@/views/course/financeManage.vue"),
      meta: {
        title: "财务管理"
        // idCode: coCodesList.financeManage,
        //keepAlive: true
      },
      children: [
        {
          path: "/course/financeManage",
          name: "FinanceManages",
          component: () => import("@/views/course/financeManage.vue"),
          meta: {
            title: "财务管理",
            idCode: coCodesList.financeManage,
            keepAlive: true
          }
        },
        {
          path: "/course/financeManage/orderDetail",
          name: "orderDetail",
          component: () => import("@/views/institution/orderDetails.vue"),
          meta: {
            title: "关联订单",
            idCode: reCodesList.orderDetails,
            showLink: true
          },
          children: [
            {
              path: "/course/financeManage/orderDetail/relatedCourse",
              name: "relatedCourses",
              component: () => import("@/views/course/currentDetails.vue"),
              // redirect: "/course/courseDetails/currentDetails/tableList",
              meta: {
                title: "关联课程",
                idCode: reCodesList.relatedCourse,
                keepAlive: true
              }
            }
          ]
        },
        {
          path: "/course/financeManage/bankAccount",
          name: "bankAccount",
          component: () => import("@/views/course/bankAccount.vue"),
          meta: {
            title: "银行账户信息",
            idCode: reCodesList.bankAccount,
            showLink: false,
            showParent: true,
            keepAlive: true
          }
        }
      ]
    }
    // {
    //   path: "/course/market",
    //   name: "courseMarket",
    //   redirect: "/course/marketIndex",
    //   meta: {
    //     title: "课程市场"
    //   },
    //   children: [
    //     {
    //       path: "/course/marketIndex",
    //       name: "courseMarketIndex",
    //       component: () => import("@/views/course/courseMarket.vue"),
    //       meta: {
    //         title: "课程市场",
    //         idCode: coCodesList.courseMarket,
    //         keepAlive: true,
    //         showLink: true // 菜单显示
    //       }
    //     },
    //     {
    //       path: "/course/market/detailInstitution",
    //       name: "detailInstitution",
    //       component: () =>
    //         import("@/views/course/components/InstitutionReleaseDetails.vue"),
    //       meta: {
    //         title: "机构发布详情",
    //         showLink: false
    //       }
    //     },
    //     {
    //       path: "/course/market/detailFaculty",
    //       name: "detailFaculty",
    //       component: () =>
    //         import("@/views/course/components/facultyReleaseDetails.vue"),
    //       meta: {
    //         title: "师资发布详情",
    //         showLink: false
    //       }
    //     },
    //     {
    //       path: "/course/market/detailRecruit",
    //       name: "detailRecruit",
    //       component: () =>
    //         import("@/views/course/components/recruitmentDemandDetails.vue"),
    //       meta: {
    //         title: "招募详情",
    //         showLink: false
    //       }
    //     },
    //     {
    //       path: "/course/market/currentDetails",
    //       name: "currentDetailsList",
    //       component: () => import("@/views/course/currentDetails.vue"),
    //       meta: {
    //         title: "课程详情",
    //         showLink: false,
    //         idCode: coCodesList.relatedCourse
    //       }
    //     },
    //     {
    //       path: "/course/market/PublishCourses",
    //       name: "PublishCourses",
    //       component: () => import("@/views/teacherResource/inviteCourse.vue"),
    //       meta: {
    //         title: "发布课程",
    //         showLink: false
    //       }
    //     }
    //   ]
    // }
  ]
};
