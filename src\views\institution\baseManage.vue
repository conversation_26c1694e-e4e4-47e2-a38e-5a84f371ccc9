<script setup>
import { onMounted, ref, reactive } from "vue";
import { formatTime } from "@/utils/index";
import { complexIsFreeze, complexFindAll } from "@/api/institution";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import dayjs from "dayjs";
defineOptions({
  name: "BaseManage"
});
const columns = [
  {
    label: "基地名", // 如果需要表格多选，此处label必须设置
    prop: "name",
    minWidth: 90,
    formatter: ({ name }) => {
      return name || "--";
    }
  },
  {
    label: "机构名",
    prop: "organizationName",
    minWidth: 90,
    formatter: ({ organizationName }) => {
      return organizationName || "--";
    }
  },
  {
    label: "创建时间",
    minWidth: 90,
    prop: "createdAt",
    formatter: ({ createdAt }) => {
      return createdAt ? dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss") : "--";
    }
  },
  {
    label: "基地状态",
    minWidth: 90,
    prop: "freeze",
    slot: "freeze"
  },
  {
    label: "操作",
    fixed: "right",
    width: 220,
    slot: "operation"
  }
];
const pagination = reactive({
  total: 0,
  pageSize: 15,
  currentPage: 1,
  background: true
});

onMounted(() => {
  // courseTypeFindApi();
  getTableList();

  // tableData.value = Array.from({ length: 20 }).fill({
  //   completed_at: "0",
  //   pending_review: 1,
  //   createdAt: 172627952334,
  //   name: "2024-09-14习题任务",
  //   termNumber: 1,
  //   courseTypeName: "手工",
  //   state: 1,
  //   organizationName: "2024-09-14习题任务",
  //   freeze: 1
  // });
});
const router = useRouter();
// 搜索表单
const form = ref({
  startTime: "",
  endTime: "",
  name: "",
  organizationName: "",
  courseTypeName: "",
  freeze: "all"
});
// 表格数据
const tableData = ref([]);
const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});
const courseTypeoptions = ref([
  {
    value: 0,
    label: "全部"
  }
]);
const courseTypeFindApi = async () => {
  const params = {
    depth: 1
  };
  // let [err, res] = await requestTo(courseTypeFind(params));
  // console.log("🐠res------------------------------>", res);
  // let res1 = res.map(it => {
  //   return {
  //     ...it,
  //     label: it.name,
  //     value: it.id
  //   };
  // });
  // courseTypeoptions.value = courseTypeoptions.value.concat(res1);
  // console.log(
  //   "🌵courseTypeoptions.value------------------------------>",
  //   courseTypeoptions.value
  // );
};
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async item => {
  if (getListLoading.value) return;
  // return;
  getListLoading.value = true;
  // let paramsData = {
  //   page: params.value.page - 1,
  //   size: params.value.size,
  //   sort: "createdAt,desc",
  // };
  let paramsData = {
    page: pagination.currentPage - 1,
    size: pagination.pageSize,
    sort: "createdAt,desc"
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      // 对于冻结状态的特殊处理
      if (paramsDataKey === "freeze") {
        if (form.value[paramsDataKey] === "all") {
          // 如果是全部，则跳过，不发送该参数
          continue;
        } else {
          // 如果是正常或冻结状态，则发送对应参数
          paramsData[paramsDataKey] = form.value[paramsDataKey];
        }
      } else if (form.value[paramsDataKey]) {
        // 其他非freeze参数，有值才发送
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  console.log("🍧-----paramsData-----", paramsData);
  const { code, data, msg } = await complexFindAll(paramsData);
  console.log("🎁-----result-----", data);
  if (data) {
    tableData.value = data?.content;

    // params.value.totalElements = data.totalElements;
    pagination.total = data.totalElements;
  }
  getListLoading.value = false;
};
//分页
const handleCurrentChange = e => {
  // params.value.page = e;
  pagination.currentPage = e;
  getTableList();
};
const handleSizeChange = val => {
  pagination.currentPage = 1;
  pagination.pageSize = val;
  // params.value.page = 1;
  // params.value.size = val;
  getTableList();
};
// 编辑
const edit = item => {
  // router.push({ path: "/institution/baseEdit", query: { data: JSON.stringify(item) } });
  router.push({
    path: "/institution/baseDetails",
    query: { id: item.id }
  });
};
// 账务
const getId = id => {
  router.push({ path: "/institution/accounting", query: { id: 1 } });
};
//搜索
const searchData = () => {
  params.value.page = 1;
  getTableList();
};
// 重置
const setData = () => {
  form.value = {
    startTime: "",
    endTime: "",
    name: "",
    organizationName: "",
    courseTypeName: "",
    freeze: "all" // 重置时将freeze重新设为'all'（全部）
  };
  // params.value.page = 1;
  pagination.currentPage = 1;
  value1.value = [];
  getTableList(); // 添加这行确保重置后立即刷新数据
};
// 选择时间
const timeChange = value => {
  console.error(value);
  if (!value || value.length !== 2) {
    form.value.startTime = form.value.endTime = null;
    return;
  }

  // 开始时间（默认 00:00:00）
  form.value.startTime = new Date(value[0]).getTime();

  // 结束时间（设置为 23:59:59.999）
  const endDate = new Date(value[1]);
  endDate.setHours(23, 59, 59, 999); // 关键修改
  form.value.endTime = endDate.getTime();
};

const value1 = ref([]);
// 前往创建
const goSet = () => {
  router.push({
    path: "/institution/baseAdd",
    query: { title: "jsMang", id: 1 }
  });
};
// 冻结/解冻
const getButtonText = isPub => {
  // console.log("🐳-----isPub-----", isPub);
  return isPub === true ? "解冻" : "冻结";
};
const Freeze = async row => {
  console.log("🍭-----row-----", row);
  const isFreezing = !row.freeze;
  const confirmText = isFreezing
    ? "你确定要冻结该基地吗?"
    : "你确定要解冻该基地吗?";
  const confirmTitle = isFreezing ? "确定冻结" : "确定解冻";
  const successMessage = isFreezing ? "已冻结" : "已解冻";

  try {
    await ElMessageBox.confirm(confirmText, confirmTitle, {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: ""
    });

    const params = {
      id: row.id,
      freeze: isFreezing
    };
    const operateLog = {
      operateLogType: "COMPLEX_MANAGEMENT",
      operateType: isFreezing ? "冻结了" : "解冻了",
      operatorTarget: `“${row.name}”的基地状态`
    };
    console.log("🐳-----operateLog-----", operateLog);
    const { code, msg } = await complexIsFreeze(params, operateLog);

    if (code === 200) {
      ElMessage({
        message: successMessage,
        type: "success"
      });

      getTableList();
    } else {
      ElMessage.error(msg);
    }
  } catch (error) {
    // console.log("操作取消");
  }
};
const defaultTime = [
  new Date(2024, 4, 1, 0, 0, 0),
  new Date(2024, 4, 1, 23, 59, 59)
];
const loadingTable = ref(false);
</script>

<template>
  <div>
    <div class="common">
      <!-- <div class="con_top">
        <div class="titles">基地管理</div>
        <el-button type="primary" @click="goSet">创建基地</el-button>
      </div> -->
      <div class="con_search">
        <el-form :model="form" label-width="80px" :inline="true">
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="value1"
              type="daterange"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              :default-time="defaultTime"
              @change="timeChange"
            />
          </el-form-item>
          <el-form-item label="基地名">
            <el-input v-model.trim="form.name" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="机构名">
            <el-input
              v-model.trim="form.organizationName"
              placeholder="请输入"
              clearable
            />
          </el-form-item>
          <el-form-item label="基地状态">
            <el-select
              v-model="form.freeze"
              style="width: 120px"
              clearable
              @clear="form.freeze = 'all'"
            >
              <el-option label="全部" :value="'all'" />
              <el-option label="正常" :value="false" />
              <el-option label="冻结" :value="true" />
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="课程类型">
          <el-select
            v-model="form.courseTypeName"
            style="width: 120px"
            placeholder="请选择"
            :empty-values="[null, undefined]"
            :value-on-clear="null"
          >
            <el-option
              v-for="item in courseTypeoptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item> -->

          <el-form-item label=" ">
            <div class="flex">
              <el-button type="primary" @click="searchData">搜索</el-button>
              <el-button @click="setData">重置</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="containers">
      <div class="puretable">
        <pure-table
          ref="tableRef"
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="left"
          table-layout="auto"
          :loading="loadingTable"
          :data="tableData"
          :columns="columns"
          :pagination="{ ...pagination }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #freeze="{ row }">
            <div :style="{ color: row.freeze === true ? 'red' : '' }">
              {{ row.freeze === true ? "冻结" : "正常" }}
            </div>
          </template>
          <template #operation="{ row }">
            <div class="botlist">
              <div class="u">
                <el-button
                  v-code="['618']"
                  class="reset-margin"
                  link
                  type="primary"
                  @click="edit(row)"
                >
                  详情
                </el-button>
              </div>
              <div class="u">
                <el-button
                  type="primary"
                  link
                  :style="{
                    color: row.freeze === true ? 'red' : '#409EFF'
                  }"
                  @click="Freeze(row)"
                >
                  {{ getButtonText(row.freeze) }}
                </el-button>
              </div>
            </div>
          </template>
        </pure-table>
      </div>

      <!-- <div class="con_table">
        <el-table :data="tableData" height="67vh">
          <el-table-column prop="name" label="基地名" min-width="120">
            <template #default="scope">
              <el-text class="w-300px mb-2" truncated>
                {{ scope.row.name || "--" }}
              </el-text>
            </template>
          </el-table-column>
          <el-table-column prop="organizationName" label="机构名" align="left">
            <template #default="scope">
              <div>
                {{ scope.row.alias || "--" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column width="200px" prop="createdAt" label="创建时间">
            <template #default="scope">
              <div>
                {{
                  formatTime(scope.row?.createdAt, "YYYY-MM-DD HH:mm") || "--"
                }}
              </div>
            </template>
          </el-table-column> -->
      <!-- <el-table-column prop="termNumber" label="管理员" align="left">
          <template #default="scope">
            <div>
              {{ scope.row.organizationAdmin.name || "--" }}
            </div>
          </template>
        </el-table-column> -->
      <!-- <el-table-column
            fixed="right"
            align="left"
            label="操作"
            width="200px"
          >
            <template #default="scope">
              <div class="button">
                <div class="btnse" @click="edit(scope.row)">详情</div> -->
      <!-- <div class="btnse" @click="getId(scope.row.id)">账务</div> -->
      <!-- <el-button
                  type="primary"
                  link
                  :style="{
                    color: scope.row.freeze === true ? 'red' : '#409EFF'
                  }"
                  @click="Freeze(scope.row)"
                >
                  {{ getButtonText(scope.row.freeze) }}
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div> -->
      <!-- <div class="con_pagination"> -->
      <!-- 分页 -->
      <!-- <el-pagination
          v-model:current-page="params.page"
          v-model:page-size="params.size"
          class="pagination"
          background
          layout="total, prev, pager, next, jumper"
          :total="params.totalElements"
          @current-change="handleCurrentChange"
        />
      </div> -->
    </div>
  </div>
</template>

<style lang="scss" scoped>
.common {
  width: 100%;
  height: 100%;
  padding: 20px 20px 2px 20px;
  background-color: #fff;
  margin-bottom: 20px;
  .con_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: fit-content;
    margin-bottom: 24px;
    .titles {
      font-size: 20px;
      font-weight: bold;
      color: #606266;
    }
  }

  .con_search {
    display: flex;
    align-items: center;
    width: 100%;
    height: fit-content;
  }
}
.botlist {
  min-width: 120px;
  display: flex;
  justify-content: left;
  .u {
    width: 60px;
  }
}

.containers {
  box-sizing: border-box;
  // width: calc(100% - 48px);
  // height: 100%;
  padding: 20px 20px 2px;
  background: #fff;

  .con_table {
    width: calc(100%);
    margin-bottom: 24px;
    // margin-left: 25px;
    .button {
      display: flex;
      // justify-content: space-around;
      .btnse {
        color: #409eff;
        cursor: pointer;
        padding: 0 5px;
      }
    }
  }

  .con_pagination {
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }
}
</style>
