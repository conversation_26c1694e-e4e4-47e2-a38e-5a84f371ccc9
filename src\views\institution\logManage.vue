<script setup>
import { ref, onMounted, nextTick } from "vue";
import { formatTime } from "@/utils/index";
import { useRouter, useRoute } from "vue-router";
import { operateLogFindAll, operateLogSave } from "@/api/institution.js";
import { ElMessage, ElMessageBox } from "element-plus";
import { requestTo } from "@/utils/http/tool";
import { decrypt, encryption } from "@/utils/SM4.js";
import { View, Hide } from "@element-plus/icons-vue";
const router = useRouter();
const route = useRoute();
onMounted(() => {
  getTableList();
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});

// 搜索表单
const form = ref({
  startTime: "",
  endTime: "",
  name: "",
  phone: "",
  detail: "", // 添加操作描述字段
  operateLogType: []
});

const citiesType = {
  机构管理: "ORGANIZATIONAL_MANAGEMENT",
  基地管理: "COMPLEX_MANAGEMENT",
  课程管理: "COURSE_MANAGEMENT",
  订单管理: "ORDER_MANAGEMENT",
  财务管理: "FINANCE_MANAGEMENT",
  领队管理: "LEADER_MANAGEMENT",
  讲师管理: "LECTURER_MANAGEMENT",
  学生管理: "STUDENT_MANAGEMENT",
  账号管理: "ACCOUNT_MANAGEMENT",
  权限管理: "AUTHORITY_MANAGEMENT",
  平台设置: "PLATFORM_SETTINGS"
};

const cities = [
  "机构管理",
  "基地管理",
  "课程管理",
  "订单管理",
  "财务管理",
  "领队管理",
  "讲师管理",
  "账号管理",
  "权限管理"
];

const checkAll = ref(true);
const isIndeterminate = ref(false);
const checkedCities = ref([...cities]); //默认选中

function getKeyByValue(value) {
  for (const [key, val] of Object.entries(citiesType)) {
    if (val === value) {
      return key;
    }
  }
  return null; // 如果没有找到对应的值，返回 null
}
const handleCheckAllChange = val => {
  checkedCities.value = val ? cities : [];
  citiesData(checkedCities.value);
  // console.log('🎁-----checkedCities.value-----', checkedCities.value);
  isIndeterminate.value = false;
};
const handleCheckedCitiesChange = value => {
  console.log("🐬-----value-----", value);
  const checkedCount = value.length;
  checkAll.value = checkedCount === cities.length;
  isIndeterminate.value = checkedCount > 0 && checkedCount < cities.length;
  citiesData(value);
};

const citiesData = value => {
  form.value.operateLogType = [];
  for (let index = 0; index < value.length; index++) {
    const element = value[index];
    citiesType[element];
    form.value.operateLogType.push(citiesType[element]);
  }
  form.value.operateLogType = form.value.operateLogType.join();
  console.log("🐬-----isIndeterminate.value-----", form.value.operateLogType);
};
// 表格数据
const tableData = ref([
  // {
  //   id: 0,
  //   createdAt: 0,
  //   updatedAt: 0,
  //   name: "1223",
  //   organizationName: "dd ",
  //   courseTypeName: "fff",
  //   termNumber: 0
  // }
]);
const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = paramsData[paramsDataKey] =
          paramsDataKey === "phone"
            ? encryption(String(form.value[paramsDataKey]))
            : form.value[paramsDataKey];
      }
    }
  }
  console.log("🍧-----paramsData-----", paramsData);
  // return;
  try {
    const { code, data, msg } = await operateLogFindAll(paramsData);
    // console.log("🎁-----data-----", code, data, msg);
    if (code == 200) {
      tableData.value = data?.content;
      params.value.totalElements = data?.totalElements;
    }
  } catch (error) {
    console.log("catch-----error-----", error);
  }
  getListLoading.value = false;
};
const eye_phone = async () => {
  let paramsData = {
    operateLogType: "ORGANIZATIONAL_MANAGEMENT",
    detail: "新建基地"
    // additionalParameter: ''
  };
  try {
    const { code, data, msg } = await operateLogSave(paramsData);
    // console.log("🎁-----data-----", code, data, msg);
    if (code == 200) {
      tableData.value = data?.content;
      params.value.totalElements = data?.totalElements;
    }
  } catch (error) {
    console.log("catch-----error-----", error);
  }
};
// const eye_card = id => {
//   const item = tableData.value.find(item => item.id === id);
//   if (item) {
//     item.show_card = !item.show_card;
//   }
// };
//搜索
const searchData = () => {
  params.value.page = 1;
  getTableList();
};
// 选择时间
const timeChange = value => {
  if (!value || value.length !== 2) return;

  // 开始时间（默认 00:00:00）
  form.value.startTime = new Date(value[0]).getTime();

  // 结束时间（设置为 23:59:59.999）
  const endDate = new Date(value[1]);
  endDate.setHours(23, 59, 59, 999); // 关键修改
  form.value.endTime = endDate.getTime();
};

// 清除时间
const clearEvt = () => {
  form.value.startTime = "";
  form.value.endTime = "";
};

const value1 = ref([]);
// 重置
const setData = () => {
  // eye_phone()
  // return
  form.value = {
    startTime: "",
    endTime: "",
    name: "",
    phone: "",
    detail: "",
    operateLogType: []
  };
  params.value.page = 1;
  value1.value = [];
  checkedCities.value = [...cities];
  checkAll.value = true;
  isIndeterminate.value = false;
  getTableList();
};
//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTableList();
};
const handleSizeChange = val => {
  params.value.page = 1;
  params.value.size = val;
  getTableList();
};
// 讲师详情
const getInfoid = row => {
  router.push({
    path: "/account/teacherDetails",
    query: { title: "jsMang", id: row.id }
  });
};
// 冻结/解冻
const getButtonText = isPub => {
  return isPub === true ? "解冻" : "冻结";
};
const Freeze = async row => {
  const isFreezing = !row.freeze;
  const confirmText = isFreezing
    ? "你确定要冻结该账户吗?"
    : "你确定要解冻该账户吗?";
  const confirmTitle = isFreezing ? "确认冻结" : "确认解冻";
  const successMessage = isFreezing ? "已冻结" : "已解冻";

  try {
    await ElMessageBox.confirm(confirmText, confirmTitle, {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: ""
    });
    const params = {
      id: row.id,
      freeze: isFreezing
    };
    const { code, msg } = await isFreeze(params);
    if (code === 200) {
      ElMessage({
        message: successMessage,
        type: "success"
      });

      getTableList();
    } else {
      ElMessage.error(msg);
    }
  } catch (error) {
    // console.log("操作取消");
  }
};

// 计算表格高度
const searchFormHeight = ref(0);
const tableHeight = ref("calc(100vh - 400px)");

const calculateTableHeight = async () => {
  await nextTick();
  const searchForm = document.querySelector(".con_search");
  if (searchForm) {
    searchFormHeight.value = searchForm.offsetHeight;
    // 动态计算表格高度，减去搜索框高度、表头按钮高度、分页器高度和其他间距
    tableHeight.value = `calc(100vh - 240px - ${searchFormHeight.value}px)`;
  }
};
</script>

<template>
  <div class="containers">
    <div class="con_search">
      <el-form :model="form" label-width="100px" :inline="true">
        <el-form-item label="操作时间">
          <el-date-picker
            v-model="value1"
            type="daterange"
            start-placeholder="请选择开始时间"
            end-placeholder="请选择结束时间"
            @change="timeChange"
            @clear="clearEvt"
          />
        </el-form-item>
        <el-form-item label="操作人">
          <el-input
            v-model.trim="form.name"
            placeholder="请输入"
            clearable
            style="width: 180px"
          />
        </el-form-item>
        <el-form-item label="操作描述">
          <el-input
            v-model.trim="form.detail"
            placeholder="请输入操作描述"
            clearable
            style="width: 240px"
          />
        </el-form-item>
        <div style="display: flex">
          <el-form-item label="操作类型">
            <el-checkbox
              v-model="checkAll"
              :indeterminate="isIndeterminate"
              @change="handleCheckAllChange"
            >
              全部
            </el-checkbox>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <el-checkbox-group
              v-model="checkedCities"
              @change="handleCheckedCitiesChange"
            >
              <el-checkbox
                v-for="city in cities"
                :key="city"
                :label="city"
                :value="city"
              >
                {{ city }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label=" ">
            <div class="flex">
              <el-button type="primary" @click="searchData">搜索</el-button>
              <el-button @click="setData">重置</el-button>
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <el-scrollbar class="scrollbar">
      <div class="con_table">
        <!-- :row-style="{ height: '70px' }" -->

        <el-table
          :data="tableData"
          table-layout="fixed"
          :header-cell-style="{ backgroundColor: '#fafafa', color: '#565353' }"
          highlight-current-row
          :max-height="tableHeight"
          :row-style="{ height: '50px' }"
        >
          <el-table-column width="160px" prop="id" label="编号">
            <template #default="scope">
              <el-text>
                {{ scope.row.id || "--" }}
              </el-text>
            </template>
          </el-table-column>
          <el-table-column width="240px" prop="createdAt" label="操作时间">
            <template #default="scope">
              <div>
                {{
                  formatTime(scope.row?.createdAt, "YYYY-MM-DD HH:mm") || "--"
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            width="220px"
            prop="operateLogType"
            label="操作类型"
            align="left"
          >
            <template #default="scope">
              <div>
                {{ getKeyByValue(scope.row.operateLogType) || "--" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            width="200px"
            prop="username"
            label="操作人员"
            align="left"
          >
            <template #default="scope">
              <div>
                {{ scope.row.username || "--" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            width="500px"
            prop="detail"
            label="操作描述"
            align="left"
          >
            <template #default="scope">
              <div>
                {{ scope.row.detail || "--" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="ip" label="操作IP" align="left">
            <template #default="scope">
              <div>
                {{ scope.row.ip || "--" }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-scrollbar>

    <div class="con_pagination">
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="params.page"
        v-model:page-size="params.size"
        class="pagination"
        background
        :page-sizes="[15, 30, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="params.totalElements"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.scrollbar {
  height: calc(100vh - 330px);
  background-color: #fff;
  padding: 20px 20px 0 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
:deep(.el-form-item__label) {
  width: 68px !important;
}
.containers {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 89vh;
  overflow: hidden;
  // width: calc(100% - 48px);
  // height: 100%;
  // padding: 24px;
  // background: #fff;

  .con_search {
    display: flex;
    align-items: center;
    // width: 100%;
    height: fit-content;
    background: #fff;
    padding: 20px;
    padding-bottom: 0px;
    margin-bottom: 20px;
  }

  .con_table {
    // width: calc(100% - 25px);
    // margin-bottom: 24px;
    // margin-left: 25px;

    .btnse {
      color: #409eff;
    }
  }

  .con_pagination {
    display: flex;
    justify-content: flex-end;
    // margin-top: 20px;
    width: 100%;
    background: #fff;
    padding: 0px 20px 20px 20px;
    flex-wrap: wrap;

    :deep(.el-pagination) {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-end;
      row-gap: 10px;

      .el-pagination__sizes,
      .el-pagination__jump {
        margin-bottom: 0;
      }
    }
  }
  .eye_style {
    display: flex;
    align-items: center;
    justify-content: center;
    .eye {
      margin-left: 20px;
      cursor: pointer;
      :hover {
        color: #409eff;
      }
    }
  }
}
</style>
