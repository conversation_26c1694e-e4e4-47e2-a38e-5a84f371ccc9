<script setup>
import { ref, reactive, onMounted, computed, onActivated } from "vue";
import { nanoid } from "nanoid";
import {
  complexId,
  courseTypeFind,
  leaderLecturerFind,
  coursePeriodAdd,
  coursePeriodFind,
  courseFindId
} from "@/api/course.js";
import { findAllCourseType, coursePeriodCopy } from "@/api/period.js";
import { requestTo } from "@/utils/http/tool";
import { Plus } from "@element-plus/icons-vue";
import {
  uploadFile,
  validateFileType,
  isOverSizeLimit
} from "@/utils/upload/upload.js";
import { formatTime } from "@/utils/index";
import { useRouter, useRoute } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import dayjs from "dayjs";
import weekday from "dayjs/plugin/weekday";
import { number } from "echarts";
import { courseStore } from "@/store/modules/course.js";
import { to } from "@iceywu/utils";

defineOptions({
  name: "PeriodCreate"
});
const useCourseStore = courseStore();
const router = useRouter();
const route = useRoute();
const ruleForm = reactive({
  name: "",
  courseTypeId: "",
  complexId: "",
  // minPeopleNumber: "",
  leaderIds: "",
  maxPeopleNumber: "",
  lecturerIds: "",
  cover: []
});
// 人数限制校验
const minPeoplePass = (rule, value, callback) => {
  if (value <= 0) {
    callback(new Error("请输入正确的人数限制"));
  } else {
    callback();
  }
};
const maxPeoplePass = (rule, value, callback) => {
  console.log("🎉-----value-----", value);

  // console.log("🐬-----ruleForm.minPeopleNumber-----", ruleForm.minPeopleNumber);

  if (value <= 0) {
    callback(new Error("请输入正确的人数限制"));
  }
  // else if (value < Number(ruleForm.minPeopleNumber)) {
  //   callback(new Error("人数上限不能低于人数下限"));
  // }
  else {
    callback();
  }
};
const rules = reactive({
  name: [{ required: true, message: "请输入课期名称", trigger: "blur" }],
  courseTypeId: [
    { required: true, message: "请选择课程类型", trigger: "blur" }
  ],
  complexId: [{ required: true, message: "请选择基地", trigger: "blur" }],
  leaderIds: [{ required: true, message: "请选择领队", trigger: "blur" }],
  lecturerIds: [{ required: true, message: "请选择讲师", trigger: "blur" }],
  // minPeopleNumber: [{ validator: minPeoplePass, trigger: "blur" }],
  maxPeopleNumber: [
    {
      pattern: /^\d+$/,
      message: "必须为整数，不允许小数或负数",
      trigger: "blur"
    }
  ]
});

const formData = ref([
  {
    label: "课期名",
    type: "input",
    typeInput: "text",
    prop: "name",
    check: true,
    placeholder: "请输入课期名",
    width: "100%"
  },
  {
    label: "课程类型",
    type: "cascader",
    prop: "courseTypeId",
    check: true,
    placeholder: "请选择课程类型",
    width: "50%",
    options: []
  },
  {
    label: "基地",
    type: "select",
    prop: "complexId",
    check: true,
    placeholder: "请选择基地",
    width: "50%",
    options: [],
    text: "新建基地"
  },
  // {
  //   label: "人数下限",
  //   type: "input",
  //   typeInput: "number",
  //   prop: "minPeopleNumber",
  //   check: false,
  //   placeholder: "请输入人数下限",
  //   width: "240px"
  // },
  {
    label: "人数上限",
    type: "input",
    typeInput: "number",
    prop: "maxPeopleNumber",
    check: false,
    placeholder: "请输入人数上限",
    width: "50%"
  },
  {
    label: "领队",
    type: "selectMultiple",
    prop: "leaderIds",
    check: true,
    placeholder: "请选择领队",
    width: "50%",
    options: [],
    text: "新建领队"
  },
  {
    label: "讲师",
    type: "selectMultiple",
    prop: "lecturerIds",
    check: true,
    placeholder: "请选择讲师",
    width: "50%",
    options: [],
    text: "新建讲师"
  }
]);

//课程分类查询不分页
const courseTypeFindApi = async () => {
  let [err, res] = await requestTo(findAllCourseType());
  if (res) {
    formData.value[1].options = transformArray(res);
  }
};
function transformArray(inputArray) {
  return inputArray.map(item => {
    const newItem = {
      value: item.id,
      label: item.name
    };

    // 如果有 children，则递归转换
    if (item.children && item.children.length > 0) {
      newItem.children = transformArray(item.children);
    }

    return newItem;
  });
}
const courseTypeChange = val => {
  ruleForm.courseTypeId = val[val.length - 1];
};
//  基地查询不分页
const complexIdApi = async () => {
  let [err, res] = await requestTo(complexId());
  if (res) {
    formData.value[2].options = res.map(it => {
      return {
        ...it,
        label: it.name,
        value: it.id
      };
    });
  }
};
const dialogImageUrl = ref("");
const dialogVisible = ref(false);
const handlePictureCardPreview = uploadFile => {
  dialogImageUrl.value = uploadFile.url;
  dialogVisible.value = true;
};
const fileList = ref([]);
// 图片上传
const beforeUpload = async file => {
  // const fileType = file.type?.split("/")[0];
  // if (fileType == "image") {
  let isSize = isOverSizeLimit(file, 10);
  if (isSize.valid) {
    try {
      await uploadFile(
        file,
        progress => {
          // 构造用于 el-upload 展示的文件对象
          const currentFile = {
            name: file.name,
            uid: file.uid,
            status: progress.status || "uploading",
            percentage: progress.percent || 0
          };

          // 上传成功，补充 url 字段
          if (progress.status === "success" && progress.data?.url) {
            currentFile.url = progress.data.url;

            // 更新 ruleForm.cover，确保无 Proxy
            if (progress.data.fileIdentifier) {
              ruleForm.cover = ruleForm.cover.filter(
                item => item.uid !== file.uid
              );
              ruleForm.cover.push({
                fileIdentifier: progress.data.fileIdentifier,
                fileType: "PHOTO",
                uid: file.uid
              });
            }
          }

          // 失败时移除，成功/上传中则更新
          fileList.value = fileList.value.filter(f => f.uid !== file.uid);
          if (progress.status === "fail") {
            ElMessage.error(progress.errMessage || "上传失败，请重试");
          } else {
            // 用浅拷贝，避免 Proxy
            fileList.value = [...fileList.value, { ...currentFile }];
          }
        },
        ["image"]
      );
    } catch (error) {
      ElMessage.error(error.message || "上传过程中发生错误");
      fileList.value = fileList.value.filter(f => f.uid !== file.uid);
    }

    // if (code === 200) {
    //   ruleForm.cover.push({
    //     fileIdentifier: data.fileIdentifier,
    //     fileType: "PHOTO",
    //     uid: file.uid,
    //     url: data.url,
    //     fileName: data.fileName
    //   });
    // }
  } else {
    ElMessage.error(isSize.message);
  }
  // } else {
  //   ElMessage.error("请上传图片");
  //   return false;
  // }
};
//删除图片视频
const handleRemove = (uploadFile, uploadFiles) => {
  // console.log("🌈-----uploadFile-----", uploadFile);
  ruleForm.cover = ruleForm.cover.filter(it => it.uid !== uploadFile.uid);
};

const handleChange = value => {
  console.log(value);
};
// 领队讲师查询
const leaderFindApi = async type => {
  const params = {
    roleId: type
  };
  let [err, res] = await requestTo(leaderLecturerFind(params));
  if (res) {
    if (type == 2) {
      formData.value[5].options = res.map(it => {
        return {
          ...it,
          label: it.name,
          value: it.id
        };
      });
    } else {
      formData.value[4].options = res.map(it => {
        return {
          ...it,
          label: it.name,
          value: it.id
        };
      });
    }
  } else {
    console.log("🌵-----err-----", err);
  }
};
// 开课时间设置表格数据
const tableData = ref([]);
// 选择添加的日期
const selectDate = ref();

// 添加日期
const setData = () => {
  selectedDates.value?.forEach(item => {
    tableData.value.push({
      openTime: dayjs(item).format("YYYY-MM-DD"),
      weekDay: dayjs(item).format("dddd"),
      classStart: "",
      signUpDeadline: 0,
      sortTime: dayjs(item).valueOf(),
      valueId: nanoid()
    });
  });
};
const addDAte = () => {
  if (route.query.type === "new") {
    // selectedDates.value
    // console.log('🍧selectedDates.value--------333---------------------->',tableData.value);
    if (tableData.value.length >= 1) {
      ElMessage.error("全新创建只能创建一个课期");
      return;
    }
  }
  dialogVisiblesc.value = true;
};
// 删除课期
const getDelted = val => {
  tableData.value.splice(val, 1);
};
// 创建
const isNeedTip = ref(false);
const submitLoading = ref(false);
const submitForm = async formEl => {
  if (!formEl) return;
  if (submitLoading.value) return;
  submitLoading.value = true;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      // if (Number(ruleForm.minPeopleNumber) > Number(ruleForm.maxPeopleNumber)) {
      //   ElMessage.error("人数上限不能低于人数下限");
      //   submitLoading.value = false;
      //   return;
      // }
      const params = { ...ruleForm };

      params.courseId = Number(route.query.id);
      // params.minPeopleNumber = Number(ruleForm.minPeopleNumber);
      params.maxPeopleNumber = Number(ruleForm.maxPeopleNumber);
      // console.log("🎁-----params-----", params);
      if (!params?.cover?.length) {
        delete params?.cover;
      } else {
        params.cover = ruleForm.cover.map((it, index) => {
          return {
            sortOrder: index + 1,
            fileIdentifier: it.fileIdentifier,
            fileType: it.fileType || "PHOTO"
          };
        });
      }
      let coursePeriodTimes = [];
      if (tableData.value?.length) {
        tableData.value.forEach(item => {
          if (item?.openTime) {
            const times =
              item.openTime + " " + dayjs(item.classStart).format("HH:mm:ss");
            if (!dayjs(times).valueOf()) {
              ElMessage.error("请选择开课时间");
              submitLoading.value = false;
              isNeedTip.value = true;
              return;
            }
            if (
              dayjs(times).valueOf() &&
              dayjs(times).valueOf() <= dayjs().valueOf()
            ) {
              ElMessage.error("开课时间不能早于或等于当前时间");
              submitLoading.value = false;
              isNeedTip.value = true;
              return;
            }
            const signUpDeadline =
              dayjs(times).valueOf() - item.signUpDeadline * 60 * 60 * 1000;

            coursePeriodTimes.push({
              openTime: dayjs(times).valueOf(),
              signUpDeadline
            });
          } else {
            isNeedTip.value = true;
          }
        });
        if (isNeedTip.value) {
          // ElMessage.error("请给开课日期设置对应的上课时间！");
          isNeedTip.value = false;
          submitLoading.value = false;
          return;
        }
      } else {
        ElMessage.error("请设置开课时间！");
        submitLoading.value = false;
        return;
      }
      if (coursePeriodTimes?.length) {
        params.coursePeriodTimes = coursePeriodTimes;
      }
      // console.log("🎉-----params-22----", params);
      if (route.query.type === "copy") {
        params.copyCoursePeriodId = route.query.copyId;
      }
      const operateLog = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `创建了“${ruleForm.name}”课期`
        // operatorTarget: form.value.name,
      };
      let [err, res] =
        route.query.type === "copy"
          ? await to(coursePeriodCopy(params, operateLog))
          : await to(coursePeriodAdd(params, operateLog));
      if (res.code === 200) {
        ElMessage.success("创建成功");
        useCourseStore.saveLeaderInfo([]);
        useCourseStore.saveLecturerInfo([]);
        useCourseStore.saveBaseInfo({});
        useCourseStore.savePeriodState("NOT_LISTED");
        if (tableData.value?.length > 1) {
          router.replace({
            path: "/course/courseDetails",
            query: { id: route.query.id }
          });
        } else {
          router.replace({
            path: "/course/courseDetails/currentDetails",
            query: {
              courseId: res?.data?.courseId,
              periodId: res?.data?.coursePeriodId,
              type: "new"
            }
          });
        }
      } else {
        ElMessage.error(`创建失败，${res.msg}`);
      }
    } else {
      console.log("校验不通过!", fields);
    }
    submitLoading.value = false;
  });
};
// 取消
const cancelEvt = () => {
  const currentPath = router.currentRoute.value.path;
  const isFromCopy = currentPath.includes("periodCopy");

  useCourseStore.saveLeaderInfo([]);
  useCourseStore.saveLecturerInfo([]);
  useCourseStore.saveBaseInfo({});

  if (isFromCopy) {
    router.replace({
      path: "/course/periodCopy/create",
      query: { courseId: route.query.id }
    });
  } else {
    router.replace({
      path: "/course/courseDetails",
      query: { id: route.query.id }
    });
  }
};
// 复制
const copyData = ref({});
const getCopy = val => {
  copyData.value = { ...val };
};
// 粘贴
const getPaste = val => {
  tableData.value[val].classStart = copyData.value.classStart;
  tableData.value[val].signUpDeadline = copyData.value.signUpDeadline;
};

// 复制创建查询课期详情
const getCoursePeriodFind = async () => {
  let [err, res] = await requestTo(
    coursePeriodFind({ id: route.query.copyId })
  );
  if (res) {
    // console.log("🐬-----res1111-----", res);
    ruleForm.name = res?.course?.name;
    ruleForm.courseTypeId = res?.courseType?.id;
    if (route.query.create === "create" && useCourseStore.baseInfo) {
      ruleForm.complexId = useCourseStore.baseInfo?.id;
    } else {
      ruleForm.complexId = res?.complex?.id;
    }

    // ruleForm.minPeopleNumber = res?.minPeopleNumber;
    ruleForm.maxPeopleNumber = res?.maxPeopleNumber;
    if (
      useCourseStore.lecturerInfo &&
      useCourseStore.lecturerInfo.length &&
      route.query.create === "create"
    ) {
      ruleForm.lecturerIds = useCourseStore.lecturerInfo?.map(it => it.id);
    } else {
      ruleForm.lecturerIds = res?.lecturers?.map(item => {
        return item.id;
      });
    }
    if (
      useCourseStore.leaderInfo &&
      useCourseStore.leaderInfo.length &&
      route.query.create === "create"
    ) {
      ruleForm.leaderIds = useCourseStore.leaderInfo?.map(it => it.id);
    } else {
      ruleForm.leaderIds = res?.leaders?.map(item => {
        return item.id;
      });
    }

    tableData.value.push({
      openTime: dayjs(res?.openTime).format("YYYY-MM-DD"),
      weekDay: dayjs(res?.openTime).format("dddd"),
      classStart: res?.openTime,
      signUpDeadline: (res?.openTime - res?.signUpDeadline) / 60 / 60 / 1000,
      sortTime: res?.openTime,
      valueId: res?.id
    });
    if (res.cover?.length) {
      res.cover.map(item => {
        ruleForm.cover.push(item.uploadFile);
        fileList.value.push(item.uploadFile);
      });
    }
  } else {
    console.log("🐳-----err-----", err);
  }
};
// 从store中获取并更新表单数据
const updateFormFromStore = () => {
  if (route.query.create === "create") {
    if (useCourseStore.lecturerInfo && useCourseStore.lecturerInfo.length) {
      ruleForm.lecturerIds = useCourseStore.lecturerInfo?.map(it => it.id);
    }
    if (useCourseStore.leaderInfo && useCourseStore.leaderInfo.length) {
      ruleForm.leaderIds = useCourseStore.leaderInfo?.map(it => it.id);
    }
    if (useCourseStore.baseInfo) {
      ruleForm.complexId = useCourseStore.baseInfo?.id;
    }

    // 从store中恢复课期名称和人数上限
    ruleForm.name = useCourseStore.periodName;
    ruleForm.maxPeopleNumber = useCourseStore.periodMaxPeopleNumber;

    // 从store中恢复封面图和开课时间设置
    if (useCourseStore.coverInfo && useCourseStore.coverInfo.length) {
      ruleForm.cover = useCourseStore.coverInfo;
      fileList.value = useCourseStore.coverInfo;
    }
    if (
      useCourseStore.periodTableData &&
      useCourseStore.periodTableData.length
    ) {
      tableData.value = useCourseStore.periodTableData;
    }
  }
};

// 根据ID更新选择框的选项标签
const updateSelectOptions = () => {
  // 领队和讲师选项更新
  if (ruleForm.leaderIds && formData.value[4].options.length > 0) {
    // 重新匹配选中项，确保显示的是姓名而不是ID
    const leaderIdMap = {};
    formData.value[4].options.forEach(opt => {
      leaderIdMap[opt.value] = opt;
    });

    // 如果当前选中的ID在选项中存在，则保留，否则清除
    ruleForm.leaderIds = ruleForm.leaderIds.filter(id => leaderIdMap[id]);
  }

  if (ruleForm.lecturerIds && formData.value[5].options.length > 0) {
    // 同样更新讲师选项
    const lecturerIdMap = {};
    formData.value[5].options.forEach(opt => {
      lecturerIdMap[opt.value] = opt;
    });

    ruleForm.lecturerIds = ruleForm.lecturerIds.filter(id => lecturerIdMap[id]);
  }
};

onMounted(async () => {
  await complexIdApi();
  await courseTypeFindApi();
  await leaderFindApi(3);
  await leaderFindApi(2);
  if (route.query.copyId) {
    await getCoursePeriodFind();
  }
  //根据id查询详情信息
  if (route.query.type === "new") {
    await getDetelList();
  }
  updateFormFromStore();
});

// 当组件从缓存中激活时更新数据
onActivated(async () => {
  // 先重新加载领队和讲师数据，确保显示姓名而不是ID
  await Promise.all([
    leaderFindApi(3), // 领队
    leaderFindApi(2) // 讲师
  ]);

  // 当选项加载完成后，再从store中更新表单
  updateFormFromStore();

  // 更新选项匹配，确保选中项存在于选项中
  updateSelectOptions();
});

// 全新复制携带课程信息（课程信息查询）
const getDetelList = async () => {
  let paramsData = {
    id: route.query.id
  };
  const [err, result] = await requestTo(courseFindId(paramsData));
  if (!result) return false;

  ruleForm.name = result.name;
  ruleForm.courseTypeId = result.courseType?.id;
  if (route.query.create === "create" && useCourseStore.baseInfo) {
    ruleForm.complexId = useCourseStore.baseInfo?.id;
  } else {
    ruleForm.complexId = result?.complex?.id;
  }
  ruleForm.maxPeopleNumber = result.maxPeopleNumber;

  if (!result.files) return false;
  if (result.files && result.files.length) {
    result.files?.map(it => {
      fileList.value.push(it.uploadFile);
      ruleForm.cover.push(it.uploadFile);
    });
  }
};
// 弹窗显示状态
const dialogVisiblesc = ref(false);
const currentDatesed = ref(new Date());
const selectedDates = ref([]);

const isSelected = dateString => {
  return selectedDates.value.some(
    d => dayjs(d).format("YYYY-MM-DD") === dateString
  );
};

const toggleDate = dateString => {
  const date = new Date(dateString);
  const index = selectedDates.value.findIndex(
    d => dayjs(d).format("YYYY-MM-DD") === dateString
  );
  if (route.query.type === "new") {
    if (index === -1) {
      selectedDates.value = [date];
    } else {
      selectedDates.value.splice(index, 1);
    }
  } else {
    if (index === -1) {
      selectedDates.value.push(date);
    } else {
      selectedDates.value.splice(index, 1);
    }
  }
};

// 修改这里：用空格代替逗号分隔
const formattedDates = computed(() => {
  return selectedDates.value.map(d => dayjs(d).format("YYYY-MM-DD")).join("  "); // 改为空格分隔
});
// 清空选择日期
const handleCancle = () => {
  selectedDates.value = [];
  dialogVisiblesc.value = false;
};

// 选择日期确认
const setChooseDate = () => {
  if (selectedDates.value?.length) {
    setData();
    if (tableData.value?.length) {
      // 去掉重复项
      tableData.value = tableData.value.filter(
        (item, index, self) =>
          index === self.findIndex(t => t.sortTime === item.sortTime)
      );
      // 升序排序
      tableData.value = tableData.value.sort(function (a, b) {
        return a.sortTime - b.sortTime;
      });
    }
  } else {
    ElMessage.error("请先选择日期");
  }

  handleCancle();
};

const ruleFormRef = ref("");
// 点击新建
// 保存当前表单数据到store
const saveCurrentFormToStore = () => {
  // 保存基地信息
  if (ruleForm.complexId) {
    useCourseStore.saveBaseInfo({ id: ruleForm.complexId });
  }

  // 保存领队信息
  if (ruleForm.leaderIds && ruleForm.leaderIds.length) {
    let ids = ruleForm.leaderIds.map(it => ({ id: it }));
    useCourseStore.saveLeaderInfo(ids);
  }

  // 保存讲师信息
  if (ruleForm.lecturerIds && ruleForm.lecturerIds.length) {
    let ids = ruleForm.lecturerIds.map(it => ({ id: it }));
    useCourseStore.saveLecturerInfo(ids);
  }

  // 保存课期名称和人数上限
  useCourseStore.savePeriodName(ruleForm.name);
  useCourseStore.savePeriodMaxPeopleNumber(ruleForm.maxPeopleNumber);

  // 保存封面图和开课时间设置
  useCourseStore.saveCoverInfo(ruleForm.cover);
  useCourseStore.savePeriodTableData(tableData.value);
};

const clickEvt = item => {
  const currentPath = router.currentRoute.value.path;
  const isFromCopy = currentPath.includes("periodCopy");

  // 无论点击哪个类型的新建按钮，都保存当前表单数据
  saveCurrentFormToStore();

  if (item.text === "新建基地") {
    router.push({
      path: isFromCopy
        ? "/course/periodCopy/create/baseAdd"
        : "/course/periodCreate/baseAdd",
      query: {
        type: route.query.type,
        copyId: route.query.copyId,
        id: route.query.id,
        name: route.query.name,
        termNumber: route.query.termNumber
      }
    });
  } else if (item.text === "新建领队") {
    router.push({
      path: isFromCopy
        ? "/course/periodCopy/create/leaderCreate"
        : "/course/periodCreate/leaderCreate",
      query: {
        type: "new",
        text: "leader",
        roleId: 3,
        copyId: route.query.copyId,
        type: route.query.type,
        id: route.query.id,
        name: route.query.name,
        termNumber: route.query.termNumber
      }
    });
  } else if (item.text === "新建讲师") {
    router.push({
      path: isFromCopy
        ? "/course/periodCopy/create/lecturerCreate"
        : "/course/periodCreate/lecturerCreate",
      query: {
        type: "new",
        text: "teacher",
        roleId: 2,
        copyId: route.query.copyId,
        type: route.query.type,
        id: route.query.id,
        name: route.query.name,
        termNumber: route.query.termNumber
      }
    });
  }
};
router.afterEach((to, from) => {
  if (to.path === "/course/courseDetails") {
    useCourseStore.saveLeaderInfo([]);
    useCourseStore.saveLecturerInfo([]);
    useCourseStore.saveBaseInfo({});
  }
});
// 开课日期改变
const handleCalendarChange = val => {
  console.log("🐬-----val-----", tableData.value);

  let weekArray = ["日", "一", "二", "三", "四", "五", "六"];

  let week = weekArray[new Date(val.openTime).getDay()];

  tableData.value.map(item => {
    if (item.valueId === val.valueId) {
      item.weekDay = `星期${week}`;
    }
  });
};
</script>

<template>
  <div class="course-period">
    <div class="course_center">
      <div class="left">
        <div v-if="route.query.name" class="copy_title">
          {{ `从 ${route.query.name} 第 ${route.query.termNumber}期复制` }}
        </div>
        <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules">
          <el-descriptions
            title=""
            :column="1"
            border
            style="width: 100%; overflow-x: auto"
          >
            <el-descriptions-item
              v-for="(item, index) in formData"
              :key="index"
              label-align="center"
            >
              <template #label>
                <span v-if="item.check" class="star">*</span>{{ item.label }}
              </template>
              <el-form-item
                :prop="item.prop"
                :inline-message="item.check"
                style="margin-bottom: 0"
                :show-message="true"
                error-placement="right"
              >
                <!-- input输入 -->
                <template v-if="item.type === 'input'">
                  <el-input
                    v-model.trim="ruleForm[item.prop]"
                    :placeholder="item.placeholder"
                    :style="{ width: item.width }"
                    :type="item.typeInput"
                  />
                </template>
                <!-- 多选 -->
                <template v-if="item.type === 'selectMultiple'">
                  <el-select
                    v-model="ruleForm[item.prop]"
                    multiple
                    :placeholder="item.placeholder"
                    :style="{ width: item.width }"
                  >
                    <el-option
                      v-for="it in item.options"
                      :key="it.value"
                      :label="it.label"
                      :value="it.value"
                    />
                  </el-select>
                  <el-button
                    type="primary"
                    style="margin-left: 10px"
                    @click="clickEvt(item)"
                  >
                    {{ item.text }}
                  </el-button>
                </template>
                <!-- 单选 -->
                <template v-if="item.type === 'select'">
                  <el-select
                    v-model="ruleForm[item.prop]"
                    :placeholder="item.placeholder"
                    :style="{ width: item.width }"
                  >
                    <el-option
                      v-for="it in item.options"
                      :key="it.value"
                      :label="it.label"
                      :value="it.value"
                    />
                  </el-select>
                  <el-button
                    type="primary"
                    style="margin-left: 10px"
                    @click="clickEvt(item)"
                  >
                    {{ item.text }}
                  </el-button>
                </template>
                <!-- 级联选择 -->
                <template v-if="item.type === 'cascader'">
                  <el-cascader
                    v-model="ruleForm[item.prop]"
                    :options="item.options"
                    :show-all-levels="false"
                    :style="{ width: item.width }"
                    :disabled="true"
                    @change="courseTypeChange"
                  />
                </template>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>

          <div class="upload">封面图</div>
          <el-upload
            v-model:file-list="fileList"
            action="#"
            :http-request="() => {}"
            list-type="picture-card"
            :before-upload="beforeUpload"
            :on-remove="handleRemove"
            :on-preview="handlePictureCardPreview"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
          <el-dialog v-model="dialogVisible">
            <img class="w-full" :src="dialogImageUrl" alt="Preview Image">
          </el-dialog>
          <div class="upload_text">
            支持上传jpg、jpeg、png、gif、bmp、webp格式，支持多张图片上传，图片最佳尺寸：750*750px，单张图片大小不超过10MB
          </div>
        </el-form>
      </div>
      <div class="right">
        <div class="set">
          <div class="text">开课时间设置</div>
          <el-button type="primary" @click="addDAte">添加日期</el-button>
        </div>

        <!-- 弹窗 -->
        <el-dialog
          v-model="dialogVisiblesc"
          title="选择日期"
          width="700px"
          :close-on-click-modal="false"
          :show-close="false"
        >
          <!-- 日历组件 -->
          <div class="chooseDates">
            <div class="demo-container">
              <el-calendar v-model="currentDatesed">
                <template #date-cell="{ data }">
                  <div
                    class="cell-content"
                    :class="{ 'selected-date': isSelected(data.day) }"
                    @click.stop="toggleDate(data.day)"
                  >
                    {{ data.day.split("-").slice(2).join("-") }}
                    <!-- 勾选标志 -->
                    <span v-if="isSelected(data.day)" class="checkmark">✓</span>
                  </div>
                </template>
              </el-calendar>
            </div>
            <span style="font-weight: bold"> 已添加的日期 </span>
            <div class="selected-dates">{{ formattedDates }}</div>
            <div class="btns">
              <el-button @click="handleCancle">取消</el-button>
              <el-button type="primary" @click="setChooseDate">
                确定
              </el-button>
            </div>
          </div>
        </el-dialog>
        <div v-if="tableData?.length" class="tables">
          <el-table :data="tableData" height="600">
            <el-table-column prop="openTime" label="开课日期" min-width="100">
              <template #default="scope">
                <div class="time_picker">
                  <el-date-picker
                    v-model="scope.row.openTime"
                    type="date"
                    placeholder="选择日期"
                    size="default"
                    value-format="YYYY-MM-DD"
                    :style="{ width: '150px' }"
                    @change="handleCalendarChange(scope.row)"
                  />
                </div>
              </template>
              <!-- <template #default="scope">
                {{ scope.row.openTime || "暂无" }}
              </template> -->
            </el-table-column>
            <el-table-column prop="weekDay" label="星期" align="center">
              <template #default="scope">
                <div>
                  {{ scope.row.weekDay || "暂无" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="classStart"
              label="开课时间"
              align="left"
              min-width="220"
            >
              <template #default="scope">
                <div class="time_picker">
                  <el-time-picker
                    v-model="scope.row.classStart"
                    placeholder="请选择时间"
                    :style="{ width: '150px' }"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column
              width="180px"
              prop="signUpDeadline"
              label="提前关闭报名时间"
            >
              <template #default="scope">
                <div style="display: flex; align-items: center">
                  <el-input-number
                    v-model="scope.row.signUpDeadline"
                    :min="0"
                    :step="0.5"
                    :precision="1"
                    step-strictly
                    @change="handleChange"
                  />
                  <span class="hour">小时</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column
              fixed="right"
              label="操作"
              align="left"
              min-width="140"
            >
              <template #default="scope">
                <div
                  style="
                    display: flex;
                    justify-content: space-around;
                    align-items: center;
                  "
                >
                  <div class="btnse" @click="getPaste(scope.$index)">粘贴</div>
                  <div class="btnse" @click="getCopy(scope.row)">复制</div>
                  <div class="btnse c_red" @click="getDelted(scope.$index)">
                    删除
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <div class="buttons">
      <el-button @click="cancelEvt">取消</el-button>

      <el-button
        type="primary"
        :loading="submitLoading"
        @click="submitForm(ruleFormRef)"
      >
        创建
      </el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.is-selected {
  color: #1989fa;
}
.course-period {
  box-sizing: border-box;
  padding: 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: 90%;
}
.copy_title {
  margin: 10px 0;
  line-height: 32px;
  font-weight: bold;
}
.set {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 10px 0;
  cursor: pointer;
  font-weight: bold;
}
.course_center {
  width: 100%;
  height: calc(100% - 42px);
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  flex-wrap: wrap;
  overflow-y: auto;
}
.left {
  min-width: 700px;
  flex: 5;
  height: fit-content;
  border-right: 2px solid #eee;
  padding-right: 20px;
  box-sizing: border-box;
  white-space: nowrap;
  margin-right: 20px;
  min-height: 500px;

  .upload {
    line-height: 32px;
    margin: 10px 0;
    font-weight: bold;
  }
}
.right {
  min-width: 700px;
  flex: 5;

  box-sizing: border-box;
  .tables {
    width: 100%;
    .time_picker {
      width: 120px;
    }
  }
}

.buttons {
  display: flex;
  // justify-content: space-between;
  justify-content: flex-end;
  width: 100%;
  // margin:  auto;

  .create {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 36px;
    color: rgb(255 255 255 / 100%);
    cursor: pointer;
    background-color: rgb(64 149 229 / 100%);
    border-radius: 6px;
  }
}

.star {
  margin-right: 3px;
  color: red;
}
:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  width: 100px;
  background: #e1f5ff;
}

.btnse {
  color: #409eff;
  cursor: pointer;
  min-width: fit-content;
  font-weight: bold;
}
.c_red {
  color: #f56c6c;
}
//滚动条的宽度
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: #e4e4e4;
  border-radius: 3px;
}
//滚动条的滑块
::-webkit-scrollbar-thumb {
  background-color: #eee;
  border-radius: 3px;
}
.date_picker {
  width: 200px;
}
.m-b {
  margin-bottom: 40px;
}
.hour {
  min-width: fit-content;
  margin-left: 10px;
}
.chooseDates {
  padding: 30px;
  box-sizing: border-box;
  background-color: #fff;
  height: 90%;
}
.demo-container {
  max-width: 100%;
  background-color: #ccc;
  box-shadow: 0px 4px 13px 0px #ccc;
  margin-bottom: 20px;
}

.cell-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s;
}

/* .cell-content:hover {
  background-color: #f5f7fa;
} */

/* 修改选中样式为勾选标志 */
.selected-date {
  color: #409eff;
  font-weight: bold;
}

.checkmark {
  position: absolute;
  right: 2px;
  bottom: 2px;
  font-size: 12px;
  color: #67c23a;
  font-weight: bold;
}

.selected-dates {
  margin-top: 20px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  white-space: pre-wrap; /* 允许空格换行 */
  font-weight: bold;
  min-height: 44px;
}
:deep(.el-calendar-table .el-calendar-day) {
  height: 50px;
}
.btns {
  margin-top: 20px;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  // justify-content: space-between;
}
.upload_text {
  font-size: 12px;
  position: relative;
  top: 5px;
  color: #8c939d;
}
</style>
