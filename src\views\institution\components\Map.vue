<script setup>
import {
  getLocationInfo,
  searchLocationByName,
  getLocation
} from "@/utils/bdMap";
import {
  MAP_CONSTANTS,
  createBMapPoint,
  isValidCoordinates,
  createMapIcon,
  debounce,
  formatAddressLabel,
  generateInfoWindowContent,
  generateMapConfig,
  Error<PERSON>andler,
  MapUtils
} from "@/utils/mapHelpers";
import { reactive, ref, onMounted, onUnmounted, nextTick, computed } from "vue";
import { Search, Loading } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import ImgPos from "@/assets/pos_loc.png";

const props = defineProps({
  // 地图中心点坐标
  center: {
    type: Array,

    default: () => [116.3912757, 39.906217],
    validator: value => isValidCoordinates(value)
  },
  // 倾斜角度
  pitch: {
    type: Number,
    default: MAP_CONSTANTS.DEFAULT_PITCH,
    validator: value => value >= 0 && value <= 80
  },
  // 地图缩放级别
  zoom: {
    type: Number,
    default: MAP_CONSTANTS.DEFAULT_ZOOM,
    validator: value => value >= 3 && value <= 19
  },
  // 地图旋转角度
  rotation: {
    type: Number,
    default: 0,
    validator: value => value >= 0 && value < 360
  },
  // 最大缩放级别
  maxZoom: {
    type: Number,
    default: 19
  },
  // 最小缩放级别
  minZoom: {
    type: Number,
    default: 11
  },
  checkInResult: {
    type: Object,
    default: () => ({})
  },
  // 上次选择的位置信息
  selectedLocation: {
    type: Object,
    default: () => null,
    validator: value => {
      if (!value) return true;
      return (
        value.point &&
        typeof value.point.lng === "number" &&
        typeof value.point.lat === "number" &&
        typeof value.address === "string"
      );
    }
  },
  addressStr: {
    type: String,
    default: "基地位置"
  }
});

const emits = defineEmits(["confirm"]);

// 响应式数据
const isShowModel = defineModel({ type: Boolean, default: false });
const form = reactive({
  name: ""
});
const baiduRef = ref();
const mapObj = ref(null);
const currentInfoWindow = ref(null);
const currentMarker = ref(null);
const loadingMap = ref(true);
const options = ref([]);
const loading = ref(false);
const chooseData = ref({});
const hasUserSelected = ref(false); // 标记用户是否主动选择了地址
const currentUserLocation = ref(null); // 用户当前位置
const isLocatingUser = ref(false); // 是否正在定位用户位置

// 计算属性
const mapCenter = computed(() => {
  // 优先级：1. 上次选择的位置 2. 用户当前位置 3. props传入的中心点 4. 默认点
  if (props.selectedLocation?.point) {
    const { lng, lat } = props.selectedLocation.point;
    if (isValidCoordinates([lng, lat])) {
      return [lng, lat];
    }
  }

  if (currentUserLocation.value) {
    return [currentUserLocation.value.lng, currentUserLocation.value.lat];
  }

  return MapUtils.getValidCenter(props.center, MAP_CONSTANTS.DEFAULT_POINT);
});

const mapConfig = computed(() => {
  return generateMapConfig(props);
});

// 工具函数
const getLabel = formatAddressLabel;

const clearMapOverlays = () => {
  if (mapObj.value) {
    mapObj.value.clearOverlays();
  }
  currentInfoWindow.value = null;
  currentMarker.value = null;
};

// 地图初始化
const initMap = async () => {
  try {
    await nextTick();

    const map = MapUtils.createMapInstance("map-container", mapConfig.value);

    // 使用当前计算出的地图中心点
    const point = createBMapPoint(mapCenter.value);

    // 设置地图中心和添加控件
    map.centerAndZoom(point, mapConfig.value.zoom);
    map.addControl(new BMapGL.LocationControl());

    mapObj.value = map;

    // 初始化时设置标记和信息窗口
    if (form.name) {
      await createInfoWindow(mapCenter.value, form.name);
    }
    addIcon(mapCenter.value);

    // 添加事件监听
    map.addEventListener("click", handleMapClick);

    loadingMap.value = false;
  } catch (error) {
    ErrorHandler.handleMapError(error, "地图初始化");
    loadingMap.value = false;
  }
};
// 地图点击事件处理
const handleMapClick = async e => {
  try {
    const { latlng } = e || {};
    const { lng, lat } = latlng || {};
    const coordinates = [lng, lat];

    if (!isValidCoordinates(coordinates)) {
      throw new Error("Invalid coordinates from map click");
    }

    const pointObj = createBMapPoint(coordinates);

    // 清除覆盖物
    clearMapOverlays();

    // 获取地址信息
    const res = await getLocationInfo(coordinates);
    const { address } = res;
    form.name = address;
    chooseData.value = { point: { lng, lat }, address };
    hasUserSelected.value = true; // 标记用户已通过地图选择地址

    // 添加标记和信息窗口
    addIcon(coordinates);
    await createInfoWindow(coordinates, address);

    // 移动地图到点击位置
    mapObj.value.panTo(pointObj);
  } catch (error) {
    ErrorHandler.handleMapError(error, "地图点击处理");
  }
};

// 创建信息窗口
const createInfoWindow = async (coordinates, address) => {
  if (!mapObj.value || !isValidCoordinates(coordinates)) return;

  try {
    const pointObj = createBMapPoint(coordinates);
    const content = generateInfoWindowContent({ pos: address });
    const infoWindow = new BMapGL.InfoWindow(content, {
      width: 0,
      height: 0,
      title: ""
    });

    // 关闭之前的信息窗口
    if (currentInfoWindow.value) {
      mapObj.value.closeInfoWindow();
    }

    mapObj.value.openInfoWindow(infoWindow, pointObj);
    currentInfoWindow.value = infoWindow;
  } catch (error) {
    ErrorHandler.handleMapError(error, "创建信息窗口");
  }
};

// 传入坐标点，进行定位
const locationTo = async (
  coordinates = [116.3912757, 39.906217],
  options = {}
) => {
  if (!mapObj.value || !isValidCoordinates(coordinates)) return;

  try {
    const {
      addMarker = true,
      addInfoWindow = true,
      updateAddress = true
    } = options;

    const point = createBMapPoint(coordinates);

    // 清除之前的覆盖物
    clearMapOverlays();

    // 移动地图到指定位置
    mapObj.value.panTo(point);
    mapObj.value.setViewport([point]);

    // 如果需要更新地址信息
    if (updateAddress) {
      try {
        const res = await getLocationInfo(coordinates);
        const { address } = res;
        form.name = address;
        chooseData.value = {
          point: { lng: coordinates[0], lat: coordinates[1] },
          address
        };

        // 添加信息窗口
        if (addInfoWindow) {
          await createInfoWindow(coordinates, address);
        }
      } catch (error) {
        console.warn("获取地址信息失败:", error);
      }
    }

    // 添加标记
    if (addMarker) {
      addIcon(coordinates);
    }

    // 如果没有更新地址但需要显示信息窗口，使用当前地址
    if (!updateAddress && addInfoWindow && form.name) {
      await createInfoWindow(coordinates, form.name);
    }

    // 延迟设置视角，避免动画冲突
    setTimeout(() => {
      mapObj.value.setTilt(props.pitch);
    }, MAP_CONSTANTS.TILT_ANIMATION_DELAY);
  } catch (error) {
    ErrorHandler.handleMapError(error, "地图定位");
  }
};

// 添加地图标记
const addIcon = (coordinates = [116.3912757, 39.906217]) => {
  if (!mapObj.value || !isValidCoordinates(coordinates)) return;

  try {
    const myIcon = createMapIcon(ImgPos);
    const marker = new BMapGL.Marker(createBMapPoint(coordinates), {
      icon: myIcon
    });

    mapObj.value.addOverlay(marker);
    currentMarker.value = marker;
  } catch (error) {
    ErrorHandler.handleMapError(error, "添加地图标记");
  }
};

// 搜索地址
const searchPlace = async query => {
  if (!mapObj.value || !query?.trim()) {
    options.value = [];
    return;
  }

  try {
    const res = await searchLocationByName(query.trim(), mapObj.value);
    const { local } = res;
    const resData = local.getResults();

    if (resData) {
      // 不在搜索时清除地图标记，保持当前标记点
      const dataList = resData._pois || [];
      options.value = dataList;
    } else {
      options.value = [];
      console.warn("未找到搜索结果");
    }
  } catch (error) {
    ErrorHandler.handleApiError(error, "搜索地址");
    options.value = [];
  } finally {
    loading.value = false;
  }
};

// 防抖搜索
const debouncedSearch = debounce(
  searchPlace,
  MAP_CONSTANTS.SEARCH_DEBOUNCE_DELAY
);

// 处理地址选择
const handleLocation = async val => {
  // return
  if (!val || typeof val !== "object") return;

  chooseData.value = val;
  hasUserSelected.value = true; // 标记用户已选择地址
  const { address = "", title = "", point } = val;
  form.name = address || title;

  if (point?.lng && point?.lat && isValidCoordinates([point.lng, point.lat])) {
    // 清除旧的标记点，添加新的标记点
    clearMapOverlays();

    // 移动地图到新位置并添加标记
    const pointObj = createBMapPoint([point.lng, point.lat]);
    mapObj.value.panTo(pointObj);
    addIcon([point.lng, point.lat]);
    await createInfoWindow([point.lng, point.lat], address || title);
  }
};

// 确认选择
const handleConfirm = () => {
  // 如果用户进行了搜索但没有选择地址，则提示
  if (!hasUserSelected.value) {
    ElMessage({
      type: "warning",
      message: "请先从搜索结果中选择一个地址"
    });
    return;
  }

  emits("confirm", chooseData.value);
  isShowModel.value = false;
};

// 获取用户当前位置
const getUserCurrentLocation = async () => {
  try {
    isLocatingUser.value = true;

    // 添加超时机制，防止卡住
    const locationPromise = getLocation();
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error("定位超时")), 10000); // 10秒超时
    });

    const userPoint = await Promise.race([locationPromise, timeoutPromise]);

    if (userPoint && userPoint.lng && userPoint.lat) {
      currentUserLocation.value = userPoint;
      console.log("成功获取用户位置:", userPoint);
      return true;
    }
    return false;
  } catch (error) {
    console.warn("获取用户位置失败:", error);
    ErrorHandler.handleApiError(error, "获取用户位置");
    return false;
  } finally {
    isLocatingUser.value = false;
  }
};

// 初始化地图信息
const initMapInfo = async () => {
  try {
    let hasUserLocation = false;
    let useSelectedLocation = false;

    // 如果有上次选择的位置，直接使用它
    if (props.selectedLocation?.point && props.selectedLocation?.address) {
      const { lng, lat } = props.selectedLocation.point;
      if (isValidCoordinates([lng, lat])) {
        form.name = props.selectedLocation.address;
        chooseData.value = props.selectedLocation;
        hasUserSelected.value = true;
        useSelectedLocation = true;
        console.log("使用上次选择的位置初始化:", props.selectedLocation);
      }
    }

    // 如果没有上次选择的位置，则尝试获取用户当前位置
    if (!useSelectedLocation) {
      hasUserLocation = await getUserCurrentLocation();

      // 使用当前计算出的地图中心点（优先用户位置，其次默认位置）
      const centerCoordinates = mapCenter.value;
      const { address } = await getLocationInfo(centerCoordinates);

      form.name = address;
      chooseData.value = {
        point: { lng: centerCoordinates[0], lat: centerCoordinates[1] },
        address
      };

      // 如果成功获取到用户位置，标记为已选择；如果是默认位置，也允许直接确认
      hasUserSelected.value = true;

      console.log(
        hasUserLocation ? "使用用户当前位置初始化" : "使用默认位置初始化",
        centerCoordinates
      );
    }
  } catch (error) {
    ErrorHandler.handleApiError(error, "初始化地图信息");
  }
};

// 搜索输入处理
const handleSearchInput = value => {
  hasUserSelected.value = false; // 重置选择状态
  if (value?.trim()) {
    loading.value = true;
    debouncedSearch(value.trim());
  } else {
    options.value = [];
    loading.value = false;
  }
};

// 远程搜索方法
const remoteMethod = query => {
  if (query?.trim()) {
    loading.value = true;
    debouncedSearch(query.trim());
  } else {
    options.value = [];
  }
};

// 清理函数
const cleanup = () => {
  if (mapObj.value) {
    mapObj.value.removeEventListener("click", handleMapClick);
    clearMapOverlays();
  }
};

// 生命周期
onMounted(async () => {
  // 先初始化地图信息（获取用户位置）
  await initMapInfo();
  // 然后初始化地图
  await initMap();
});

onUnmounted(() => {
  cleanup();
});

// 暴露方法
defineExpose({
  locationTo,
  clearMapOverlays,
  addIcon,
  searchPlace
});
</script>

<template>
  <el-dialog
    v-model="isShowModel"
    append-to-body
    :title="`${addressStr}选择`"
    :show-close="false"
    width="1000"
    class="map-dialog"
  >
    <template #default>
      <div class="dlg-con">
        <!-- 顶部描述文字 -->
        <div class="desc-text">
          <!-- <span v-if="isLocatingUser" class="locating-tip">
            <el-icon class="is-loading locating-icon">
              <Loading />
            </el-icon>
            正在定位您的当前位置...
          </span>
          <span v-else-if="props.selectedLocation?.point" class="location-selected-tip">
            ✓ 已定位到上次选择的位置
          </span>
          <span v-else-if="currentUserLocation" class="location-success-tip">
            ✓ 已定位到您的当前位置
          </span>
          <span v-else class="location-fallback-tip">
            无法获取当前位置，使用默认位置
          </span>
          <br> -->
          在地图上点击选择位置，或使用搜索框查找定点地点
        </div>

        <!-- 主要内容区域：左右布局 -->
        <div class="main-content">
          <!-- 左侧地图区域 -->
          <div class="map-section">
            <div
              id="map-container"
              ref="baiduRef"
              v-loading="loadingMap"
              class="map-con"
            />
          </div>

          <!-- 右侧搜索区域 -->
          <div class="search-section">
            <div class="search-title">搜索位置</div>
            <div class="search-input-wrapper">
              <el-input
                v-model="form.name"
                class="search-input"
                placeholder="输入地点名称或地址"
                clearable
                @input="handleSearchInput"
              >
                <template #prefix>
                  <el-icon class="search-icon">
                    <Search />
                  </el-icon>
                </template>
              </el-input>

              <!-- 搜索结果列表 -->
              <div v-if="options.length > 0" class="search-results">
                <div
                  v-for="item in options"
                  :key="item?.uid"
                  class="result-item"
                  @click="handleLocation(item)"
                >
                  <div class="result-title">{{ item.title || item.name }}</div>
                  <div class="result-address">{{ item.address }}</div>
                </div>
              </div>

              <!-- 加载状态 -->
              <div v-if="loading" class="loading-state">
                <el-icon class="is-loading">
                  <Loading />
                </el-icon>
                <span>搜索中...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="isShowModel = false">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss">
.map-tip {
  box-sizing: border-box;
  padding: 12px 16px;
  padding-top: 0;
  // background: #ffffff;
  // white-space: nowrap;
  // border-radius: 8px;
  // box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  // border: 1px solid #e0e0e0;
  // font-size: 14px;
  // line-height: 1.4;
  // min-width: 200px;
  // max-width: 300px;

  .pos-name {
    font-weight: 600;
    color: #333;
    margin-left: 4px;
  }
}

// 地图弹窗样式优化
:deep(.BMap_bubble_pop) {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;

  .BMap_bubble_center {
    background: transparent !important;
    border: none !important;

    .BMap_bubble_content {
      top: 0 !important;
      left: 0 !important;
      width: auto !important;
      height: auto !important;
      background: transparent !important;
      border: none !important;
      padding: 0 !important;
      margin: 0 !important;
      box-shadow: none !important;
    }
  }

  .BMap_bubble_top,
  .BMap_bubble_bottom,
  .BMap_bubble_pointer {
    display: none !important;
  }
}

// 对话框样式
:deep(.map-dialog) {
  .el-dialog__title {
    font-weight: 600;
    font-size: 18px;
    color: #333;
  }
}
</style>

<style lang="scss" scoped>
.dlg-con {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.desc-text {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  text-align: left;
  padding: 0;
  margin-bottom: 4px;

  .locating-tip {
    color: #409eff;
    display: inline-flex;
    align-items: center;
    gap: 6px;
  }

  .locating-icon {
    font-size: 14px;
  }

  .location-success-tip {
    color: #67c23a;
    font-weight: 500;
  }

  .location-selected-tip {
    color: #409eff;
    font-weight: 500;
  }

  .location-fallback-tip {
    color: #e6a23c;
  }
}

.main-content {
  display: flex;
  gap: 20px;
  height: 400px;
}

.map-section {
  flex: 1;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

.map-con {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  overflow: hidden;
  position: relative;

  // 加载状态样式
  :deep(.el-loading-mask) {
    border-radius: 8px;
  }
}

.search-section {
  width: 280px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  height: 100%;
}

.search-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.search-input-wrapper {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  min-height: 0;
}

.search-input {
  :deep(.el-input__wrapper) {
    border-radius: 8px;
    border: 1px solid #dcdfe6;
    transition: all 0.3s ease;

    &:hover {
      border-color: #409eff;
    }

    &.is-focus {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
  }
}

.search-icon {
  color: #999;
}

.search-results {
  flex: 1;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fff;
  min-height: 0;
}

.result-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #f5f7fa;
  }

  .result-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
  }

  .result-address {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
  }
}

.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #666;
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .el-button {
    min-width: 80px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
    height: auto;
  }

  .search-section {
    width: 100%;
  }

  .map-con {
    height: 300px;
  }
}
</style>
