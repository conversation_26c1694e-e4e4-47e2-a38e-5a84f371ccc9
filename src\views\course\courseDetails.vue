<script setup>
import { ref, onMounted, onActivated, computed, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  courseFindId,
  leaderLecturerFind,
  coursePeriodOffline,
  coursePeriodOnline,
  coursePeriodDelete,
  periodOpenGroupOrder,
  courseDelete,
  deletIds
} from "@/api/course.js";
import { periodcancelReview, coursePeriodAll } from "@/api/period.js";
import { draftGetCount } from "@/api/drafts.js";
import { ElMessage, ElMessageBox } from "element-plus";
import { requestTo } from "@/utils/http/tool";
import { formatTime } from "@/utils/index";
import { courseStore } from "@/store/modules/course.js";
import { to } from "@iceywu/utils";
import OrderDialog from "@/components/Base/orderDialog.vue";
import {
  Warning,
  Document,
  User,
  Calendar,
  InfoFilled
} from "@element-plus/icons-vue";
import { AUDIT_ENUM } from "@/utils/enum";
import { ImageThumbnail } from "@/utils/imageProxy.js";
defineOptions({
  name: "CourseDetails"
});
onActivated(() => {
  getTablePeriodList();
  getTableList();
  draftGetCountApi();
});
const useCourseStore = courseStore();
const router = useRouter();
const route = useRoute();

// 表头
const tableHeader = ref([
  {
    id: "1",
    label: "课程名",
    value: "",
    width: "107px"
  },
  {
    id: "2",
    label: "课程ID",
    value: "",
    width: "107px"
  },
  {
    id: "3",
    label: "创建时间",
    value: "",
    width: "107px"
  },
  {
    id: "4",
    label: "课程类型",
    value: "",
    width: "107px"
  },
  {
    id: "5",
    label: "课程亮点标签",
    value: "",
    width: "107px"
  },
  {
    id: "6",
    label: "课程状态",
    value: "",
    width: "107px"
  }
]);
// 表格
const tableData = ref([]);
// 搜索表单
const form = ref({
  startTime: "",
  endTime: "",
  coursePeriodState: "all",
  leadersId: 0,
  lecturersId: 0,
  buyType: "all",
  reviewState: "all"
});
const url = ref();

const srcList = ref([]);
// 课程状态
const stateOptions = [
  {
    value: "all",
    label: "全部"
  },
  {
    value: "ONLINE",
    label: "上架"
  },
  {
    value: "NOT_LISTED",
    label: "未上架"
  },
  {
    value: "ONLINE_UNDER_REVIEW",
    label: "上架审核"
    // label: "审核中"
  },
  {
    value: "OFFLINE",
    label: "下架"
  },
  {
    value: "OFFLINE_UNDER_REVIEW",
    label: "下架审核"
    // label: "审核中"
  },
  {
    value: "COMPLETED",
    label: "已完成"
  }
];
// 审核状态
const auditOptions = [
  {
    value: "all",
    label: "全部"
  },
  {
    value: "ONLINE_UNDER_REVIEW",
    label: "上架审核中"
  },
  {
    value: "ONLINE_PASS",
    label: "上架通过"
  },
  {
    value: "ONLINE_REJECT",
    label: "上架驳回"
    // label: "审核中"
  },
  {
    value: "OFFLINE_UNDER_REVIEW",
    label: "下架审核中"
  },
  {
    value: "OFFLINE_PASS",
    label: "下架通过"
    // label: "审核中"
  },
  {
    value: "OFFLINE_REJECT",
    label: "下架驳回"
    // label: "审核中"
  },
  {
    value: "NONE",
    label: "无"
  }
];
// 获取课程状态
const getSatte = val => {
  let res = "";
  stateOptions?.map(item => {
    if (item.value === val) {
      res = item.label;
    }
  });
  return res;
};

// 获取课期状态颜色
const getCoursePeriodStateColor = state => {
  const colorMap = {
    NOT_LISTED: "#9A9A9A", // 无/未上架 - 灰色
    ONLINE: "#4095E5", // 上架 - 蓝色
    ONLINE_UNDER_REVIEW: "#FF8000", // 审核中 - 橙色
    OFFLINE: "#FF6161", // 下架 - 红色
    OFFLINE_UNDER_REVIEW: "#FF6161", // 下架审核 - 红色
    COMPLETED: "#4095E5" // 已完成 - 蓝色
  };
  return colorMap[state] || "#9A9A9A";
};

// 获取审核状态颜色
const getAuditStateColor = state => {
  const colorMap = {
    NONE: "#9A9A9A", // 无 - 灰色
    ONLINE_UNDER_REVIEW: "#FF8000", // 审核中 - 橙色
    OFFLINE_UNDER_REVIEW: "#FF8000", // 审核中 - 橙色
    ONLINE_PASS: "#4095E5", // 审核通过/已完成 - 蓝色
    OFFLINE_PASS: "#4095E5", // 审核通过/已完成 - 蓝色
    ONLINE_REJECT: "#FF6161", // 审核驳回 - 红色
    OFFLINE_REJECT: "#FF6161" // 审核驳回 - 红色
  };
  return colorMap[state] || "#9A9A9A";
};
// 讲师
const teacherOptions = ref([{ label: "全部", value: 0 }]);
// 领队
const leaderOptions = ref([{ label: "全部", value: 0 }]);
// 领队讲师查询
const leaderFindApi = async type => {
  const params = {
    roleId: type
  };
  let [err, res] = await requestTo(leaderLecturerFind(params));
  if (res) {
    if (type === 2) {
      // console.log("🍧-----res-----", res);
      let res1 = res.map(it => {
        return {
          ...it,
          label: it.name,
          value: it.id
        };
      });
      teacherOptions.value = teacherOptions.value.concat(res1);
      // console.log("🍧----- teacherOptions.value-----", teacherOptions.value);
    } else {
      let res2 = res.map(it => {
        return {
          ...it,
          label: it.name,
          value: it.id
        };
      });
      leaderOptions.value = leaderOptions.value.concat(res2);
      // console.log("🐠-----leaderOptions-----", leaderOptions);
    }
  } else {
    console.log("🌵-----err-----", err);
  }
};
// 购买类型
const typeOptions = [
  {
    value: "all",
    label: "全部"
  },
  {
    value: "ORDINARY",
    label: "普通单"
  },
  {
    value: "PRIVATE_DOMAIN_GROUP_ORDER",
    label: "团购单"
  }
];
// 重置
const setData = () => {
  params.value.page = 1;
  form.value = {
    startTime: "",
    endTime: "",
    coursePeriodState: "all",
    leadersId: "",
    lecturersId: "",
    buyType: "all",
    reviewState: "all"
  };
  pickTime.value = "";
  getTablePeriodList();
};
//搜索
const searchData = () => {
  params.value.page = 1;
  getTablePeriodList();
};
const pickTime = ref("");
// 选择时间
const timeChange = value => {
  // console.log("🐬-----value-----", value);
  if (value) {
    form.value.startTime = new Date(value[0])?.getTime();
    // form.value.endTime = value[1];
    const endDate = new Date(value[1]);
    endDate.setHours(23, 59, 59, 999); // 关键修改
    form.value.endTime = endDate.getTime();
  }
};

const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    id: route.query.id
  };
  const [err, result] = await requestTo(courseFindId(paramsData));
  if (result) {
    // console.log('🎁result--------333---------------------->',result);
    tableHeader.value[0].value = result.name || "--";
    tableHeader.value[1].value = result.id || "--";
    tableHeader.value[2].value =
      formatTime(result.createdAt, "YYYY-MM-DD HH:mm:ss") || "--";
    tableHeader.value[3].value = result.courseType.name || "--";
    // if (result.minPeopleNumber && result.maxPeopleNumber) {
    //   tableHeader.value[4].value =
    //     result.minPeopleNumber + "-" + result.maxPeopleNumber;
    // } else if (result.minPeopleNumber) {
    //   tableHeader.value[4].value = result.minPeopleNumber;
    // } else if (result.maxPeopleNumber) {
    //   tableHeader.value[4].value = result.maxPeopleNumber;
    // } else {
    //   tableHeader.value[4].value = "--";
    // }
    tableHeader.value[4].value = result.tags?.join("、") || "--";
    tableHeader.value[5].value = result.freeze === false ? "正常" : "冻结";
    if (result.files?.length) {
      srcList.value = [];
      result.files.map(item => {
        srcList.value.push(item?.uploadFile?.url);
      });
      url.value = result.files[0]?.uploadFile?.url;
    }
    // console.log('🎁srcList.value------------------------------>',srcList.value);
    // tableHeader.value[5].value = result.complex?.name || "--";
    useCourseStore.saveCourseInfo(result);
  } else {
    ElMessage.error(err);
  }
  getListLoading.value = false;
};
const isOffline = ref(true);
// 获取课期列表信息
const gettLoading = ref(false);
const getTablePeriodList = async data => {
  if (gettLoading.value) {
    return;
  }
  gettLoading.value = true;
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort,
    courseId: route.query.id
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  if (form.value.coursePeriodState === "all") {
    delete paramsData.coursePeriodState;
  }
  if (form.value.buyType === "all") {
    delete paramsData.buyType;
  }
  if (form.value.reviewState === "all") {
    delete paramsData.reviewState;
  }
  const [err, result] = await requestTo(coursePeriodAll(paramsData));
  if (result) {
    tableData.value = result?.content;
    // result?.content.forEach(it => {
    //   if (
    //     it.coursePeriodState === "COMPLETED" ||
    //     it.coursePeriodState === "OFFLINE_UNDER_REVIEW" ||
    //     it.coursePeriodState === "ONLINE" ||
    //     it.coursePeriodState === "ONLINE_UNDER_REVIEW"
    //   ) {
    //     isOffline.value = false;
    //   }
    // });
    params.value.totalElements = result.totalElements;
  } else {
    ElMessage.error(err);
  }
  gettLoading.value = false;
};
//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTablePeriodList();
};
const handleSizeChange = val => {
  params.value.page = 1;
  params.value.size = val;
  getTablePeriodList();
};
// 清除数据
const clearEvt = val => {
  if (val === "time") {
    form.value.startTime = "";
    form.value.endTime = "";
  }
  // params.value.page = 1;
  getTablePeriodList();
};
//编辑基本信息
const editinfo = () => {
  router.push({
    path: "/course/courseEdite",
    query: {
      type: "edite",
      id: route.query.id
    }
  });
};

// 复制
const copyEvt = row => {
  // console.log("💗getInfoid---------->", row);
  router.push({
    path: "/course/copyCourseSchedule",
    query: { courseId: route.query.id, periodId: row.id }
  });
  useCourseStore.savePeriodState(row.coursePeriodState);
};

// 详情
const detailEvt = row => {
  // console.log("💗getInfoid---------->", row);
  router.push({
    path: "/course/courseDetails/currentDetails",
    query: { courseId: route.query.id, periodId: row.id }
  });
  useCourseStore.savePeriodState(row.coursePeriodState);
};
const operateLog = ref({});
// 是否上下架
const removeEvt = (row, bool) => {
  let freezeText =
    bool === true
      ? "课程下架申请，提交后等待平台审核。"
      : "课程上架申请，提交后等待平台审核。";
  let title = bool === true ? "下架申请" : "上架申请";
  ElMessageBox.confirm(`${freezeText}`, `${title}`, {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(() => {
      // console.log("🐬-----row-----", row);
      isFreezeApi(row, bool);
    })
    .catch(() => {});
};
const isFreezeApi = async (row, bool) => {
  const params = {
    id: row?.id
  };
  let api = coursePeriodOnline;
  operateLog.value = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `申请上架“${row.name}”课期`
    // operatorTarget: form.value.name,
  };
  if (bool) {
    api = coursePeriodOffline;
    operateLog.value = {
      operateLogType: "COURSE_MANAGEMENT",
      operateType: `申请下架“${row.name}”课期`
      // operatorTarget: form.value.name,
    };
  }

  const { code, msg } = await api(params, operateLog.value);
  if (code === 200) {
    ElMessage({
      type: "success",
      message: bool === true ? "下架申请成功" : "上架申请成功"
    });
    getTablePeriodList();
  } else {
    if (code === 30039) {
      ElMessage({
        type: "error",
        message:
          bool === true
            ? `下架申请失败，${msg}`
            : `上架申请失败，${msg}，请去编辑`
      });
    } else if (code === 30040) {
      ElMessage({
        type: "error",
        message:
          bool === true
            ? `下架申请失败，${msg}`
            : `上架申请失败，${msg}，请去检查`
      });
    } else if (code === 30034) {
      ElMessage({
        type: "error",
        message:
          bool === true
            ? `下架申请失败，开课时间已过期`
            : `上架申请失败，开课时间已过期`
      });
    } else {
      ElMessage({
        type: "error",
        message: bool === true ? `下架申请失败，${msg}` : `上架申请失败，${msg}`
      });
    }
  }
};
// 删除课期
const deleteEvt = val => {
  ElMessageBox.confirm(`确定要删除该课期吗？`, "删除课期", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(async () => {
      const operateLog = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `删除了“${val.name}”课期`
        // operatorTarget: form.value.name,
      };
      let [err, res] = await to(coursePeriodDelete({ id: val.id }, operateLog));
      if (res.code === 200) {
        ElMessage.success("删除成功");
        getTablePeriodList();
      } else {
        ElMessage.error("删除失败");
      }
      if (err) {
        ElMessage.error("删除失败");
      }
    })
    .catch(() => {});
};
// 删除课程
const deleteCourseEvt = () => {
  ElMessageBox.confirm(`确定要删除该课程吗？`, "删除课程", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(async () => {
      const operateLog = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `删除了“${tableHeader.value[0].value}”课程`
        // operatorTarget: form.value.name,
      };
      let [err, res] = await to(
        courseDelete({ id: Number(route.query.id) }, operateLog)
      );
      if (res.code === 200) {
        ElMessage.success("删除成功");
        router.replace("/course/courseManage");
      } else {
        ElMessage.error(`删除失败,${res.msg}`);
      }
      if (err) {
        ElMessage.error("删除失败");
      }
    })
    .catch(() => {});
};
// 团购弹窗
const dialogFormVisible = ref(false);
const periodValue = ref(0);
const periodName = ref("");
// 开启团购
const openGroupOrder = row => {
  periodValue.value = row.id;
  periodName.value = row.name;
  dialogFormVisible.value = true;
};
// 团购分享
const groupOrderShare = id => {
  router.replace({
    path: "/course/currentDetails/groupOrder",
    query: { periodId: id }
  });
};

// 取消审核（撤销申请）
const cancel = val => {
  let title =
    val.coursePeriodState === "OFFLINE_UNDER_REVIEW"
      ? "下架申请"
      : val.coursePeriodState === "ONLINE_UNDER_REVIEW"
        ? "上架申请"
        : "申请";
  ElMessageBox.confirm(`确定要撤销该课期的${title}吗？`, `撤销${title}`, {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(async () => {
      const operateLog = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `撤销了“${val.name}”课期的${title}`
        // operatorTarget: form.value.name,
      };
      let [err, res] = await to(periodcancelReview({ id: val.id }, operateLog));
      if (res.code === 200) {
        ElMessage.success("撤销申请成功");
        getTablePeriodList();
      } else {
        ElMessage.error(`撤销申请失败,${res.msg}`);
      }
      if (err) {
        ElMessage.error("撤销申请失败");
      }
    })
    .catch(() => {});
};
// 获取领队讲师姓名
const getName = val => {
  let res = [];
  if (!val?.length) return;
  val.map(item => {
    res.push(item.name);
  });
  return res.join("、");
};
// 批量删除
let batchDeleteArr = ref([]);
const tableRef = ref(null);
let isSelection = ref(false);
const selectable = row => {
  if (
    (row.coursePeriodState === "OFFLINE" && row.isExistInformation === false) ||
    row.coursePeriodState === "NOT_LISTED"
  ) {
    return row.id;
  }
};
const getRowKeys = row => {
  return row.id;
};
const isHandleSelectionChange = () => {
  isSelection.value = !isSelection.value;
  if (isSelection.value) {
    clearDelete();
  }
};
const handleSelectionChange = val => {
  batchDeleteArr.value = [];
  ids.value = [];
  course.value = [];
  if (!val.length) return;
  if (val.length > 0) {
    val.forEach(item => {
      batchDeleteArr.value.push(item);
    });
  } else {
    batchDeleteArr.value = val[0];
  }
};
let ids = ref([]);
let course = ref([]);
const batchDelete = () => {
  batchDeleteArr.value.forEach(item => {
    ids.value.push(item.id);
    course.value.push(item.name);
  });
  if (ids.value.length === 0) {
    ElMessage.error("请选择要删除的课程");
    return;
  }
  ElMessageBox.confirm(
    `确定要删除“${course.value.join("、")}”课程吗？`,
    "删除课程",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    }
  )
    .then(async () => {
      const operateLog = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `删除了“${course.value.join("、")}”课程`
      };
      let [err, res] = await to(deletIds({ ids: ids.value }, operateLog));
      if (res.code === 200 && res.data.length === 0) {
        ElMessage.success("删除成功");
        batchDeleteArr.value = [];
        ids.value = [];
        course.value = [];
        tableRef.value.clearSelection();
        getTablePeriodList();
      } else {
        let coursePeriodName = [];
        let reason = [];
        res.data.forEach(item => {
          coursePeriodName.push(item.coursePeriodName);
          reason.push(item.reason);
        });
        // ElMessage.error(`删除失败,${res.msg}`);
        ElMessageBox.confirm(
          `${coursePeriodName.map((item, i) => `${item}:${reason[i]}`).join("、")}`,
          "删除失败",
          {
            cancelButtonText: "取消"
          }
        );
      }
      if (err) {
        ElMessage.error("删除失败");
      }
    })
    .catch(() => {});
};
// 退出批量删除
const clearDelete = () => {
  tableRef.value.clearSelection();
  batchDeleteArr.value = [];
};
onMounted(async () => {
  getTableList();
  getTablePeriodList();
  draftGetCountApi();
  await leaderFindApi(2);
  leaderFindApi(3);
});
const draftsNum = ref(0);

// 计算表格高度
const tableHeight = computed(() => {
  // 88vh 减去上容器、搜索区域、操作按钮、分页等固定高度
  return "calc(88vh - 400px)";
});
// 查询草稿数量
const draftGetCountApi = async () => {
  let [err, res] = await to(draftGetCount());
  if (res.code === 200) {
    draftsNum.value = res?.data?.draftCount || 0;
    // console.log("🍭-----res-----", res.data.draftCount);
  } else {
    console.log("🎁-----err-----", err);
  }
};
</script>

<template>
  <div class="containers">
    <div class="content_top">
      <!-- 操作按钮区域 -->
      <div class="course-actions">
        <div class="left-actions">
          <el-button type="primary" @click="router.push('/course/drafts')">
            草稿箱{{ `（${draftsNum}）` }}
          </el-button>
          <el-button
            type="primary"
            @click="
              router.push({
                path: '/course/allEvaluate',
                query: { courseId: route.query.id }
              })
            "
          >
            查看全部评价
          </el-button>
          <el-button
            v-if="tableHeader[5].value === '正常'"
            type="primary"
            @click="editinfo"
          >
            编辑基本信息
          </el-button>
        </div>

        <div class="right-actions">
          <el-button type="danger" @click="deleteCourseEvt">
            删除课程
          </el-button>
        </div>
      </div>

      <!-- 课程信息卡片 -->
      <el-card class="course-card" shadow="never">
        <div class="course-header">
          <div class="course-cover">
            <el-image
              :src="ImageThumbnail(url)"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="srcList"
              :hide-on-click-modal="true"
              show-progress
              :initial-index="4"
              fit="cover"
              class="cover-image"
            />
          </div>

          <div class="course-info">
            <div class="course-title">
              <h2>{{ tableHeader[0].value || "--" }}</h2>
            </div>

            <!-- 课程亮点标签 -->
            <div
              v-if="tableHeader[4].value && tableHeader[4].value !== '--'"
              class="course-tags"
            >
              <el-tag
                v-for="tag in tableHeader[4].value.split('、')"
                :key="tag"
                type="primary"
                effect="light"
                class="tag-item"
              >
                {{ tag }}
              </el-tag>
            </div>

            <div class="course-meta">
              <div class="meta-row">
                <div class="meta-item">
                  <el-icon class="meta-icon"><Document /></el-icon>
                  <span class="meta-label">课程ID：</span>
                  <span class="meta-value">{{
                    tableHeader[1].value || "--"
                  }}</span>
                </div>
                <div class="meta-item">
                  <el-icon class="meta-icon"><User /></el-icon>
                  <span class="meta-label">课程类型：</span>
                  <span class="meta-value">{{
                    tableHeader[3].value || "--"
                  }}</span>
                </div>
              </div>

              <div class="meta-row">
                <div class="meta-item">
                  <el-icon class="meta-icon"><Calendar /></el-icon>
                  <span class="meta-label">创建时间：</span>
                  <span class="meta-value">{{
                    tableHeader[2].value || "--"
                  }}</span>
                </div>
                <div class="meta-item">
                  <el-icon class="meta-icon"><InfoFilled /></el-icon>
                  <span class="meta-label">课程状态：</span>
                  <span
                    class="meta-value"
                    :style="{
                      color:
                        tableHeader[5].value === '冻结' ? '#f56c6c' : '#67c23a'
                    }"
                  >
                    {{ tableHeader[5].value || "--" }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
    <div class="content_bottom">
      <div class="con_search">
        <div class="search-form-container">
          <el-form :model="form" :inline="true" class="search-form">
            <el-form-item label="开课时间">
              <el-date-picker
                v-model="pickTime"
                type="daterange"
                start-placeholder="请选择开始时间"
                end-placeholder="请选择结束时间"
                value-format="x"
                @change="timeChange"
                @clear="clearEvt('time')"
              />
            </el-form-item>
            <el-form-item label="购买类型">
              <el-select
                v-model="form.buyType"
                style="width: 110px"
                placeholder="请选择"
                value-key="id"
              >
                <el-option
                  v-for="item in typeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="领队">
              <el-select
                v-model="form.leadersId"
                style="width: 110px"
                placeholder="请选择领队"
                value-key="id"
              >
                <el-option
                  v-for="item in leaderOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="讲师">
              <el-select
                v-model="form.lecturersId"
                style="width: 110px"
                placeholder="请选择讲师"
                value-key="id"
              >
                <el-option
                  v-for="item in teacherOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="课期状态">
              <el-select
                v-model="form.coursePeriodState"
                style="width: 110px"
                placeholder="请选择"
                :empty-values="[null, undefined]"
                :value-on-clear="null"
              >
                <el-option
                  v-for="item in stateOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="审核状态">
              <el-select
                v-model="form.reviewState"
                style="width: 120px"
                placeholder="请选择"
                :empty-values="[null, undefined]"
                :value-on-clear="null"
              >
                <el-option
                  v-for="item in auditOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-form>

          <div class="search-buttons">
            <el-button type="primary" @click="searchData"> 搜索 </el-button>
            <el-button @click="setData"> 重置 </el-button>
          </div>
        </div>
      </div>

      <!-- 操作按钮区域 -->
      <div class="operation-buttons">
        <el-button
          v-if="tableHeader[5].value === '正常'"
          type="primary"
          style="margin-right: 10px"
          @click="
            router.push({
              path: '/course/coursePeriod/Create',
              query: {
                courseId: route.query.id,
                type: 'createPeriod'
              }
            })
          "
        >
          新建课期
        </el-button>
        <el-button
          type="danger"
          style="margin-right: 10px"
          @click="isHandleSelectionChange()"
        >
          {{ isSelection === false ? "批量删除" : "退出批量删除" }}
        </el-button>
      </div>

      <div class="con_table">
        <el-table
          ref="tableRef"
          :data="tableData"
          table-layout="fixed"
          :header-cell-style="{ backgroundColor: '#fafafa', color: '#565353' }"
          highlight-current-row
          :height="tableHeight"
          :row-key="getRowKeys"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            v-if="isSelection === true"
            :selectable="selectable"
            :reserve-selection="true"
            type="selection"
            width="55"
          />
          <el-table-column prop="termNumber" label="期号" width="80" fixed>
            <template #default="scope">
              <el-text>
                {{ scope.row.termNumber || 0 }}
              </el-text>
            </template>
          </el-table-column>
          <el-table-column
            prop="name"
            label="课期名"
            align="left"
            show-overflow-tooltip
            width="150"
            fixed
          >
            <template #default="scope">
              {{ scope.row.name || "--" }}
            </template>
          </el-table-column>
          <el-table-column
            width="120px"
            prop="coursePeriodState"
            label="课期状态"
          >
            <!-- <template #default="scope">
              <div>
                {{ getSatte(scope.row.coursePeriodState) || "--" }}
              </div>
            </template> -->
            <template #default="scope">
              <div
                v-if="
                  scope.row.offlineType === 'PLATFORM_OFFLINE' ||
                  scope.row.offlineType === 'PLATFORM_CLOSE_GROUP'
                "
              >
                <el-tooltip
                  class="box-item"
                  title=""
                  :content="
                    scope.row.offlineType === 'PLATFORM_OFFLINE'
                      ? '课期已被强制下架，请联系平台客服了解情况'
                      : '课期已被强制取消定制，请联系平台客服了解情况'
                  "
                  placement="bottom"
                  effect="light"
                >
                  <div
                    class="status-tag"
                    :style="{
                      color: getCoursePeriodStateColor(
                        scope.row.coursePeriodState
                      )
                    }"
                  >
                    {{ getSatte(scope.row.coursePeriodState) || "--" }}
                    <el-icon style="color: red; margin-left: 3px">
                      <Warning />
                    </el-icon>
                  </div>
                </el-tooltip>
              </div>
              <div
                v-else
                class="status-tag"
                :style="{
                  color: getCoursePeriodStateColor(scope.row.coursePeriodState)
                }"
              >
                {{ getSatte(scope.row.coursePeriodState) || "--" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            width="120px"
            prop="auditState"
            label="审核状态"
            align="left"
          >
            <template #default="scope">
              <div
                v-if="
                  scope.row.reviewState === 'OFFLINE_REJECT' ||
                  scope.row.reviewState === 'ONLINE_REJECT'
                "
              >
                <el-tooltip
                  class="box-item"
                  title=""
                  :content="scope.row.opinion ? scope.row.opinion : '无'"
                  placement="bottom"
                  effect="light"
                >
                  <div
                    class="status-tag"
                    :style="{
                      color: getAuditStateColor(scope.row.reviewState)
                    }"
                  >
                    {{ AUDIT_ENUM[scope.row.reviewState]?.label || "无" }}
                    <el-icon style="color: red; margin-left: 3px">
                      <Warning />
                    </el-icon>
                  </div>
                </el-tooltip>
              </div>
              <div
                v-else
                class="status-tag"
                :style="{ color: getAuditStateColor(scope.row.reviewState) }"
              >
                {{ AUDIT_ENUM[scope.row.reviewState]?.label || "无" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="buyType" label="购买类型" align="left">
            <template #default="scope">
              <div>
                {{
                  scope.row.buyType === "ORDINARY"
                    ? "普通单"
                    : scope.row.buyType === "PRIVATE_DOMAIN_GROUP_ORDER"
                      ? "团购单"
                      : "--"
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="leaders"
            label="领队"
            align="left"
            width="200"
            show-overflow-tooltip
          >
            <template #default="scope">
              <div>
                {{ getName(scope.row.leaders) || "--" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="lecturers"
            label="讲师"
            align="left"
            width="200"
            show-overflow-tooltip
          >
            <template #default="scope">
              <div>
                {{ getName(scope.row.lecturers) || "--" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="openTime"
            label="开课时间"
            align="left"
            min-width="180"
          >
            <template #default="scope">
              <div>
                {{
                  formatTime(scope.row.openTime, "YYYY-MM-DD HH:mm:ss") || "--"
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="createdAt"
            label="创建时间"
            align="left"
            min-width="180"
          >
            <template #default="scope">
              <div>
                {{
                  formatTime(scope.row.createdAt, "YYYY-MM-DD HH:mm:ss") || "--"
                }}
              </div>
            </template>
          </el-table-column>
          <!-- <el-table-column
            prop="teachers"
            label="专家"
            align="left"
            width="200"
            show-overflow-tooltip
          >
            <template #default="scope">
              <div v-if="scope.row.lecturers?.length > 0">
                <div v-for="item in scope.row.lecturers">
                  <div
                    v-if="
                      scope.row.offlineType === 'PLATFORM_OFFLINE' ||
                      scope.row.offlineType === 'PLATFORM_CLOSE_GROUP'
                    "
                  >
                    <el-tooltip
                      class="box-item"
                      title=""
                      :content="item.name"
                      placement="bottom"
                      effect="light"
                    >
                      <div class="state-reject">
                        {{ getSatte(scope.row.coursePeriodState) || "--" }}
                        <el-icon style="color: red; margin-left: 3px">
                          <Warning />
                        </el-icon>
                      </div>
                    </el-tooltip>
                  </div>
                  <div
                    v-if="
                      scope.row.offlineType === 'PLATFORM_OFFLINE' ||
                      scope.row.offlineType === 'PLATFORM_CLOSE_GROUP'
                    "
                  >
                    <el-tooltip
                      class="box-item"
                      title=""
                      :content="item.name"
                      placement="bottom"
                      effect="light"
                    >
                      <div class="state-reject">
                        {{ getSatte(scope.row.coursePeriodState) || "--" }}
                        <el-icon style="color: blue; margin-left: 3px">
                          <Warning />
                        </el-icon>
                      </div>
                    </el-tooltip>
                  </div>
                </div>
              </div>
              <div v-else>{{ getName(scope.row.lecturers) || "--" }}</div>
            </template>
          </el-table-column> -->
          <el-table-column
            prop="address"
            fixed="right"
            label="操作"
            align="left"
            width="318px"
          >
            <template #default="{ row }">
              <div class="option">
                <div class="btnse" @click="detailEvt(row)">详情</div>
                <div
                  v-if="
                    row.coursePeriodState === 'OFFLINE' ||
                    row.coursePeriodState === 'NOT_LISTED'
                  "
                  class="btnse"
                  @click="
                    router.push({
                      path: '/course/coursePeriodEdite',
                      query: {
                        periodId: row.id,
                        type: 'edite',
                        courseId: route.query.id,
                        fromPage: 'courseDetail'
                      }
                    })
                  "
                >
                  编辑
                </div>
                <div class="btnse" @click="copyEvt(row)">复制</div>
                <div
                  v-if="
                    row.coursePeriodState === 'ONLINE' &&
                    row.buyType === 'ORDINARY'
                  "
                  class="btnse"
                  @click="removeEvt(row, true)"
                >
                  下架
                </div>
                <div
                  v-else-if="
                    row.coursePeriodState === 'OFFLINE' ||
                    row.coursePeriodState === 'NOT_LISTED'
                  "
                  class="nofreeze"
                  @click="removeEvt(row, false)"
                >
                  上架
                </div>
                <div
                  v-if="
                    row.buyType === 'ORDINARY' &&
                    (row.coursePeriodState === 'OFFLINE' ||
                      row.coursePeriodState === 'NOT_LISTED')
                  "
                  class="btnse1"
                  @click="openGroupOrder(row)"
                >
                  课程定制
                </div>
                <div
                  v-if="
                    row.buyType !== 'ORDINARY' &&
                    row.coursePeriodState === 'ONLINE'
                  "
                  class="btnse"
                  @click="groupOrderShare(row.id)"
                >
                  团购分享
                </div>
                <div
                  v-if="
                    (row.coursePeriodState === 'OFFLINE' &&
                      row.isExistInformation === false) ||
                    row.coursePeriodState === 'NOT_LISTED'
                  "
                  class="btnse1"
                  @click="deleteEvt(row)"
                >
                  删除
                </div>
                <div
                  v-if="
                    row.coursePeriodState === 'OFFLINE_UNDER_REVIEW' ||
                    row.coursePeriodState === 'ONLINE_UNDER_REVIEW'
                  "
                  class="btnse"
                  @click="cancel(row)"
                >
                  撤销申请
                </div>
              </div>
            </template>
          </el-table-column>
          <!-- 批量删除 -->
        </el-table>
        <div v-if="isSelection === true" class="con_delete">
          <el-button type="primary" @click="batchDelete">确认删除</el-button>
          <template v-if="batchDeleteArr.length > 0">
            <span class="delete-tip">已选{{ batchDeleteArr.length }}个课期：</span>
            <span
              v-for="item in batchDeleteArr"
              :key="item.id"
              class="delete-tip"
              >{{ item.name }}&nbsp;&nbsp;</span>
          </template>
        </div>
      </div>
      <div class="con_pagination">
        <!-- 分页 -->
        <el-pagination
          v-model:current-page="params.page"
          v-model:page-size="params.size"
          class="pagination"
          background
          :page-sizes="[15, 30, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="params.totalElements"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <OrderDialog
      :id="periodValue"
      v-model:dialogFormVisible="dialogFormVisible"
      :api="periodOpenGroupOrder"
      :operateLogType="'COURSE_MANAGEMENT'"
      :operateType="`定制了“${periodName}课期”的课程`"
      :logOut="false"
      :showContent="'groupOrder'"
      :textRightBtn="'确认'"
      :textLeftBtn="'取消'"
      :title="'课程定制确认'"
      :marginLeft="'10px'"
      @reset="dialogFormVisible = false"
    />
  </div>
</template>

<style lang="scss" scoped>
.containers {
  box-sizing: border-box;
  width: calc(100% - 48px);
  height: 100vh;
  max-height: 88vh;
  display: flex;
  flex-direction: column;
  // padding: 24px;
  background: #f0f2f5;
  overflow: hidden;

  .content_top {
    box-sizing: border-box;
    padding: 20px;
    background-color: #fff;
    flex-shrink: 0;

    .course-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      .left-actions {
        display: flex;
        gap: 10px;
      }

      .right-actions {
        display: flex;
        gap: 10px;
      }
    }

    .course-card {
      border-radius: 0;
      border: none;
      :deep(.el-card__body) {
        padding: 0;
      }
      .course-header {
        display: flex;
        gap: 24px;

        .course-cover {
          flex-shrink: 0;

          .cover-image {
            width: 260px;
            height: 154px;
            border-radius: 8px;
            object-fit: cover;
          }
        }

        .course-info {
          flex: 1;

          .course-title {
            margin-bottom: 16px;

            h2 {
              font-size: 24px;
              font-weight: 600;
              color: #1a1a1a;
              margin: 0;
              line-height: 1.3;
            }
          }

          .course-tags {
            margin-bottom: 20px;

            .tag-item {
              margin-right: 8px;
              margin-bottom: 8px;
            }
          }

          .course-meta {
            .meta-row {
              max-width: 800px;
              display: flex;
              margin-bottom: 16px;

              &:last-child {
                margin-bottom: 0;
              }

              .meta-item {
                display: flex;
                align-items: center;
                gap: 8px;
                flex: 1;
                min-width: 0;

                .meta-icon {
                  color: #409eff;
                  font-size: 16px;
                  flex-shrink: 0;
                }

                .meta-label {
                  color: #666;
                  font-size: 14px;
                  width: 80px;
                  flex-shrink: 0;
                  text-align: left;
                }

                .meta-value {
                  color: #333;
                  font-size: 14px;
                  font-weight: 500;
                  flex: 1;
                  min-width: 0;
                  word-break: break-all;
                }
              }
            }
          }
        }
      }
    }
  }

  .content_bottom {
    box-sizing: border-box;
    padding: 20px;
    margin-top: 20px;
    background-color: #fff;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  :deep(.el-button + .el-button) {
    margin: 0;
  }

  // :deep(.el-button--primary) {
  //   width: 100px;
  // }
}

.con_search {
  width: 100%;
  height: fit-content;
  flex-shrink: 0;
  // margin-top: 40px;

  .search-form-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;

    .search-form {
      flex: 1;
    }

    .search-buttons {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      flex-shrink: 0;
      gap: 10px;
    }
  }

  .btn_search {
    display: flex;
    justify-content: space-between;
    width: 140px;
  }
}

.operation-buttons {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;
}
.state-reject {
  width: 40px;
  display: flex;
  // justify-content: center;
  align-items: center;
  white-space: nowrap;
}

.status-tag {
  display: flex;
  align-items: center;
  white-space: nowrap;
  font-weight: 500;
}
:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}

.con_table {
  // width: calc(100% - 25px);
  // min-height: 500px;
  margin-bottom: 24px;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  // margin-left: 25px;
  .option {
    display: flex;

    .btnse {
      display: flex;
      margin-right: 16px;
      color: #4095e5;
      cursor: pointer;
    }
    .nofreeze {
      color: #f56c6c;
      cursor: pointer;
      margin-right: 16px;
    }
    .btnse1 {
      // display: flex;
      margin-right: 16px;
      color: #f56c6c;
      cursor: pointer;
    }
  }
}

.con_pagination {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  flex-shrink: 0;
  padding-top: 16px;
}
.delete-tip {
  font-size: 14px;
  &:nth-of-type(1) {
    margin-left: 10px;
  }
}
</style>
