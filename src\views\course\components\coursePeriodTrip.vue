<script setup>
import {
  onMounted,
  ref,
  reactive,
  defineEmits,
  onBeforeMount,
  onUnmounted
} from "vue";
import { useRouter, useRoute } from "vue-router";
import { Edit, Delete, Loading } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import RichEditor from "@/components/Base/RichEditor.vue";
import { findByCoursePeriodId, itineraryUpdateV2 } from "@/api/period.js";
import { coursePeriodFind } from "@/api/course.js";
import { requestTo } from "@/utils/http/tool";
import {
  ItineraryFindByDraftId,
  ItineraryNextDraftItinerary,
  itinerarySaveDraftItinerary,
  draftCoursePeriodFindByDraftId,
  draftDelete
} from "@/api/drafts.js";
import dayjs from "dayjs";
import { removeEmptyValues, to, deepClone } from "@iceywu/utils";
import { aiNewPage } from "@/utils/aiTool.js";
import { getAsyncTask, vETgetAsyncTask } from "@/utils/common";
import { questionAsk } from "@/api/aiQuestion.js";
import { courseStore } from "@/store/modules/course.js";
const props = defineProps({
  // draftId: {
  //   type: String,
  //   default: ""
  // },
  isNewEdit: {
    type: String,
    default: "new"
  },
  periodName: {
    type: String,
    default: "未命名"
  },
  infoShowEnName: {
    type: String,
    default: "foundation"
  }
});

const emits = defineEmits(["upperBelowEvt"]);
const useCourseStore = courseStore();
let handleCancel = null;
// console.log("🦄-----useCourseStore-----", useCourseStore?.draftId);

const router = useRouter();
const route = useRoute();
const ruleFormRef = ref();

const openTime = ref("");
// 日期禁用逻辑：禁止选择小于开课时间的日期
const disabledDate = time => {
  if (!openTime.value) return false;
  if (dialogTitle.value === "新增") {
    return time.getTime() < dayjs(openTime.value).startOf("day").valueOf();
  } else {
    return time.getTime() < dayjs(openTime.value).startOf("day").valueOf();
  }
};
const validateDate = (rule, value, callback) => {
  if (!value) {
    callback(new Error("请选择日期"));
  } else {
    callback();
  }
};
const validateTime = (rule, value, callback) => {
  if (!value) {
    callback(new Error("请选择时间"));
  } else if (
    dayjs(
      dayjs(form.value.date).format("YYYY-MM-DD") +
      "" +
      dayjs(value).format("HH:mm:ss")
    ).valueOf() < dayjs(openTime.value).valueOf()
  ) {
    if (dialogTitle.value === "新增") {
      if (tripList.value.at(-1)) {
        callback(new Error(`时间不能早于${tripList.value.at(-1).title}时间`));
      } else {
        callback(new Error("时间不能早于开课时间"));
      }
    } else {
      callback(new Error("时间不能早于开课时间"));
    }
  } else {
    callback();
  }
};
const rules = reactive({
  title: [{ required: true, message: "请输入行程标题", trigger: "blur" }],
  date: [{ required: true, validator: validateDate, trigger: "blur" }],
  time: [{ required: true, validator: validateTime, trigger: "blur" }]
});

const tripList = ref([]);
const textarea2 = ref("");
const valueHtml = ref("");
const form = ref({
  // 弹框表单
  title: "",
  gapDays: "",
  time: "",
  timeA: "",
  content: "",
  index: ""
});
const dialogFormVisible = ref(false);
const submitLoading = ref(false);
const dialogTitle = ref(""); // 弹框标题
const btnData = ref([
  { id: 1, name: "放弃新建" },
  { id: 2, name: "保存到草稿箱" }
]);

// 弹出框-打开
const isDialog = (text, val, a) => {
  // console.log("🐬-----text, val, a-----", text, val, a);
  if (ruleFormRef.value) ruleFormRef.value.resetFields();
  dialogFormVisible.value = true;
  if (text === "newly") {
    dialogTitle.value = "新增";
    form.value = {};
    valueHtml.value = "";
  } else {
    openTime.value = tripList.value[0].time;

    dialogTitle.value = "编辑";
    form.value.index = a || "";
    form.value.title = val.title;
    form.value.gapDays = val.gapDays;
    form.value.time = val.time;
    form.value.timeA = val.timeA;
    valueHtml.value = val.content;
  }
};
// 弹出框-新增及编辑行程点
const addItineraryCreate = async formEl => {
  if (!formEl) return;
  if (submitLoading.value) return;
  submitLoading.value = true;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      const p = {
        content: valueHtml.value,
        title: form.value.title,
        gapDays: form.value.gapDays || "",
        time: form.value.time || "",
        timeA: form.value.timeA || ""
      };
      // console.log("🐬-----p--新增及编辑信息---", p);
      if (dialogTitle.value === "编辑") {
        tripList.value.splice(form.value.index, 1, p);
        submitLoading.value = false;
        dialogFormVisible.value = false;
      } else {
        tripList.value.push(p);

        submitLoading.value = false;
        dialogFormVisible.value = false;
      }
    } else {
      console.log("校验不通过!", fields);
    }
  });
  submitLoading.value = false;
};
// 弹出框-取消
const cancel = () => {
  // if (ruleFormRef.value) ruleFormRef.value.resetFields();
  dialogFormVisible.value = false;
  submitLoading.value = false;
  dialogTitle.value = "新增";
  form.value = {};
  valueHtml.value = "";
};
// 删除
const deteleEvt = (val, index) => {
  ElMessageBox.confirm(`确定要删除该条行程吗`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(async () => {
      tripList.value.splice(index, 1);
    })
    .catch(() => {});
};

// 草稿Id查询行程API
const draftIdTripApi = async () => {
  editCopyClone.value = [];
  const params = {
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId)
  };
  let [err, res] = await requestTo(ItineraryFindByDraftId(params));
  if (res) {
    tripList.value = res.map(it => {
      return {
        content: it.content,
        gapDays: it.gapDays,
        id: it.id,
        timeA: timest(it.time),
        time: it.time,
        title: it.title
      };
    });
    editCopyClone.value = deepClone(tripList.value);
  }
};

const openTimeNian = ref("");
const openTimeValue = ref("");
const CoursePeriodId = ref({});
// 草稿Id查询课期API
const CoursePeriodIdTripApi = async () => {
  const params = {
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId)
  };
  let [err, res] = await requestTo(draftCoursePeriodFindByDraftId(params));
  if (res) {
    CoursePeriodId.value = res || {};
    let doopTime = CoursePeriodId.value?.draftCoursePeriodTimes || [];
    const maxOpenTime =
      doopTime.length > 0
        ? Math.max(...doopTime.map(item => item.openTime))
        : "";
    openTimeValue.value = maxOpenTime;
    openTimeNian.value = formatTimestamp(maxOpenTime);
  }
};

function deepEqualArrays(arr1, arr2) {
  if (arr1.length !== arr2.length) return false;
  for (let i = 0; i < arr1.length; i++) {
    const obj1 = arr1[i];
    const obj2 = arr2[i];
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);
    if (keys1.length !== keys2.length) return false;
    for (const key of keys1) {
      if (obj1[key] !== obj2[key]) {
        return false;
      }
    }
  }
  return true;
}
const editCopyClone = ref([]);

// 草稿箱 上、下一步+保存草稿箱 按钮
const buttonLoading = ref(false);
const upperBelowEvt = butt => {
  buttonLoading.value = true;
  let obj = {
    type: butt,
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
    infoShow: "基础信息"
  };
  if (butt === "upper") {
    emits("upperBelowEvt", obj);
    buttonLoading.value = false;
    return;
  }
  if (butt === "below") {
    nextStepApi(true);
  } else if (butt === "save") {
    saveApi();
  }
};

// 草稿行程下一步API
const nextStepApi = async val => {
  // console.log("🌈-----tripList.value---下一步API--", tripList.value);
  const type = deepEqualArrays(tripList.value, editCopyClone.value);
  if (type === true) {
    if (val !== false) {
      let obj = {
        type: "below",
        draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
        infoShow: "课期介绍",
        complete: true
      };
      emits("upperBelowEvt", obj);
    }
    buttonLoading.value = false;
  } else {
    if (Array.isArray(tripList.value) && tripList.value.length === 0) {
      buttonLoading.value = false;
      return ElMessage.error({
        type: "error",
        message: "请新增行程点"
      });
    }

    tripList.value.forEach((it, index) => {
      if (it.timeA) {
        const startTImes = dayjs(it.timeA).format("HH:mm:ss");
        const startTime = openTimeNian.value + " " + startTImes;
        let s = new Date(startTime).getTime();
        if (it.gapDays > 1) {
          tripList.value[index].time = startTImes;
        } else {
          if (openTimeValue.value > s) {
            tripList.value[index].time = "";
            tripList.value[index].timeA = "";
          } else {
            tripList.value[index].time = startTImes;
          }
        }
      }
    });
    const isType = ref(false);
    tripList.value.some((value, index) => {
      if (value.timeA === "") {
        isType.value = true;
        buttonLoading.value = false;
        ElMessage({
          message: "请选择“" + value.title + "”的时间点",
          type: "error"
        });
        return true; // 返回 true 结束循环
      }
      return false; // 返回 false 继续循环
    });
    if (isType.value === true) return;

    const tripData = tripList.value.map(it => {
      return {
        content: it.content,
        gapDays: it.gapDays,
        time: it.time,
        title: it.title
      };
    });
    // console.log("🦄-----tripData-----", tripData);
    const params = {
      draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
      draftItineraries: tripData
    };
    const operateLog = {
      operateLogType: "COURSE_MANAGEMENT",
      operateType: `将“${props.periodName}”课期中的课期行程保存在草稿箱`
      // operatorTarget: form.value.name,
    };
    const newList = removeEmptyValues(params);
    let [err, res] = await to(ItineraryNextDraftItinerary(newList, operateLog));
    // console.log("🍧-----err, res-----", err, res);
    if (res) {
      if (res.code === 30040) {
        ElMessage.error(`${res.msg},保存失败。`);
        buttonLoading.value = false;
      } else if (res.code === 200) {
        ElMessage.success("当前资料已保存到草稿箱");
        buttonLoading.value = false;
        if (val !== false) {
          let obj = {
            type: "below",
            draftId:
              Number(route.query.draftId) || Number(useCourseStore.draftId),
            infoShow: "课期介绍",
            complete: true
          };
          emits("upperBelowEvt", obj);
        }
      }
    } else {
      buttonLoading.value = false;
    }
  }
};
// 草稿行程保存API
const saveApi = async () => {
  if (Array.isArray(tripList.value) && tripList.value.length === 0) {
    buttonLoading.value = false;
    return ElMessage.error({
      type: "error",
      message: "请新增行程点"
    });
  }
  tripList.value.forEach((it, index) => {
    if (it.timeA) {
      const startTImes = dayjs(it.timeA).format("HH:mm:ss");
      const startTime = openTimeNian.value + " " + startTImes;
      let s = new Date(startTime).getTime();
      if (it.gapDays > 1) {
        tripList.value[index].time = startTImes;
      } else {
        if (openTimeValue.value > s) {
          tripList.value[index].time = "";
          tripList.value[index].timeA = "";
        } else {
          tripList.value[index].time = startTImes;
        }
      }
    }
  });
  const isType = ref(false);
  tripList.value.some((value, index) => {
    if (value.timeA === "") {
      isType.value = true;
      buttonLoading.value = false;
      ElMessage({
        message: "请选择“" + value.title + "”的时间点",
        type: "error"
      });
      return true; // 返回 true 结束循环
    }
    return false; // 返回 false 继续循环
  });
  if (isType.value === true) return;

  const tripData = tripList.value.map(it => {
    return {
      content: it.content,
      gapDays: it.gapDays,
      time: it.time,
      title: it.title
    };
  });
  // console.log("🦄-----tripData-----", tripData);
  const operateLog = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `将“${props.periodName}”课期中的课期行程保存在草稿箱`
    // operatorTarget: form.value.name,
  };
  const params = {
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
    draftItineraries: tripData
  };
  const newList = removeEmptyValues(params);
  let [err, res] = await to(itinerarySaveDraftItinerary(newList, operateLog));
  // console.log("🐬-----err, res-----", err, res);
  if (res) {
    if (res.code === 30040) {
      ElMessage.error(`${res.msg},保存失败。`);
      buttonLoading.value = false;
    } else if (res.code === 200) {
      ElMessage.success("当前资料已保存到草稿箱");
      draftIdTripApi();
      CoursePeriodIdTripApi();
      buttonLoading.value = false;
    }
  } else {
    ElMessage.error(`保存失败。`);
    buttonLoading.value = false;
  }
};

// 删除 草稿箱 Api
const deleteDraft = async () => {
  let operateLogDraft = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: route.query.draftId
      ? `删除了草稿id为“${route.query.draftId}”的草稿数据`
      : `删除了草稿id为“${useCourseStore.draftId}”的草稿数据`
  };
  const [err, res] = await to(
    draftDelete(
      { id: Number(route.query.draftId) || Number(useCourseStore.draftId) },
      operateLogDraft
    )
  );
  if (res.code === 200) {
    ElMessage.success("删除成功");
  } else {
    ElMessage.error("删除失败");
  }
};
// 退出
const backEvt = (val, it) => {
  // console.log("🍭-----val, it-----", val, it);
  if (val === "exit") {
    let titlt =
      it.id === 1
        ? `请确认是否清除当前编辑和提交的所有资料，并删除当前编辑资料在草稿箱中对应的草稿条目`
        : `请确认是否将当前编辑和提交的所有资料保存到草稿箱，并退出编辑页面`;
    ElMessageBox.confirm(`${titlt}`, `退出并${it.name}`, {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    }).then(() => {
      if (it.id === 1) {
        deleteDraft();
        if (route.query.type === "draft") {
          router.replace("/course/drafts");
        } else if (route.query.type === "edite") {
          router.replace("/course/courseDetails");
        } else {
          router.go(-1);
        }
      } else if (it.id === 2) {
        saveApi();
        if (route.query.type === "draft") {
          router.replace("/course/drafts");
        } else if (route.query.type === "edite") {
          router.replace("/course/courseDetails");
        } else {
          router.go(-1);
        }
      }
    });
  } else {
    if (route.query.fromPage === "courseDetail") {
      router.replace({
        path: "/course/courseDetails",
        query: { id: route.query.courseId }
      });
    } else if (route.query.fromPage === "currentDetail") {
      router.replace({
        path: "/course/courseDetails/currentDetails",
        query: {
          periodId: route.query.periodId,
          courseId: route.query.courseId
        }
      });
    }
  }
};

// 课程详情-编辑集合
const isOpenTime = ref("");
const coursePeriodFindApi = async () => {
  const params = {
    id: route.query.periodId
  };
  const newList = removeEmptyValues(params);
  let [err, res] = await to(coursePeriodFind(newList));
  if (res) {
    CoursePeriodId.value = res?.data;
    isOpenTime.value = res?.data?.openTime || 1;
    openTimeValue.value = res?.data?.openTime;
    openTimeNian.value = formatTimestamp(res?.data?.openTime);
    findByCoursePeriodApi();
  }
};
// 编辑 获取行程介绍数据 api
const findByCoursePeriodApi = async () => {
  const params = {
    coursePeriodId: route.query.periodId
  };
  const newList = removeEmptyValues(params);
  let [err, res] = await to(findByCoursePeriodId(newList));
  if (res) {
    tripList.value = res?.data?.map(it => {
      return {
        content: it.content,
        gapDays: calculateDays(it.startTime, isOpenTime.value),
        id: it.id,
        timeA: it.startTime,
        time: dayjs(it.startTime).format("HH:mm:ss"),
        title: it.title
      };
    });
  }
};
// 编辑 保存并返回和保存按钮 api
const saveEvt = async text => {
  buttonLoading.value = true;
  let operateLog = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `编辑了“${CoursePeriodId.value.name}”课期的行程点`
  };
  if (Array.isArray(tripList.value) && tripList.value.length === 0) {
    buttonLoading.value = false;
    return ElMessage.error({
      type: "error",
      message: "请新增行程点"
    });
  }
  const arrData = ref([]);
  const isType = ref(false);
  tripList.value.forEach(it => {
    if (!it.timeA) {
      buttonLoading.value = false;
      isType.value = true;
      return ElMessage({
        message: "请选择“" + it.title + "”的时间点",
        type: "error"
      });
    }
  });
  if (isType.value === true) return;
  tripList.value.map((it, index) => {
    return arrData.value.push({
      content: it.content,
      title: it.title,
      startTime: calculate(isOpenTime.value, it.time, it.gapDays)
    });
  });
  let params = {
    coursePeriodId: route.query.periodId,
    itinerarys: arrData.value
  };
  const newList = removeEmptyValues(params);
  let [err, res] = await to(itineraryUpdateV2(newList, operateLog));
  if (res.code === 200) {
    ElMessage({
      type: "success",
      message: "编辑成功"
    });
    if (text === "saveRet") {
      router.back();
    }
    buttonLoading.value = false;
  } else {
    buttonLoading.value = false;
    ElMessage({
      type: "error",
      message: `编辑失败，${res.msg}`
    });
  }
  buttonLoading.value = false;
};

onMounted(() => {
  if (route.query.type === "edite") {
    //draft  edite
    if (route.query.periodId) {
      coursePeriodFindApi();
    }
    tripList.value = [];
  } else {
    draftIdTripApi();
    CoursePeriodIdTripApi();
  }
  // startAutoSave()
});

let time1 = openTimeValue.value;
const pickerChange = (value, index) => {
  if (!value.timeA) {
    tripList.value[index].time = "";
    // tripList.value[index].timeA = "";
    return ElMessage({
      message: "请选择时间点",
      type: "error"
    });
  }
  const startTImes = dayjs(value.timeA).format("HH:mm:ss");
  const startTime = openTimeNian.value + " " + startTImes;
  let s = new Date(startTime).getTime();
  if (value.gapDays > 1) {
    tripList.value[index].time = startTImes;
  } else {
    if (openTimeValue.value > s) {
      tripList.value[index].time = "";
      tripList.value[index].timeA = "";
      return ElMessage({
        message: "时间不能早于开课时间",
        type: "error"
      });
    } else {
      tripList.value[index].time = startTImes;
    }
  }
};
const numberChange = (value, index) => {
  if (
    value.gapDays == "" ||
    value.gapDays === null ||
    value.gapDays === undefined
  ) {
    value.gapDays = 1;
  }
  Math.round(value.gapDays);
  if (value.timeA) {
    const startTImes = dayjs(value.timeA).format("HH:mm:ss");
    const startTime = openTimeNian.value + " " + startTImes;
    let s = new Date(startTime).getTime();
    if (value.gapDays > 1) {
      tripList.value[index].time = startTImes;
    } else {
      if (openTimeValue.value > s) {
        tripList.value[index].time = "";
        tripList.value[index].timeA = "";
        return ElMessage({
          message: "时间不能早于开课时间",
          type: "error"
        });
      } else {
        tripList.value[index].time = startTImes;
      }
    }
  }
};

const timest = val => {
  const timeRegex = /^(?:[01]\d|2[0-3]):[0-5]\d:[0-5]\d$/;
  if (!timeRegex.test(val)) return;
  let timeStr = val;
  let timeParts = timeStr.split(":"); // 分割时间字符串为小时、分钟、秒
  let hours = parseInt(timeParts[0], 10);
  let minutes = parseInt(timeParts[1], 10);
  let seconds = parseInt(timeParts[2], 10);
  let today = new Date(); // 获取当前日期和时间
  today.setHours(hours, minutes, seconds, 0); // 设置小时、分钟、秒和毫秒
  let timestamp = today.getTime(); // 获取时间戳
  // console.log(timestamp); // 输出时间戳
  return timestamp;
};

function formatTimestamp(timestamp) {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份从 0 开始
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
}
/**
 * 计算两个时间戳相差的整数天数
 * @param timestamp1 第一个时间戳（毫秒）
 * @param timestamp2 第二个时间戳（毫秒）
 * @returns 相差的整数天数（绝对值）
 */
function calculateDays(timestamp1, timestamp2) {
  const timestamp = timestamp2; // 给定的时间戳
  const date = new Date(timestamp); // 转为 Date 对象
  date.setHours(0, 0, 0, 0); // 设置为当天的 00:00:00
  const startOfDayTimestamp = date.getTime(); // 获取时间戳

  const timeDifference = Math.abs(startOfDayTimestamp - timestamp1);
  const daysDifference = Math.ceil(timeDifference / (24 * 60 * 60 * 1000));
  return daysDifference;
}

function calculate(timestamp1, timestamp2, timestamp3) {
  const time55 = timestamp1;
  const date = new Date(time55);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // "05"
  const day = String(date.getDate()).padStart(2, "0"); // "07"
  const itemdate = `${year}-${month}-${day} ` + timestamp2;
  const tian = timestamp3 - 1;
  const timestamp = dayjs(itemdate).add(tian, "day").valueOf();
  return timestamp;
}

const nextStepApiatt = async val => {
  const tre = ref(false);
  const type = deepEqualArrays(tripList.value, editCopyClone.value);
  if (type === true) {
    tre.value = true;
    buttonLoading.value = false;
  } else {
    if (Array.isArray(tripList.value) && tripList.value.length === 0) {
      buttonLoading.value = false;
      return ElMessage.error({
        type: "error",
        message: "请新增行程点"
      });
    }
    tripList.value.forEach((it, index) => {
      if (it.timeA) {
        const startTImes = dayjs(it.timeA).format("HH:mm:ss");
        const startTime = openTimeNian.value + " " + startTImes;
        let s = new Date(startTime).getTime();
        if (it.gapDays > 1) {
          tripList.value[index].time = startTImes;
        } else {
          if (openTimeValue.value > s) {
            tripList.value[index].time = "";
            tripList.value[index].timeA = "";
          } else {
            tripList.value[index].time = startTImes;
          }
        }
      }
    });
    const isType = ref(false);
    tripList.value.some((value, index) => {
      if (value.timeA === "") {
        isType.value = true;
        buttonLoading.value = false;
        ElMessage({
          message: "请选择“" + value.title + "”的时间点",
          type: "error"
        });
        return true; // 返回 true 结束循环
      }
      return false; // 返回 false 继续循环
    });
    if (isType.value === true) return;

    const tripData = tripList.value.map(it => {
      return {
        content: it.content,
        gapDays: it.gapDays,
        time: it.time,
        title: it.title
      };
    });
    const params = {
      draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
      draftItineraries: tripData
    };
    const operateLog = {
      operateLogType: "COURSE_MANAGEMENT",
      operateType: `将“${props.periodName}”课期中的课期行程保存在草稿箱`
      // operatorTarget: form.value.name,
    };
    const newList = removeEmptyValues(params);
    let [err, res] = await to(ItineraryNextDraftItinerary(newList, operateLog));
    // console.log("🍧-----err, res-----", err, res);
    if (res) {
      if (res.code === 30040) {
        ElMessage.error(`${res.msg},保存失败。`);
        buttonLoading.value = false;
        tre.value = false;
      } else if (res.code === 200) {
        ElMessage.success("当前资料已保存到草稿箱");
        buttonLoading.value = false;
        tre.value = true;
      }
    } else {
      buttonLoading.value = false;
      tre.value = false;
    }
  }
  return tre.value;
};

// 用于草稿箱点击基础信息校验
defineExpose({
  nextStepApiatt
});
function isJSONString(str) {
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
}

let count = ref(3);
const textLoading = ref(false);
const loading = ref(false);
// 智能填充行程内容的 确认+取消按钮
const editorLoading = ref(false);
const valueHtmlv1 = ref("");
const editorEvt = async val => {
  if (val === "confirm") {
    loading.value = true;
    const text = valueHtmlv1.value
      .replace(/\n/g, "\\n")
      .replace(/\t/g, "\\t")
      .replace(/\r/g, "\\r")
      .replace(/<br\s*\/?>/gi, "\\n");
    const params = {
      description: text,
      questionType: "ITINERARY"
    };
    const newList = removeEmptyValues(params);
    console.log("🎉-----newList-------------------", newList);
    // tripList.value = []
    try {
      // textLoading.value = true
      // count.value = 3
      let { code, data } = await questionAsk(newList);
      if (code == 200 && data?.id) {
        // const intervalId = setInterval(() => {
        //   count.value--;
        //   console.log(`第 ${count.value} 次执行`);
        //   if (count.value <= 0) {
        //     textLoading.value = false
        //     clearInterval(intervalId); // 停止计时器
        //   }
        // }, 1000);
        textLoading.value = true;
        const asyncFn = await vETgetAsyncTask(data?.id);

        handleCancel = asyncFn.stop;
        const task = await asyncFn.task;
        if (task.data.complete && task.data.success) {
          let resData = {};
          // console.log("🦄-----task-----", task);
          if (task.data.res) {
            if (isJSONString(task.data.res)) {
              resData = JSON.parse(task.data.res);
              // console.log("🌵-----resData----成功-", resData);
              tripList.value = resData.map((it, ind) => {
                return {
                  content: it.content,
                  gapDays: it.gapDays,
                  id: ind + 1,
                  timeA: timest(it.time),
                  time: it.time,
                  title: it.title
                };
              });
              ElMessage.success("智能填充成功");
            } else {
              let t = `填充失败，请提供完整的行程信息，包含行程标题、第几天、具体时间及行程内容。`;
              ElMessage.error(t);
            }
            loading.value = false;
          } else {
            ElMessage.error(task.data.errMsg);
            loading.value = false;
          }
        }
      }
    } catch (error) {
      loading.value = false;
      ElMessage.error("智能填充失败");
    }
  } else {
    valueHtmlv1.value = "";
  }
};
const handleCancelFn = () => {
  loading.value = false;
  textLoading.value = false;
  handleCancel?.();
};
// // 自动保存
// const autoSaveInterval = ref(null); // 新增：用于存储定时器引用
// const startAutoSave = () => {
//   clearInterval(autoSaveInterval.value); // 清除已有定时器以防重复
//   autoSaveInterval.value = setInterval(
//     () => {
//       upperBelowEvt('save')
//     },
//     5 * 60 * 1000
//   ); // 每5分钟执行一次
// };
// onUnmounted(() => {
//   clearInterval(autoSaveInterval.value); // 组件卸载时清除定时器
// });
</script>

<template>
  <div class="scheduling">
    <div v-if="loading" class="custom-loading">
      <div class="loading-content">
        <el-icon class="is-loading" color="#409efc" :size="48">
          <Loading />
        </el-icon>

        <p>智能填充中，请耐心等待...</p>
        <el-button
          v-if="textLoading"
          color="rgba(255, 255, 255, 0.3)"
          round
          size="small"
          @click="handleCancelFn"
        >
          <!-- <span v-if="textLoading">（{{ count }}）</span> -->
          取消
        </el-button>
      </div>
    </div>

    <div class="box">
      <div class="timelinebox">
        <div style="display: flex; justify-content: end">
          <el-button type="primary mb-4" @click="isDialog('newly')">
            新增行程点
          </el-button>
        </div>
        <el-timeline class="content">
          <el-timeline-item
            v-for="(item, index) in tripList"
            :key="item.id"
            :timestamp="item.timestamp"
          >
            <div class="inebox">
              <div class="m-w">{{ item.title }}</div>
              <div class="boxpicker">
                <div class="m-w tian">
                  第
                  <el-input-number
                    v-model.trim="item.gapDays"
                    style="width: 120px"
                    type="number"
                    :min="1"
                    :max="100"
                    @change="numberChange(item, index)"
                  />
                  天
                </div>
                <div>
                  <el-time-picker
                    v-model="item.timeA"
                    placeholder="请选择时间点"
                    style="width: 160px"
                    @change="pickerChange(item, index)"
                  />
                </div>
              </div>
            </div>

            <el-button
              type="primary"
              plain
              @click="isDialog('edit', item, index)"
            >
              编辑<el-icon style="margin-left: 6px">
                <Edit />
              </el-icon>
            </el-button>
            <el-button
              type="danger"
              :icon="Delete"
              circle
              @click="deteleEvt(item, index)"
            />

            <div class="item_content">
              <div v-html="item.content" />
            </div>
          </el-timeline-item>
        </el-timeline>

        <el-dialog
          v-model="dialogFormVisible"
          :title="`${dialogTitle}行程点`"
          width="70%"
        >
          <el-form
            ref="ruleFormRef"
            :model="form"
            :rules="rules"
            label-width="100px"
          >
            <el-form-item label="行程点标题" prop="title">
              <el-input
                v-model.trim="form.title"
                placeholder="请输入行程标题"
                style="width: 340px"
                :maxlength="50"
                show-word-limit
                type="text"
              />
            </el-form-item>
            <!-- <el-form-item label="日期" prop="date">
              <el-date-picker
                v-model="form.date"
                placeholder="请选择日期"
                :disabled-date="disabledDate"
                style="width: 240px"
              />
            </el-form-item>
            <el-form-item label="时间点" prop="time">
              <el-time-picker
                v-model="form.time"
                placeholder="请选择时间点"
                style="width: 240px"
              />
            </el-form-item> -->
          </el-form>
          <div class="editer">
            <RichEditor
              v-model="valueHtml"
              height="260px"
              :excludeKeys="['headerSelect', 'fontSize', 'lineHeight']"
              :lineHeight="2"
            />
          </div>
          <template #footer>
            <div class="dialog-footer">
              <el-button @click="cancel">取消</el-button>
              <el-button
                type="primary"
                :loading="submitLoading"
                @click="addItineraryCreate(ruleFormRef)"
              >
                保存
              </el-button>
            </div>
          </template>
        </el-dialog>
      </div>

      <div class="inputbox">
        <div class="title">智能填充行程内容</div>
        <div class="vueEditor">
          <el-input
            v-model.trim="valueHtmlv1"
            type="textarea"
            placeholder="请输入..."
          />
          <div
            v-if="valueHtmlv1 !== ''"
            style="display: flex; justify-content: end; padding: 10px 0px"
          >
            <el-button @click="editorEvt('cancel')"> 取消 </el-button>
            <el-button
              :loading="editorLoading"
              type="primary"
              @click="editorEvt('confirm')"
            >
              确认
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="butt">
      <div v-if="props.isNewEdit === 'new'">
        <el-dropdown>
          <el-button style="margin-right: 10px">退出</el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="it in btnData"
                :key="it.id"
                @click="backEvt('exit', it)"
              >
                {{ it.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button
          type="primary"
          :loading="buttonLoading"
          @click="upperBelowEvt('save')"
        >
          保存草稿箱
        </el-button>
        <el-button type="primary" @click="upperBelowEvt('upper')">
          上一步
        </el-button>
        <el-button
          type="primary"
          :loading="buttonLoading"
          @click="upperBelowEvt('below')"
        >
          下一步
        </el-button>
      </div>
      <div v-if="props.isNewEdit === 'edit'">
        <el-button @click="router.go(-1)">返回</el-button>
        <el-button
          type="primary"
          :loading="buttonLoading"
          @click="saveEvt('saveRet')"
        >
          保存并返回
        </el-button>
        <el-button type="primary" :loading="buttonLoading" @click="saveEvt">
          保存
        </el-button>
      </div>
      <el-button type="primary" @click="aiNewPage(infoShowEnName)">
        AI课程设计
      </el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.custom-loading .circular {
  margin-right: 6px;
  width: 18px;
  height: 18px;
  animation: loading-rotate 2s linear infinite;
}
.custom-loading .circular .path {
  animation: loading-dash 1.5s ease-in-out infinite;
  stroke-dasharray: 90, 150;
  stroke-dashoffset: 0;
  stroke-width: 2;
  stroke: var(--el-button-text-color);
  stroke-linecap: round;
}

.custom-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}
.loading-content {
  text-align: center;
  color: #fff;
}
.loading-content i {
  font-size: 30px;
  margin-bottom: 10px;
}
.loading-content p {
  margin: 10px 0;
}
.el-icon-loading {
  width: 100px;
  height: 100px;
}

.scheduling {
  height: 100%;
  width: 100%;

  .box {
    width: 100%;
    height: calc(100% - 63px);
    position: relative;
    display: flex;
    margin-bottom: 30px;

    .timelinebox {
      width: 60%;
      .content {
        width: 100%;
        height: calc(100vh - 372px);
        overflow-y: auto;
        padding: 10px 0 10px 10px;
        box-sizing: border-box;
        scrollbar-width: none;
        -ms-overflow-style: none;
        .timeLine_title {
          width: 100%;
          display: flex;
          align-items: center;
          gap: 20px;
        }
        .inebox {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 100%;
          width: 100%;
          .boxpicker {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .tian {
              margin-right: 10px;
            }
          }
        }

        .item_content {
          margin-top: 20px;
          width: 100%;
          padding-left: 30px;
          // 添加表格样式
          :deep(table) {
            border-collapse: collapse;
            td,
            th {
              border: 1px solid #a7a6a6;
              min-width: 50px;
              text-align: center;
            }
          }
        }
      }
      .content::-webkit-scrollbar {
        display: none;
      }
    }
    .inputbox {
      width: 40%;
      margin-left: 20px;
      .title {
        padding: 10px 0px;
        min-width: fit-content;
        font-size: 15px;
        font-weight: bold;
      }
    }
  }

  .butt {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.editer {
  height: 350px;
  overflow-y: auto;
  margin-bottom: 5px;
}

.vueEditor {
  width: 100%;
  height: calc(100% - 42px);
  border-radius: 4px;
  padding: 12px 0px 0px;
  // border: solid 1px var(--el-border-color);
}

.m-w {
  min-width: fit-content;
  font-size: 16px;
  font-weight: bold;
}

:deep(.el-textarea .el-textarea__inner) {
  height: calc(100vh - 410px) !important;
}
</style>
