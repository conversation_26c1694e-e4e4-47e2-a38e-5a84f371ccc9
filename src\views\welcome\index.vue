<script setup>
import { ref, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { homeStatistics, adminFindById } from "@/api/institution.js";
import { useUserStoreHook } from "@/store/modules/user";
import { Hide, View } from "@element-plus/icons-vue";
import { decrypt, encryption } from "@/utils/SM4.js";
import base from "@/assets/home/<USER>";
import course from "@/assets/home/<USER>";
import order from "@/assets/home/<USER>";
import accountNumber from "@/assets/home/<USER>";
import teamLeader from "@/assets/home/<USER>";
import lecturer from "@/assets/home/<USER>";
import log from "@/assets/home/<USER>";
import denglv from "@/assets/home/<USER>";
import iphone from "@/assets/home/<USER>";
import sfz from "@/assets/home/<USER>";
defineOptions({
  name: "Welcome"
});

const router = useRouter();
const route = useRoute();
const userInfIdData = ref(null);
const userInfoData = ref({});
const userInfo = ref({});
const platformInfo = ref({});
onMounted(() => {
  // userInfIdData.value = useUserStoreHook().userInfoData.id;
  userInfIdData.value = JSON.parse(localStorage.getItem("userInfoData"))?.id;
  userInfo.value = JSON.parse(localStorage.getItem("userInfoData"));
  getTableList();
  getUserinfo();

  // 密码90天后修改提示弹窗
  if (userInfo.value.lastUpdatedPassword) {
    let newDate = Date.parse(new Date());
    let lastDate = userInfo.value.lastUpdatedPassword;
    const diffMs = Math.abs(lastDate - newDate);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffDays >= 90) {
      dialogVisible.value = true;
    }
  }

  platformInfo.value = JSON.parse(localStorage.getItem("platformInfo") || "{}");
});
// 1	管理员
// 2	讲师
// 3	领队
const tableData = ref([
  {
    id: "1",
    name: "基地数量",
    type: "countComplex",
    number: "0",
    icon: base
  },
  {
    id: "2",
    name: "课程数量",
    type: "countCourse",
    number: "0",
    icon: course
  },
  {
    id: "3",
    name: "订单数量",
    type: "countSubOrders",
    number: "0",
    icon: order
  },
  {
    id: "4",
    name: "账号数量",
    type: "countOrganizationAdmin",
    number: "0",
    icon: accountNumber
  },
  {
    id: "5",
    name: "领队数量",
    type: "countLeader",
    number: "0",
    icon: teamLeader
  },
  {
    id: "6",
    name: "讲师数量",
    type: "countLecturer",
    number: "0",
    icon: lecturer
  }
]);

const colorSttle = [
  {
    background: "#dad9ff",
    color: "#7b79e5"
  },
  {
    background: "#ffe4d3",
    color: "#f78b00"
  },
  {
    background: "#d5f7cc",
    color: "#36ca39"
  },
  {
    background: "#ffd9f9",
    color: "#e64ab8"
  }
];
const editInfo = () => {
  console.log(555);
  router.push({
    path: "/welcome/homeEdit",
    query: { id: userInfIdData.value }
  });
};
const changePassword = () => {
  router.push({
    path: "/welcome/changePassword",
    query: { id: userInfIdData.value }
  });
};
// 获取列表信息
const getTableList = async data => {
  try {
    const { code, data, msg } = await homeStatistics();
    // console.log("🎁-----data-----", code, data, msg);
    if (code == 200) {
      console.log("🐬-----data-----", data.length);
      //循环对象
      Object.keys(data).forEach(key => {
        tableData.value.forEach(item => {
          if (item.type == key) {
            item.number = data[key];
          }
        });
      });
    }
  } catch (error) {
    console.log("catch-----error-----", error);
  }
};
const newData = ref();

// 获取用户信息
const getUserinfo = async data => {
  try {
    const { code, data, msg } = await adminFindById({
      id: userInfIdData.value
    });
    // console.log("🎁-----data-----", code, data, msg);
    if (code == 200) {
      userInfoData.value = data;
      newData.value = JSON.parse(JSON.stringify(data));
    }
  } catch (error) {
    console.log("catch-----error-----", error);
  }
};
const phoneIsEye = ref(true);
const isViewFnPhone = val => {
  phoneIsEye.value = !phoneIsEye.value;
  // return;
  if (phoneIsEye.value) {
    userInfoData.value.phone = newData.value.phone;
  } else {
    userInfoData.value.phone = decrypt(newData.value.phoneCt);
  }
};
const NumberIsEye = ref(true);
const isViewFnNumber = val => {
  NumberIsEye.value = !NumberIsEye.value;
  // return;
  if (NumberIsEye.value) {
    userInfoData.value.idNumber = newData.value.idNumber;
  } else {
    userInfoData.value.idNumber = decrypt(newData.value.idNumberCt);
  }
};
// 弹窗
let dialogVisible = ref(false);
const Goout = () => {
  changePassword();
};
</script>

<template>
  <div class="box">
    <div class="header">
      <div class="header_button">
        <el-button class="primary" type="primary" @click="editInfo">
          编辑信息
        </el-button>
        <el-button class="primary" type="primary" @click="changePassword">
          修改密码
        </el-button>
      </div>
      <div class="header_bg">
        <img class="logBg" :src="log" alt="">
        <div class="welcome">
          <p>Hi,{{ userInfoData.name || "--" }}</p>
          <div class="info_item">
            <template v-for="(item, index) in userInfoData?.roles" :key="index">
              <span
                :style="{
                  background: colorSttle[index].background,
                  color: colorSttle[index].color
                }"
                class="main_item_info_role"
                >{{ item.name || "--" }}</span>
            </template>
          </div>
          <p>欢迎来到</p>
          <!-- <p>星探索&nbsp;|&nbsp;实践课堂</p> -->
          <p>
            {{ platformInfo.platformName || "星探索&nbsp;|&nbsp;实践课堂" }}
          </p>
        </div>
        <div class="info">
          <div class="info_Admin">
            <div class="info_icon">
              <img :src="denglv" alt="">
              <span>登陆账号</span>
            </div>
            <div class="cell_item">
              <span>{{ userInfoData.account || "--" }}</span>
            </div>
            <div />
          </div>
          <el-divider />
          <div class="info_Admin">
            <div class="info_icon">
              <img :src="iphone" alt="">
              <span>手机号</span>
            </div>
            <div class="cell_item">
              <span>{{ userInfoData.phone || "--" }}</span>
            </div>
            <div>
              <span v-if="userInfoData.phone" class="icon">
                <el-icon
                  v-if="phoneIsEye"
                  style="cursor: pointer"
                  @click="isViewFnPhone()"
                >
                  <Hide />
                </el-icon>
                <el-icon
                  v-else
                  style="cursor: pointer"
                  @click="isViewFnPhone()"
                >
                  <View />
                </el-icon>
              </span>
            </div>
          </div>
          <el-divider />
          <div class="info_Admin">
            <div class="info_icon">
              <img :src="sfz" alt="">
              <span>身份证号</span>
            </div>
            <div class="cell_item">
              <span>{{ userInfoData.idNumber || "--" }}</span>
            </div>
            <div>
              <span v-if="userInfoData.idNumber" class="icon">
                <el-icon
                  v-if="NumberIsEye"
                  style="cursor: pointer"
                  @click="isViewFnNumber()"
                >
                  <Hide />
                </el-icon>
                <el-icon
                  v-else
                  style="cursor: pointer"
                  @click="isViewFnNumber()"
                >
                  <View />
                </el-icon>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="main">
      <div class="main_lift">
        <template v-for="(item, index) in tableData" :key="index">
          <div class="main_item">
            <img class="main_item_img" :src="item.icon" alt="">
            <div class="main_item_info">
              <p>{{ item.name }}</p>
              <span>{{ item.number }}</span>
            </div>
          </div>
        </template>
      </div>
      <!-- <div class="main_right" /> -->
    </div>
    <!-- 首页弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="风险提示"
      width="610px"
      align-center
      :append-to-body="true"
    >
      <div class="checkboxs">
        您的密码已经超过90天未修改，存在安全风险，请及时修改密码
      </div>

      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">我知道了</el-button>
        <el-button type="primary" @click="Goout"> 修改密码 </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.header {
  // display: flex;
  // width: 84vw;
  height: 24vw;
  // height: 452px;
  background-color: #ffffff;
  border-radius: 8px;
  // padding: 39px;
  padding: 2vw;
  box-sizing: border-box;
  .header_button {
    display: flex;
    justify-content: flex-end;
    // margin-bottom: 32px;
    margin-bottom: 2vw;
    .primary {
      width: 5vw;
      height: 2vw;
      font-size: 0.83vw;
    }
  }
  .header_bg {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .logBg {
      // width: 344px;
      // height: 244px;
      width: 18vw;
      height: 13vw;
      // object-fit: cover;
      object-fit: contain;
    }
    .welcome {
      width: 30%;
      // height: 189px;
      :nth-child(1) {
        font-family: SourceHanSansCN-Bold;
        // font-size: 30px;
        font-size: 2vw;
        font-weight: normal;
        font-stretch: normal;
        letter-spacing: 0px;
        color: #409eff;
      }
      :nth-child(3) {
        font-family: SourceHanSansCN-Heavy;
        // font-size: 40px;
        font-size: 2vw;
        font-weight: normal;
        font-stretch: normal;
        letter-spacing: 0px;
        color: #333333;
      }
      :nth-child(4) {
        font-family: SourceHanSansCN-Heavy;
        // font-size: 40px;
        font-size: 2vw;
        font-weight: normal;
        font-stretch: normal;
        letter-spacing: 0px;
        color: #333333;
      }
      .info_item {
        display: flex;
        align-items: center;
        .main_item_info_role {
          // width: 73px;
          // height: 28px;
          padding: 2px 10px;
          border-radius: 14px;
          font-family: SourceHanSansCN-Regular;
          // font-size: 16px;
          font-size: 1vw;
          font-weight: normal;
          font-stretch: normal;
          letter-spacing: 0px;
          display: inline-block;
          text-align: center;
          // margin-right: 15px;
          margin-right: 1vw;
          // margin-top: 11px;
          margin-top: 1vw;
          // margin-bottom: 24px;
          margin-bottom: 1vw;
        }
        :last-child {
          margin-right: 0;
        }
      }
    }
    .info {
      width: 33%;
      .info_Admin {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 1vw;
        .info_icon {
          display: flex;
          justify-content: space-between;
          align-items: center;
          img {
            // width: 19px;
            // height: 19px;
            width: 1vw;
            height: 1vw;
            object-fit: contain;
            // object-fit: cover;
            // margin-right: 16px;
            margin-right: 1vw;
          }
          span .cell_item {
            font-family: SourceHanSansCN-Bold;
            // font-size: 18px;
            font-size: 1vw;
            font-weight: normal;
            font-stretch: normal;
            letter-spacing: 0px;
            color: #333333;
          }
        }
        :nth-child(3) {
          // width: 20px;
          width: 1vw;
        }
        // .cell_item {
        //   font-size: 18px;
        //   font-weight: normal;
        //   font-stretch: normal;
        //   letter-spacing: 0px;
        //   color: #333333;
        // }
      }
    }
  }
}
:deep(.el-divider--horizontal) {
  margin: 2vw 0;
}
.main {
  display: flex;
  // padding: 20px;
  // background: #ffff;

  .main_lift {
    display: flex;
    flex-wrap: wrap;
    // gap: 23px;
    // justify-content: space-around;
    justify-content: space-between;
    width: 100%;

    .main_item {
      margin-top: 24px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-around;
      width: 32.4%;
      background: #ffff;
      // height: 208px;
      height: 10.83vw;
      // border-radius: 8px;
      border-radius: 0.42vw;

      // padding: 54px 110px;
      .main_item_img {
        // width: 85px;
        // height: 92px;
        width: 4.43vw;
        height: 4.79vw;
        // object-fit: cover;
        object-fit: contain;
        // margin-right: 120px;
      }
      .main_item_info {
        // width: 95px;
        // height: 100px;
        p {
          font-family: SourceHanSansCN-Bold;
          // font-size: 24px;
          font-size: 1.25vw;
          font-weight: normal;
          font-stretch: normal;
          letter-spacing: 0px;
          color: #333333;
        }
        span {
          font-family: SourceHanSansCN-Heavy;
          // font-size: 48px;
          font-size: 2.5vw;
          font-weight: normal;
          font-stretch: normal;
          letter-spacing: 0px;
          color: #409eff;
        }
      }
    }
  }

  .main_right {
    width: 50%;
  }
}

:deep(.my-label) {
  background: #e1f5ff !important;
}
.cell_item {
  display: flex;
  justify-content: center;
  .icon {
    display: flex;
    align-items: center;
    margin-left: 10px;
  }
}
// 弹窗样式
.checkboxs {
  width: 100%;
  .checkboxs_title {
    margin-top: 10px;
    display: flex;
    .titles {
      white-space: nowrap;
      font-size: 16px;
      margin-right: 8px;
    }
    .title_content {
      display: flex;
      flex-wrap: wrap;
      flex-grow: 1;
    }
  }
}

:deep(.el-dialog__body) {
  padding-top: 0;
  padding-bottom: 0;
}
.dialog-footer {
  width: 100%;
  margin-top: 30px;
  display: inline-block;
  text-align: right;
}
</style>
