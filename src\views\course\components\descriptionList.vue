<script setup>
import { ref, onMounted, onBeforeMount } from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  coursePeriodFind,
  coursePeriodOffline,
  coursePeriodOnline,
  periodOpenGroupOrder,
  courseFindId
} from "@/api/course.js";
import { ElMessage, ElMessageBox } from "element-plus";
import { requestTo } from "@/utils/http/tool";
import dayjs from "dayjs";
import { COURSE_PERIOD_ENUM, AUDIT_ENUM } from "@/utils/enum.js";
import { Warning } from "@element-plus/icons-vue";
const props = defineProps({
  periodId: {
    type: Number,
    default: 0
  },
  courseId: {
    type: Number,
    default: 0
  }
});
const emit = defineEmits(["update:openTime", "name"]);
const route = useRoute();
const router = useRouter();
// 表头
const tableHeader = ref([
  {
    id: "1",
    label: "课期名",
    value: "",
    width: "107px"
  },
  {
    id: "2",
    label: "期号",
    value: "",
    width: "107px"
  },
  {
    id: "3",
    label: "课期ID",
    value: "",
    width: "107px"
  },
  {
    id: "4",
    label: "创建时间",
    value: "",
    width: "107px"
  },
  {
    id: "5",
    label: "领队",
    value: "",
    width: "107px"
  },
  {
    id: "6",
    label: "讲师",
    value: "",
    width: "107px"
  },
  {
    id: "7",
    label: "开课时间",
    value: "",
    width: "107px"
  },
  {
    id: "8",
    label: "报名截止",
    value: "",
    width: "107px"
  },
  {
    id: "9",
    label: "基地",
    value: "",
    width: "107px"
  },
  {
    id: "10",
    label: "购买类型",
    value: "",
    width: "107px"
  },
  {
    id: "11",
    label: "课期状态",
    value: "",
    width: "107px",
    state: ""
  },
  {
    id: "12",
    label: "审核状态",
    value: "",
    width: "107px",
    state: ""
  }
]);
const state = ref();
const url = ref();
const openTime = ref(null);
const srcList = ref([]);
// 查询课期详情
const getCoursePeriodFind = async () => {
  let [err, res] = await requestTo(
    coursePeriodFind({ id: route.query.periodId || props.periodId })
  );
  if (res) {
    // console.log("🐬-----res1111-----", res);

    tableHeader.value[0].value = res.name || "--";
    tableHeader.value[1].value = res.termNumber || "0";
    tableHeader.value[2].value = res.id || "--";
    tableHeader.value[3].value =
      dayjs(res.createdAt).format("YYYY-MM-DD HH:mm:ss") || "--";
    tableHeader.value[8].value = res.complex?.name || "--";
    tableHeader.value[6].value =
      dayjs(res.openTime).format("YYYY-MM-DD HH:mm:ss") || "--";
    tableHeader.value[7].value =
      dayjs(res.signUpDeadline).format("YYYY-MM-DD HH:mm:ss") || "--";
    // 保存openTime数据并发出更新事件
    openTime.value = res.openTime;
    emit("update:openTime", res.openTime);
    emit("name", res.name);
    if (res.leaders?.length) {
      let learderList = [];
      res.leaders.map(item => {
        learderList.push(item.name);
      });
      tableHeader.value[4].value = learderList.join(" 、") || "--";
    }
    if (res.lecturers?.length) {
      let lecturersList = [];
      res.lecturers.map(item => {
        lecturersList.push(item.name);
      });
      tableHeader.value[5].value = lecturersList.join(" 、") || "--";
    }
    tableHeader.value[9].value =
      res.buyType === "ORDINARY"
        ? "普通单"
        : res.buyType === "PRIVATE_DOMAIN_GROUP_ORDER"
          ? "团购单"
          : "--";
    // if (res.minPeopleNumber && res.maxPeopleNumber) {
    //   tableHeader.value[10].value =
    //     res.minPeopleNumber + "-" + res.maxPeopleNumber;
    // } else if (res.minPeopleNumber) {
    //   tableHeader.value[10].value = res.minPeopleNumber;
    // } else if (res.maxPeopleNumber) {
    //   tableHeader.value[10].value = res.maxPeopleNumber;
    // } else {
    //   tableHeader.value[10].value = "--";
    // }
    tableHeader.value[10].value =
      COURSE_PERIOD_ENUM[res.coursePeriodState]?.label || "--";
    tableHeader.value[10].state = res.offlineType || "--";
    tableHeader.value[11].value = AUDIT_ENUM[res.reviewState]?.label || "无";
    tableHeader.value[11].state = res.reviewState || "NONE";
    tableHeader.value[11].opinion = res.opinion || "无";
    state.value = res.coursePeriodState;
    if (res.cover?.length) {
      res.cover.map(item => {
        srcList.value.push(item?.uploadFile?.url);
      });
      url.value = res.cover[0]?.uploadFile?.url;
    }
  } else {
    console.log("🐳-----err-----", err);
  }
};
// 查询课程详情
const getCourseFind = async () => {
  let [err, res] = await requestTo(courseFindId({ id: props.courseId }));
  if (res) {
    // console.log("🦄res---------22--------------------->", res);
    tableHeader.value[0].value = res.name || "--";
    tableHeader.value[1].value = res.id || "--";
    tableHeader.value[2].value =
      dayjs(res.createdAt).format("YYYY-MM-DD HH:mm:ss") || "--";
    tableHeader.value[3].value = res.courseType?.name || "--";
    tableHeader.value[4].value = res.maxPeopleNumber || "--";
    tableHeader.value[5].value = res.freeze;
    if (res.files?.length) {
      res.files.map(item => {
        srcList.value.push(item?.uploadFile?.url);
      });
      url.value = res.files[0]?.uploadFile?.url;
    }
  } else {
    console.log("🐳-----err-----", err);
  }
};
onBeforeMount(() => {
  if (props.periodId) {
    tableHeader.value = [
      {
        id: "1",
        label: "课期名",
        value: "",
        width: "107px"
      },
      {
        id: "2",
        label: "期号",
        value: "",
        width: "107px"
      },
      {
        id: "3",
        label: "课期ID",
        value: "",
        width: "107px"
      },
      {
        id: "4",
        label: "创建时间",
        value: "",
        width: "107px"
      },
      {
        id: "5",
        label: "领队",
        value: "",
        width: "107px"
      },
      {
        id: "6",
        label: "讲师",
        value: "",
        width: "107px"
      },
      {
        id: "7",
        label: "开课时间",
        value: "",
        width: "107px"
      },
      {
        id: "8",
        label: "报名截止",
        value: "",
        width: "107px"
      },
      {
        id: "9",
        label: "基地",
        value: "",
        width: "107px"
      },
      {
        id: "10",
        label: "购买类型",
        value: "",
        width: "107px"
      },
      {
        id: "11",
        label: "课期状态",
        value: "",
        width: "107px",
        state: ""
      },
      {
        id: "12",
        label: "审核状态",
        value: "",
        width: "107px",
        state: ""
      }
    ];
  } else {
    tableHeader.value = [
      {
        id: "1",
        label: "课程名",
        value: "",
        width: "107px"
      },
      {
        id: "2",
        label: "课程ID",
        value: "",
        width: "107px"
      },
      {
        id: "3",
        label: "创建时间",
        value: "",
        width: "107px"
      },
      {
        id: "4",
        label: "课程类型",
        value: "",
        width: "107px"
      },
      {
        id: "5",
        label: "人数上限",
        value: "",
        width: "107px"
      },
      {
        id: "6",
        label: "课程状态",
        value: "",
        width: "107px"
      }
    ];
  }
});
onMounted(() => {
  if (props.periodId) {
    getCoursePeriodFind();
  } else {
    getCourseFind();
  }
});
</script>

<template>
  <div class="curse-table">
    <el-descriptions
      title=""
      :column="periodId ? 4 : 3"
      border
      style="width: 1500px"
    >
      <template v-for="(item, index) in tableHeader" :key="index">
        <el-descriptions-item width="120px" label-align="center">
          <template #label>
            <div class="cell-item">{{ item.label }}</div>
          </template>
          <div
            v-if="
              (item.label === '审核状态' && item.state === 'OFFLINE_REJECT') ||
              item.state === 'ONLINE_REJECT'
            "
          >
            <el-tooltip
              class="box-item"
              title=""
              :content="item.opinion ? item.opinion : '无'"
              placement="bottom"
              effect="light"
            >
              <div class="warning">
                {{ item.value }}
                <el-icon style="color: red; margin-left: 3px">
                  <Warning />
                </el-icon>
              </div>
            </el-tooltip>
          </div>
          <div
            v-else-if="
              (item.label === '课期状态' &&
                item.state === 'PLATFORM_OFFLINE') ||
              item.state === 'PLATFORM_CLOSE_GROUP'
            "
          >
            <el-tooltip
              class="box-item"
              title=""
              :content="
                item.state === 'PLATFORM_OFFLINE'
                  ? '课期已被强制下架，请联系平台客服了解情况'
                  : '课期已被强制取消定制，请联系平台客服了解情况'
              "
              placement="bottom"
              effect="light"
            >
              <div class="warning">
                {{ item.value }}
                <el-icon style="color: red; margin-left: 3px">
                  <Warning />
                </el-icon>
              </div>
            </el-tooltip>
          </div>
          <div
            v-else-if="item.label === '课程状态'"
            :style="{ color: item.value === true ? 'red' : '' }"
          >
            {{ item.value === true ? "冻结" : "正常" }}
          </div>
          <div v-else>
            {{ item.value }}
          </div>
        </el-descriptions-item>
      </template>
    </el-descriptions>
    <div :class="periodId ? 'img' : 'img1'">
      <!-- <img src="@/assets/user.jpg" alt="" /> -->
      <el-image
        :src="url"
        :zoom-rate="1.2"
        :max-scale="7"
        :min-scale="0.2"
        :preview-src-list="srcList"
        :hide-on-click-modal="true"
        show-progress
        :initial-index="0"
        fit="cover"
        class="img-pic"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.curse-table {
  display: flex;
  justify-content: space-between;
  width: 100%;
  background-color: #fff;
  box-sizing: border-box;
  padding: 24px 20px;
  margin-bottom: 20px;
  .img {
    width: 145px;
    //   min-height: 120px;
    height: 120px;
    margin-left: 20px;

    .img-pic {
      width: 145px;
      height: 120px;
      // object-fit: cover;
    }
  }
  .img1 {
    width: 145px;
    //   min-height: 120px;
    height: 85px;
    margin-left: 20px;

    .img-pic {
      width: 145px;
      height: 85px;
      // object-fit: cover;
    }
  }
  .warning {
    display: flex;
    align-items: center;
  }
}
:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}
</style>
