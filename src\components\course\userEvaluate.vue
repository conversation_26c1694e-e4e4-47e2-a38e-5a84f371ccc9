<script setup>
import { onMounted, ref } from "vue";
import { formatTime } from "@/utils/index";
import { commentsFindAll, upOnlyMe } from "@/api/course.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { to } from "@iceywu/utils";
import { useRouter, useRoute } from "vue-router";
const props = defineProps({
  periodName: {
    type: String,
    default: ""
  }
});
const emit = defineEmits(["tabShow", "evaluate"]);
const router = useRouter();
const route = useRoute();
onMounted(() => {
  getTableList();
  // tableData.value = Array.from({ length: 20 }).fill({
  //   completed_at: "0",
  //   pending_review: 1,
  //   createdAt: 172627952334,
  //   name: "2024-09-14习题任务",
  //   termNumber: 1,
  //   courseTypeName: "手工",
  //   state: 1,
  //   organizationName: "2024-09-14习题任务",
  //   freeze: 1
  // });
});
// 表格数据
const tableData = ref([]);
const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort,
    coursePeriodId: route.query.periodId
  };
  // console.log("🍧-----paramsData-----", paramsData);
  const [err, result] = await requestTo(commentsFindAll(paramsData));
  // console.log("🎁-----result-----", result);
  if (result) {
    tableData.value = result?.content;

    params.value.totalElements = result.totalElements;
  }
  if (err) {
    ElMessage.error(err);
  }
  getListLoading.value = false;
};
//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTableList();
};
const handleSizeChange = val => {
  params.value.page = 1;
  params.value.size = val;
  getTableList();
};
//搜索
const searchData = () => {
  params.value.page = 1;
  getTableList();
};
// 详情
const detailEvt = row => {
  let evaluateData = {
    id: row?.id || "",
    showcontent: "evaluate"
  };
  // console.log("🍭evaluateData-----------333------------------->", evaluateData);
  emit("tabShow", false);
  emit("evaluate", evaluateData);
};
// 是否公开
const showContentEvt = (row, bool) => {
  let freezeText =
    bool === true ? "确定不公开该条评论吗？" : "确定要公开该条评论吗？";
  let title = bool === true ? "不公开" : "公开";
  ElMessageBox.confirm(`${freezeText}`, `${title}评论`, {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(() => {
      isOnlyMeApi(row, bool);
    })
    .catch(() => {
      // ElMessage({
      //   type: 'info',
      //   message: 'Delete canceled',
      // })
    });
};
const operateLog = ref({});
const isOnlyMeApi = async (row, bool) => {
  const params = {
    id: row.id,
    onlyMe: bool
  };
  if (bool === true) {
    if (props.periodName && row.userName) {
      operateLog.value = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `不公开“${props.periodName}”课期中的“${row.userName}”用户评价`
      };
    } else {
      operateLog.value = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `不公开“${props.periodName}”课期中的用户评价`
      };
    }
  } else {
    if (props.periodName && row.userName) {
      operateLog.value = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `公开“${props.periodName}”课期中的“${row.userName}”用户评价`
      };
    } else {
      operateLog.value = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `公开“${props.periodName}”课期中的用户评价`
      };
    }
  }

  const [err, res] = await to(upOnlyMe(params, operateLog.value));
  if (res.code === 200) {
    ElMessage({
      type: "success",
      message: bool === true ? "不公开成功" : "公开成功"
    });
    getTableList();
  } else {
    ElMessage({
      type: "error",
      message: bool === true ? `不公开失败,${res.msg}` : `公开失败,${res.msg}`
    });
  }
  if (err) {
    ElMessage({
      type: "error",
      message: err?.msg
    });
  }
};
</script>

<template>
  <div class="containers">
    <div class="con_table">
      <el-table
        :data="tableData"
        table-layout="fixed"
        :header-cell-style="{ backgroundColor: '#fafafa', color: '#565353' }"
        highlight-current-row
        :style="{ height: '100%' }"
      >
        <el-table-column
          prop="userName"
          label="用户名"
          align="left"
          fixed
          width="130"
        >
          <template #default="scope">
            {{ scope.row.userName || "--" }}
          </template>
        </el-table-column>

        <el-table-column width="200px" prop="createdAt" label="评价时间">
          <template #default="scope">
            <div>
              {{
                formatTime(scope.row?.createdAt, "YYYY-MM-DD HH:mm:ss") || "--"
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="rating" label="分数" align="left">
          <template #default="scope">
            <div>
              {{ scope.row.rating || 0 }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="content"
          label="评论内容"
          align="left"
          width="300"
        >
          <template #default="scope">
            <span class="no-wrap-text">{{ scope.row.content || "--" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          width="200px"
          prop="replies.createdAt"
          label="回复时间"
        >
          <template #default="scope">
            <div>
              {{
                formatTime(
                  scope.row?.replies?.createdAt,
                  "YYYY-MM-DD HH:mm:ss"
                ) || "--"
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="replies?.content"
          label="机构回复"
          align="left"
          width="300"
        >
          <template #default="scope">
            <span class="no-wrap-text">{{
              scope.row.replies?.content || "--"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" align="left" width="180px">
          <template #default="{ row }">
            <div class="option">
              <div class="btnse" @click="detailEvt(row)">详情</div>
              <div class="btnse">
                <div
                  v-if="row.onlyMe === false"
                  class="freeze"
                  @click="showContentEvt(row, true)"
                >
                  不公开
                </div>
                <div
                  v-else
                  class="nofreeze"
                  @click="showContentEvt(row, false)"
                >
                  公开
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="con_pagination">
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="params.page"
        v-model:page-size="params.size"
        class="pagination"
        background
        :page-sizes="[15, 30, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="params.totalElements"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.containers {
  height: 100%;
  display: flex;
  flex-direction: column;
  //   box-sizing: border-box;
  //   width: calc(100% - 48px);
  //   height: 100%;
  //   padding: 24px;
  //   background: #fff;

  .con_search {
    display: flex;
    align-items: center;
    width: 100%;
    height: fit-content;
    margin-bottom: 12px;
  }

  .con_table {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
    // width: calc(100% - 25px);
    // margin-bottom: 24px;
    // margin-left: 25px;
    width: 100%;
    margin: 10px 0 12px 0;

    .option {
      display: flex;

      .btnse {
        display: flex;
        // margin-left: 16px;
        color: #409eff;
        cursor: pointer;
        white-space: nowrap;
        margin-right: 20px;

        .nofreeze {
          color: #f56c6c;
          cursor: pointer;
        }
      }
      .other {
        margin-left: 16px;
      }
    }
    /* 强制不换行，超出显示省略号 */
    .no-wrap-text {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: inline-block;
      width: 100%;
    }
  }

  .con_pagination {
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }
}
:deep(.el-popper.is-dark) {
  max-width: 500px !important;
  word-break: break-all !important;
}
</style>
