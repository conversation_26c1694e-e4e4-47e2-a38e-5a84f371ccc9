<script setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  courseAdd,
  complexId,
  courseTypeFind,
  courseEdite,
  courseFindId
} from "@/api/course.js";
import { findAllCourseType } from "@/api/period.js";
import { requestTo } from "@/utils/http/tool";
import { ElIcon, ElMessage, ElMessageBox, ElLoading } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import { uploadFile } from "@/utils/upload/upload.js";
import { courseStore } from "@/store/modules/course.js";

import { createCourse } from "@/utils/createTestData.js";
import { to, isEmpty } from "@iceywu/utils";
createCourse();

const useCourseStore = courseStore();
const { courseInfo } = useCourseStore;
const router = useRouter();
const route = useRoute();
const imgText = ref(
  "支持上传png、jpg、jpeg图片格式，最多上传9张，最佳尺寸：750*750px，单张大小不超过10MB"
);
const ruleFormRef = ref();

const ruleForm = reactive({
  name: "",
  type: "",
  base: "",
  Minimum: "",
  leader: "",
  Maximum: "",
  teacher: "",
  fileList: [],
  maxAge: "", //年龄最大
  minAge: "", //年龄最小
  tags: [] //课程亮点标签
});
const rules = reactive({
  name: [
    { required: true, message: "请输入课程名", trigger: "blur" }
    // { min: 3, max: 5, message: "Length should be 3 to 5", trigger: "blur" }
  ],
  type: [
    { required: true, message: "请选择课程类型", trigger: "change" }
    // { min: 3, max: 5, message: "Length should be 3 to 5", trigger: "blur" }
  ],
  phone: [
    { required: true, message: "请输入手机号", trigger: "blur" }
    // { min: 3, max: 5, message: "Length should be 3 to 5", trigger: "blur" }
  ],
  Minimum: [
    { pattern: /^\d+$/, message: "必须为整数，不允许小数", trigger: "blur" }
  ],
  Maximum: [
    { pattern: /^\d+$/, message: "必须为整数，不允许小数", trigger: "blur" }
  ]
});
const formData = ref([
  {
    label: "课程名",
    type: "input",
    prop: "name",
    check: true,
    placeholder: "请输入课程名",
    width: "400px",
    maxLength: 30
  },
  {
    label: "课程类型",
    type: "cascader",
    prop: "type",
    check: true,
    placeholder: "请选择课程类型",
    width: "400px",
    options: []
  },
  {
    label: "课程亮点标签",
    type: "tag",
    prop: "tags",
    check: false,
    placeholder: "请输入人数上限",
    width: "400px",
    tags: []
  },
  {
    label: "课程年龄段",
    type: "inputToInput",
    prop: "age",
    check: false,
    placeholder: "请输入年龄段",
    width: "400px"
  }
  // {
  //   label: "基地",
  //   type: "select",
  //   prop: "base",
  //   check: true,
  //   placeholder: "请选择基地",
  //   width: "240px",
  //   options: []
  // },
  // {
  //   label: "人数下限",
  //   type: "input",
  //   prop: "Minimum",
  //   check: false,
  //   placeholder: "请输入人数下限",
  //   width: "240px"
  // },
  // {
  //   label: "人数上限",
  //   type: "input",
  //   prop: "Maximum",
  //   check: false,
  //   placeholder: "请输入人数上限",
  //   width: "350px"
  // }
  // {
  //   label: "领队",
  //   type: "selectMultiple",
  //   prop: "leader",
  //   check: false,
  //   placeholder: "请选择领队",
  //   width: "400px",
  //   options: []
  // },
  // {
  //   label: "讲师",
  //   type: "selectMultiple",
  //   prop: "teacher",
  //   check: false,
  //   placeholder: "请选择讲师",
  //   width: "400px",
  //   options: []
  // }
]);

const dialogImageUrl = ref("");
const dialogVisible = ref(false);
const submitLoading = ref(false);
const handlePictureCardPreview = uploadFile => {
  dialogImageUrl.value = uploadFile.url;
  dialogVisible.value = true;
};
// 图片上传
const fileList = ref([]);
// 只在beforeUpload中进行验证，不进行实际上传
const beforeUpload = file => {
  let imgType = ["jpg", "jpeg", "png"];
  let fileType = file.type.split("/")[1];

  if (!imgType.includes(fileType)) {
    ElMessage.error("上传图片格式不支持，请上传png，jpg， jpeg格式的图片");
    return false;
  }

  if (ruleForm.fileList?.length > 8) {
    ElMessage.error("上传图片数量过多，最多上传9张图片");
    return false;
  }

  if (file.type.startsWith("image/") === false) {
    ElMessage.error("上传类型不支持");
    return false;
  }

  // 验证通过，允许上传
  return true;
};

// 自定义上传请求
const customUpload = async ({ file }) => {
  // 先移除同uid的项，防止重复
  fileList.value = fileList.value.filter(f => f.uid !== file.uid);
  fileList.value = [...fileList.value]; // 添加临时文件到列表中

  try {
    // 使用uploadFile函数，并通过回调函数更新上传进度和状态
    await uploadFile(file, progress => {
      // 根据回调返回的状态更新文件列表
      const currentFile = {
        name: file.name,
        uid: file.uid,
        status: progress.status || "uploading", // 使用返回的状态
        percentage: progress.percent || 0
      };

      // 如果上传成功且有URL，添加URL
      if (progress.status === "success" && progress.data && progress.data.url) {
        currentFile.url = progress.data.url;

        // 添加到ruleForm.fileList用于表单提交
        if (progress.data.fileIdentifier) {
          // 先移除同uid的项，防止重复
          ruleForm.fileList = ruleForm.fileList.filter(
            item => item.uid !== file.uid
          );

          ruleForm.fileList.push({
            fileIdentifier: progress.data.fileIdentifier,
            fileType: "PHOTO",
            uid: file.uid
          });
        }
      }

      // 根据状态显示消息
      if (progress.status === "fail") {
        ElMessage.error(progress.errMessage || "上传失败，请重试");
        // 上传失败时，直接从文件列表中移除该文件，不显示失败状态
        fileList.value = fileList.value.filter(f => f.uid !== file.uid);
      } else {
        // 上传成功或上传中状态，更新文件状态console.log();
        fileList.value = fileList.value.filter(f => f.uid !== file.uid);
        fileList.value = [...fileList.value, currentFile];
      }
    });
  } catch (error) {
    console.error("上传出错:", error);

    // 上传失败时，直接从文件列表中移除该文件，不显示失败状态
    fileList.value = fileList.value.filter(f => f.uid !== file.uid);

    ElMessage.error(error.message || "上传过程中发生错误");
  }
};
//删除图片视频
const handleRemove = (uploadFile, uploadFiles) => {
  ruleForm.fileList = ruleForm.fileList.filter(it => it.uid !== uploadFile.uid);
  fileList.value = fileList.value.filter(it => it.uid !== uploadFile.uid);
};
const submitForm = async formEl => {
  if (!formEl) return;
  if (submitLoading.value) return;
  submitLoading.value = true;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      const params = {
        name: ruleForm.name,
        courseTypeId: ruleForm.type
      };
      if (route.query.type === "edite") {
        params.id = Number(route.query.id);
      }
      if (ruleForm.fileList?.length > 0) {
        ruleForm.fileList = ruleForm.fileList.map((it, index) => {
          return {
            sortOrder: index + 1,
            fileIdentifier: it.fileIdentifier,
            fileType: it.fileType
          };
        });
        params.files = ruleForm.fileList;
      }
      if (ruleForm.minAge) {
        params.minAge = ruleForm.minAge;
      }
      if (ruleForm.maxAge) {
        params.maxAge = ruleForm.maxAge;
      }
      const operateLog = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType:
          route.query.type === "create"
            ? `创建了“${ruleForm.name}”课程`
            : `编辑了“${ruleForm.name}”课程`
      };
      if (formData.value[2].tags && formData.value[2].tags.length > 0) {
        params.tags = formData.value[2].tags;
      } else {
        delete params.tags;
      }
      // console.log("🍧params----------eeee-------------------->", params);
      // return
      let { code, data, msg } =
        route.query.type === "create"
          ? await courseAdd(params, operateLog)
          : await courseEdite(params, operateLog);
      if (code === 200) {
        if (route.query.type === "create") {
          ElMessage.success("创建成功");
          router.replace({
            path: "/course/courseDetails",
            query: { id: data.id }
          });
        } else {
          router.replace({
            path: "/course/courseDetails",
            query: { id: route.query.id }
          });
          ElMessage.success("编辑成功");
        }
      } else {
        let text = route.query.type === "create" ? "创建失败" : "编辑失败";
        ElMessage.error(`${text},${msg}`);
      }
    } else {
      console.log("error submit!", fields);
    }
  });
  submitLoading.value = false;
};
const complexIdApi = async () => {
  let [err, res] = await requestTo(complexId());
  if (res) {
    formData.value[2].options = res.map(it => {
      return {
        ...it,
        label: it.name,
        value: it.id
      };
    });
  }
};
const courseTypeFindApi = async () => {
  let [err, res] = await requestTo(findAllCourseType());
  // console.log("🎁res--------------jidi--222-------------->", res);
  // formData.value[1].options = res.map(it => {
  //   return {
  //     ...it,
  //     name: it.name,
  //     value: it.id
  //   };
  // });
  if (res) {
    formData.value[1].options = transformArray(res);
  }
};
function transformArray(inputArray) {
  return inputArray.map(item => {
    const newItem = {
      value: item.id,
      label: item.name
    };

    // 如果有 children，则递归转换
    if (item.children && item.children.length > 0) {
      newItem.children = transformArray(item.children);
    }

    return newItem;
  });
}
const courseTypeChange = val => {
  ruleForm.type = val[val.length - 1];
};
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    id: route.query.id
  };
  const [err, result] = await requestTo(courseFindId(paramsData));
  if (result) {
    // console.log("🐠result------------------------------>", result);
    ruleForm.name = result.name || "";
    ruleForm.base = result.complex?.id || "";
    ruleForm.type = result.courseType?.id || "";
    ruleForm.minAge = result.minAge || "";
    ruleForm.maxAge = result.maxAge || "";
    if (result.tags) {
      ruleForm.tags = result.tags;
      formData.value[2].tags = result.tags;
    }

    if (result?.files?.length) {
      // 清空现有数组
      ruleForm.fileList = [];

      // 为每张图片生成一个唯一的UID，确保两个列表中相同图片使用相同的UID
      const uploadedFiles = result.files.map(item => {
        // 为每个图片生成唯一的UID，并在两个列表中保持一致
        const uniqueUid =
          item.uploadFile.uid ||
          Date.now() + Math.random().toString(36).substring(2, 10);
        return {
          fileIdentifier: item.uploadFile.fileIdentifier,
          fileType: "PHOTO",
          uid: uniqueUid
        };
      });

      // 使用解构赋值添加到数组
      ruleForm.fileList = [...uploadedFiles];

      // 同样处理显示用的文件列表，使用相同的UID
      const displayFiles = result.files.map((item, index) => ({
        name: item.uploadFile.fileName,
        url: item.uploadFile.url,
        uid: uploadedFiles[index].uid, // 使用与uploadedFiles相同的UID
        status: "success",
        percentage: 100
      }));

      fileList.value = [...displayFiles];

      console.log("🎁-----编辑模式：已加载现有图片-----", fileList.value);
    }
  } else {
    ElMessage.error(err);
  }
  getListLoading.value = false;
};
// 课程亮点标签
const inputValue = ref("");
const inputVisible = ref(false);
const InputRef = ref();
const handleClose = tag => {
  formData.value[2].tags.splice(formData.value[2].tags.indexOf(tag), 1);
};
const showInput = () => {
  inputVisible.value = true;
  nextTick(() => {
    InputRef.value.input.focus();
  });
};

const handleInputConfirm = () => {
  if (inputValue.value) {
    if (inputValue.value.length > 12) {
      ElMessage.error("输入的字数不能超过12个");
    }
    const item = formData.value[2].tags.find(it => it === inputValue.value);
    if (item) {
      ElMessage.error("标签名称不可重复，请重新输入");
    }
    if (!item && inputValue.value.length <= 12) {
      formData.value[2].tags.push(inputValue.value);
    }
  }
  inputVisible.value = false;
  inputValue.value = "";
};
const handleChangeMax = () => {
  if (Number(ruleForm.maxAge) < 0) {
    ruleForm.maxAge = 2;
    ElMessage.error("最大年龄不能小于0");
  }
  if (Number(ruleForm.maxAge) > 150) {
    ruleForm.maxAge = 150;
    ElMessage.error("最大年龄不能大于150");
  }
  if (
    !isEmpty(ruleForm.minAge) &&
    !isEmpty(ruleForm.maxAge) &&
    Number(ruleForm.minAge) >= Number(ruleForm.maxAge)
  ) {
    ruleForm.minAge = 1;
    ElMessage.error("最大年龄不能小于等于最小年龄");
    if (!isEmpty(ruleForm.maxAge) && Number(ruleForm.maxAge) <= 0) {
      ruleForm.maxAge = 2;
    }
  }
  if (
    typeof ruleForm.maxAge === "number" &&
    !isNaN(ruleForm.maxAge) &&
    !Number.isInteger(ruleForm.maxAge)
  ) {
    ruleForm.maxAge = Math.floor(ruleForm.maxAge);
    ElMessage.error("最大年龄请输入整数");
  }
};
const handleChangeMin = () => {
  if (
    !isEmpty(ruleForm.minAge) &&
    !isEmpty(ruleForm.maxAge) &&
    Number(ruleForm.minAge) >= Number(ruleForm.maxAge)
  ) {
    ruleForm.minAge = 1;
    ElMessage.error("最小年龄不能大于等于最大年龄");
  }
  if (Number(ruleForm.minAge) < 0) {
    ruleForm.minAge = 1;
    ElMessage.error("最小年龄不能小于0");
  }
  if (Number(ruleForm.minAge) > 150) {
    ruleForm.minAge = 149;
    ElMessage.error("最小年龄不能大于150");
  }

  if (
    typeof ruleForm.minAge === "number" &&
    !isNaN(ruleForm.minAge) &&
    !Number.isInteger(ruleForm.minAge)
  ) {
    ruleForm.minAge = Math.floor(ruleForm.minAge);
    ElMessage.error("最小年龄请输入整数");
  }
};
// 年龄段只能输数字
const filterInputMin = value => {
  // 只允许输入数字
  let filteredValue = value.replace(/\D/g, "");
  // 限制最多三位数
  if (filteredValue.length > 3) {
    filteredValue = filteredValue.substring(0, 3);
  }
  // 处理前导零的情况
  if (filteredValue.length > 1 && filteredValue.startsWith("0")) {
    filteredValue = filteredValue.substring(1);
  }
  if (filteredValue.length > 2 && filteredValue.startsWith("0")) {
    filteredValue = filteredValue.substring(1);
  }
  ruleForm.minAge = filteredValue;
};
const filterInputMax = value => {
  let filteredValue = value.replace(/\D/g, "");
  if (filteredValue.length > 3) {
    filteredValue = filteredValue.substring(0, 3);
  }
  if (filteredValue.length > 1 && filteredValue.startsWith("0")) {
    filteredValue = filteredValue.substring(1);
  }
  if (filteredValue.length > 2 && filteredValue.startsWith("0")) {
    filteredValue = filteredValue.substring(1);
  }
  ruleForm.maxAge = filteredValue;
};
onMounted(() => {
  if (route.query?.type === "edite") {
    getTableList();
  }
  complexIdApi();
  courseTypeFindApi();
});
</script>

<template>
  <div class="localendAccount-add">
    <!-- <el-descriptions-item
      <span class="title-text" @click="router.go(-1)">局端账号</span>
      <span class="line">\</span>
      <span class="create-title">创建账号</span>
    </div> -->
    <div class="localendAccount-container">
      <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules">
        <el-descriptions title="" :column="1" border>
          <!-- <el-descriptions-item
            v-for="(item, index) in formData"
            :key="index"
            label-align="center"
            :span="item.label === '课程名' ? 2 : ''"
          > -->
          <el-descriptions-item
            v-for="(item, index) in formData"
            :key="index"
            label-align="center"
          >
            <template #label>
              <span v-if="item.check" class="star">*</span>{{ item.label }}
            </template>
            <el-form-item
              :prop="item.prop"
              :inline-message="item.check"
              style="margin-bottom: 0"
              :show-message="true"
              error-placement="right"
            >
              <!-- input输入 -->
              <template v-if="item.type === 'input'">
                <el-input
                  v-model.trim="ruleForm[item.prop]"
                  :placeholder="item.placeholder"
                  :style="{ width: item.width }"
                  :maxlength="item.maxLength"
                  :show-word-limit="item.maxLength"
                />
              </template>
              <!-- 多选 -->
              <template v-if="item.type === 'selectMultiple'">
                <el-select
                  v-model="ruleForm[item.prop]"
                  multiple
                  :placeholder="item.placeholder"
                  :style="{ width: item.width }"
                >
                  <el-option
                    v-for="it in item.options"
                    :key="it.value"
                    :label="it.label"
                    :value="it.value"
                  />
                </el-select>
              </template>
              <!-- 单选 -->
              <template v-if="item.type === 'select'">
                <el-select
                  v-model="ruleForm[item.prop]"
                  :placeholder="item.placeholder"
                  :style="{ width: item.width }"
                >
                  <el-option
                    v-for="it in item.options"
                    :key="it.value"
                    :label="it.label"
                    :value="it.value"
                  />
                </el-select>
              </template>
              <!-- 级联选择 -->
              <template v-if="item.type === 'cascader'">
                <el-cascader
                  v-model="ruleForm[item.prop]"
                  :options="item.options"
                  :show-all-levels="false"
                  :style="{ width: item.width }"
                  @change="courseTypeChange"
                />
              </template>
              <!-- input年龄段输入 -->
              <template v-if="item.type === 'inputToInput'">
                <el-input
                  v-model="ruleForm.minAge"
                  placeholder="输最小年龄"
                  style="width: 110px"
                  :min="0"
                  @change="handleChangeMin"
                  @input="filterInputMin($event)"
                />
                <span class="separator">-</span>
                <el-input
                  v-model="ruleForm.maxAge"
                  placeholder="输最大年龄"
                  style="width: 110px"
                  :min="1"
                  @change="handleChangeMax"
                  @input="filterInputMax($event)"
                />
              </template>
              <!-- 动态添加tag -->
              <template v-if="item.type === 'tag'">
                <el-tag
                  v-for="tag in item.tags"
                  :key="tag"
                  style="margin: 0 10px 4px 0"
                  closable
                  :disable-transitions="false"
                  @close="handleClose(tag)"
                >
                  {{ tag }}
                </el-tag>
                <div style="display: block; margin-top: -6px">
                  <el-input
                    v-if="inputVisible"
                    ref="InputRef"
                    v-model.trim="inputValue"
                    class="w-20"
                    size="small"
                    @keyup.enter="handleInputConfirm"
                    @blur="handleInputConfirm"
                  />
                  <el-button
                    v-else
                    class="button-new-tag"
                    size="small"
                    @click="showInput"
                  >
                    +
                  </el-button>
                </div>
              </template>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>

        <div class="upload">封面图</div>
        <el-upload
          v-model:file-list="fileList"
          action="#"
          :http-request="customUpload"
          list-type="picture-card"
          accept="image/*"
          :before-upload="beforeUpload"
          :on-remove="handleRemove"
          :on-preview="handlePictureCardPreview"
          :class="{ hideUploadBtn: fileList.length >= 9 }"
        >
          <el-icon><Plus /></el-icon>
        </el-upload>
        <el-dialog v-model="dialogVisible">
          <img class="w-full" :src="dialogImageUrl" alt="Preview Image">
        </el-dialog>
        <div class="img-text">{{ imgText }}</div>
      </el-form>
      <div class="buttons">
        <!-- <div class="cancel" @click="router.go(-1)">取消</div> -->
        <!-- <div class="create" @click="submitForm(ruleFormRef)">
          {{ route.query.type === "create" ? "创建" : "保存" }}
        </div> -->
        <el-button @click="router.go(-1)">取消</el-button>
        <el-button
          type="primary"
          class="create"
          :loading="submitLoading"
          @click="submitForm(ruleFormRef)"
        >
          {{ route.query.type === "create" ? "创建" : "保存" }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.localendAccount-add {
  box-sizing: border-box;
  // padding: 22px 30px;
  font-size: 14px;
  //   font-family: PingFangSC-regular;
  color: #101010;
  position: relative;

  .title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .title-text {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 76px;
      height: 28px;
      margin-right: 10px;
      cursor: pointer;
      background-color: #f6e5d4;
      border: 1px solid #ff8c19;
    }

    .line {
      margin-right: 20px;
    }
  }

  .localendAccount-container {
    box-sizing: border-box;
    width: 100%;
    height: calc(100vh - 100px);
    padding: 20px 20px;
    background-color: #fff;

    .upload {
      margin: 15px 0;
    }

    .buttons {
      display: flex;
      // justify-content: space-between;
      justify-content: flex-end;
      width: 100%;
      position: absolute;
      bottom: 30px;
      right: 20px;
    }

    .star {
      margin-right: 3px;
      color: red;
    }
    .range-input {
      display: flex;
      align-items: center;
    }

    .separator {
      margin: 0 10px;
      color: #606266;
    }
  }
}

:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  width: 240px;
  background: #e1f5ff;
}
.img-text {
  font-size: 12px;
  color: #8c939d;
}
:deep(.hideUploadBtn .el-upload--picture-card) {
  display: none;
}
</style>
