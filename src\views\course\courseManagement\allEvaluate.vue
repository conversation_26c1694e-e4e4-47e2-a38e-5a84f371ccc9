<script setup>
import { onMounted, ref } from "vue";
import { formatTime } from "@/utils/index";
import { commentsFindAll, upOnlyMe } from "@/api/course.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import CourseDescrip from "./components/courseDescrip.vue";
const props = defineProps({
  periodName: {
    type: String,
    default: ""
  }
});
const emit = defineEmits(["tabShow", "evaluate"]);
const router = useRouter();
const route = useRoute();
onMounted(() => {
  getTableList();
  // tableData.value = Array.from({ length: 20 }).fill({
  //   completed_at: "0",
  //   pending_review: 1,
  //   createdAt: 172627952334,
  //   name: "2024-09-14习题任务",
  //   termNumber: 1,
  //   courseTypeName: "手工",
  //   state: 1,
  //   organizationName: "2024-09-14习题任务",
  //   freeze: 1
  // });
});
// 搜索表单
const form = ref({
  startTime: "",
  endTime: "",
  userName: "",
  startRating: "",
  endRating: "",
  termNumber: "",
  time: ""
});
// 表格数据
const tableData = ref([]);
const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort,
    courseId: Number(route.query.courseId)
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  // console.log("🍧-----paramsData-----", paramsData);
  const [err, result] = await requestTo(commentsFindAll(paramsData));
  // console.log("🎁-----result-22----", result);
  if (result) {
    tableData.value = result?.content;
    params.value.totalElements = result.totalElements;
  } else {
    ElMessage.error(err);
  }
  getListLoading.value = false;
};
//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTableList();
};
const handleSizeChange = val => {
  params.value.page = 1;
  params.value.size = val;
  getTableList();
};
// 查看详情
const getId = val => {
  console.log("🐳-----getId-----");
};
const value1 = ref([]);
// 清除数据
const clearEvt = val => {
  if (val === "name") {
    form.value.userName = "";
  } else if (val === "time") {
    form.value.startTime = "";
    form.value.endTime = "";
  } else if (val === "termNumber") {
    form.value.termNumber = "";
  } else if (val === "startRating") {
    form.value.startRating = "";
  }
  // params.value.page = 1;
  getTableList();
};
// 选择时间
const timeChange = value => {
  // console.log("🐬-----value-----", value);
  if (value) {
    form.value.startTime = new Date(value[0])?.getTime();
    // form.value.endTime = new Date(value[1])?.getTime();
    const endDate = new Date(value[1]);
    endDate.setHours(23, 59, 59, 999); // 关键修改
    form.value.endTime = endDate.getTime();
  }

  // console.log("🐳-----form.value-----", form.value);
};
//搜索
const searchData = () => {
  params.value.page = 1;
  getTableList();
};
// 重置
const setData = () => {
  form.value = {};
  params.value.page = 1;
  value1.value = [];
  getTableList();
};

// 详情
const detailEvt = val => {
  router.push({
    path: "/course/evaluateDetail",
    query: {
      id: val.id,
      courseId: route.query.courseId
    }
  });
};
const courseName = ref("");
const periodNameEvt = val => {
  courseName.value = val;
};
// 是否公开
const showContentEvt = (row, bool) => {
  let freezeText =
    bool === true ? "确定不公开该条评论吗？" : "确定要公开该条评论吗？";
  let title = bool === true ? "不公开" : "公开";
  ElMessageBox.confirm(`${freezeText}`, `${title}评论`, {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(() => {
      isFreezeApi(row, bool);
    })
    .catch(() => {
      // ElMessage({
      //   type: 'info',
      //   message: 'Delete canceled',
      // })
    });
};
const operateLog = ref({});
const isFreezeApi = async (row, bool) => {
  const params = {
    id: row.id,
    onlyMe: bool
  };
  if (bool === true) {
    if (courseName.value && row.userName) {
      operateLog.value = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `不公开“${courseName.value}”课程的“${row.userName}”的评价`
      };
    } else {
      operateLog.value = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `不公开“${courseName.value.valuee}”课程的评价`
      };
    }
  } else {
    if (courseName.value && row.userName) {
      operateLog.value = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `公开“${courseName.value}”课程的“${row.userName}”的评价`
      };
    } else {
      operateLog.value = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `公开“${courseName.value}”课程的用户评价`
      };
    }
  }

  const { code } = await upOnlyMe(params, operateLog.value);
  if (code === 200) {
    ElMessage({
      type: "success",
      message: bool === true ? "不公开成功" : "公开成功"
    });
    getTableList();
  } else {
    ElMessage({
      type: "error",
      message: bool === true ? "不公开失败" : "公开失败"
    });
  }
};
const handleChangeMax = () => {
  if (form.value.endRating < 0) {
    ElMessage({
      type: "error",
      message: "最大评分不能小于0"
    });
    form.value.endRating = "";
    return;
  }
  if (form.value.endRating > 99999) {
    ElMessage({
      type: "error",
      message: "最大评分不能大于99999"
    });
    form.value.endRating = 99999;
    return;
  }
  if (form.value.startRating && form.value.endRating) {
    if (form.value.startRating > form.value.endRating) {
      ElMessage({
        type: "error",
        message: "最小评分不能大于最大评分"
      });
      form.value.startRating = "";
      form.value.endRating = "";
      return;
    } else {
      getTableList();
    }
  }
};
const handleChangeMin = () => {
  if (form.value.startRating < 0) {
    ElMessage({
      type: "error",
      message: "最小评分不能小于0"
    });
    form.value.startRating = "";
    return;
  }
  if (form.value.startRating > 99999) {
    ElMessage({
      type: "error",
      message: "最小评分不能大于99999"
    });
    form.value.startRating = 99999;
    return;
  }
};
</script>

<template>
  <div class="all-evaluate">
    <CourseDescrip
      :courseId="Number(route.query.courseId)"
      @name="periodNameEvt"
    />
    <div class="containers">
      <div class="con_search">
        <el-form :model="form" :inline="true">
          <el-form-item label="评价时间">
            <el-date-picker
              v-model="value1"
              type="daterange"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              @change="timeChange"
              @clear="clearEvt('time')"
            />
          </el-form-item>
          <el-form-item label="期号">
            <el-input
              v-model.trim="form.termNumber"
              placeholder="请输入"
              style="width: 240px"
              clearable
              @clear="clearEvt('termNumber')"
            />
          </el-form-item>
          <el-form-item label="用户名">
            <el-input
              v-model.trim="form.userName"
              placeholder="请输入"
              style="width: 240px"
              clearable
              @clear="clearEvt('name')"
            />
          </el-form-item>
          <el-form-item label="评分">
            <div class="range-input">
              <el-input
                v-model.number="form.startRating"
                placeholder="输最小值"
                type="number"
                style="width: 100px"
                :min="0"
                :max="99999"
                @change="handleChangeMin"
              />
              <span class="separator">-</span>
              <el-input
                v-model.number="form.endRating"
                placeholder="输最大值"
                type="number"
                style="width: 100px"
                :min="0"
                :max="99999"
                @change="handleChangeMax"
              />
            </div>
          </el-form-item>

          <el-form-item label=" ">
            <div class="flex">
              <el-button type="primary" @click="searchData">搜索</el-button>
              <el-button @click="setData">重置</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div class="con_table">
        <el-table
          :data="tableData"
          :header-cell-style="{ backgroundColor: '#fafafa', color: '#565353' }"
          highlight-current-row
          height="calc(100vh - 410px)"
        >
          <el-table-column prop="termNumber" label="期数" min-width="120" fixed>
            <template #default="scope">
              {{ scope.row.termNumber || 0 }}
            </template>
          </el-table-column>
          <el-table-column prop="userName" label="用户名" align="left" fixed>
            <template #default="scope">
              <div>
                {{ scope.row.userName || "--" }}
              </div>
            </template>
          </el-table-column>

          <el-table-column width="200px" prop="createdAt" label="评价时间">
            <template #default="scope">
              <div>
                {{
                  formatTime(scope.row?.createdAt, "YYYY-MM-DD HH:mm:ss") ||
                  "--"
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="rating" label="分数" align="left">
            <template #default="scope">
              <div>
                {{ scope.row.rating || 0 }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="content"
            label="评论内容"
            align="left"
            width="300"
          >
            <template #default="scope">
              <span class="no-wrap-text">{{ scope.row?.content || "--" }}</span>
            </template>
          </el-table-column>
          <el-table-column
            width="200px"
            prop="replies.createdAt
"
            label="回复时间"
          >
            <template #default="scope">
              <div>
                {{
                  formatTime(
                    scope.row?.replies?.createdAt,
                    "YYYY-MM-DD HH:mm:ss"
                  ) || "--"
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="replies.content"
            label="机构回复"
            align="left"
            width="300"
          >
            <template #default="scope">
              <span class="no-wrap-text">{{
                scope.row.replies?.content || "--"
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            align="left"
            width="200px"
          >
            <template #default="{ row }">
              <div class="option">
                <div class="btnse" @click="detailEvt(row)">详情</div>
                <div class="btnse">
                  <div
                    v-if="row.onlyMe === false"
                    class="freeze"
                    @click="showContentEvt(row, true)"
                  >
                    不公开
                  </div>
                  <div
                    v-else
                    class="nofreeze"
                    @click="showContentEvt(row, false)"
                  >
                    公开
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="con_pagination">
        <!-- 分页 -->
        <el-pagination
          v-model:current-page="params.page"
          v-model:page-size="params.size"
          class="pagination"
          background
          :page-sizes="[15, 30, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="params.totalElements"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.containers {
  //   box-sizing: border-box;
  //   width: calc(100% - 48px);
  //   height: 100%;
  padding: 20px;
  background: #fff;

  .con_search {
    display: flex;
    align-items: center;
    width: 100%;
    height: fit-content;
    margin-bottom: 12px;
  }

  .con_table {
    // width: calc(100% - 25px);
    margin-bottom: 24px;
    // margin-left: 25px;

    .option {
      display: flex;

      .btnse {
        display: flex;
        margin-right: 16px;
        color: #409eff;
        cursor: pointer;

        .nofreeze {
          color: #f56c6c;
          cursor: pointer;
        }
      }
    }
  }

  .con_pagination {
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }
}
.range-input {
  display: flex;
  align-items: center;
}

.separator {
  margin: 0 10px;
  color: #606266;
}
.no-wrap-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  width: 100%;
}
</style>
