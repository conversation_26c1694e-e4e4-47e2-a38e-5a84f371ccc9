import { http } from "@/utils/http";

/*  订单管理  */
// 分页查询
export const ordersFindAll = params => {
  return http.request(
    "get",
    "/platform/orders/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 查询子订单详情
export const getOrderDetails = params => {
  return http.request(
    "get",
    "/platform/orders/getOrderDetails",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
//主订单退款
export const refund = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/orders/refund",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ORDER_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "退单了主订单"
      }
    }
  );
};
//子订单退款
export const confirmRefund = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/orders/refundSub",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ORDER_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "退单了子订单"
      }
    }
  );
};
// 根据订单id查询退款记录
export const findByOrdersId = params => {
  return http.request(
    "get",
    "/platform/refundRecord/findByOrdersId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 根据子订单id查询退款记录
export const findBySubOrdersId = params => {
  return http.request(
    "get",
    "/platform/refundRecord/findBySubOrdersId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
