import { SM4 } from "gm-crypto";
import {
  courseAdd,
  complexId,
  courseTypeFind,
  courseEdite,
  courseFindId
} from "@/api/course.js";

const key = "377a416b574e66504a70345242586459";

// sm4 加密
function encryption(text) {
  const encryptedData = SM4.encrypt(text, key, {
    inputEncoding: "utf8",
    outputEncoding: "base64"
  });
  return encryptedData;
}

/*

 */

// 创建课程
export function createCourse() {
  // 基地id
  let complexId = 7;
  let tmpEnData = [
    "中山民俗-中山沙溪四月八-剪纸(NO.2)",
    "中山民俗-龙头环十八掌-雕刻"
  ];

  for (let i = 0; i < tmpEnData.length; i++) {
    console.log("创建：" + tmpEnData[i]);
    // let tt = encryption(tmpEnData[i].phone)
    // console.log(tt);
    // tmpEnData[i].phone = tt;
    const operateLog = {
      operateLogType: "COURSE_MANAGEMENT",
      operateType: "创建了课程",
      operatorTarget: tmpEnData[i]
    };
    let upData = {
      name: tmpEnData[i],
      courseTypeId: 4,
      complexId: complexId,
      files: [
        {
          sortOrder: 1,
          fileIdentifier: "d029f684f6cef049352ae69f8cb52110",
          fileType: "PHOTO"
        }
      ]
    };
    // courseAdd(upData, operateLog);
  }
  console.log(tmpEnData);
}
