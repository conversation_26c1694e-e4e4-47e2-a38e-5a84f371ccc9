<script setup>
import { ref, onMounted, reactive, onBeforeMount, onBeforeUnmount } from "vue";
import { useRouter, useRoute } from "vue-router";
import RichEditor from "@/components/Base/RichEditor.vue";
import { complexUpdate, complexFindById } from "@/api/institution";
import { ElMessage } from "element-plus";
import { formatTime } from "@/utils/index";
import { decrypt, encryption } from "@/utils/SM4.js";
import { Hide, View } from "@element-plus/icons-vue";
import { compareObjects, debounce } from "@iceywu/utils";
import Map from "./components/Map.vue";
import ImgPos from "@/assets/pos.png";

const router = useRouter();
const route = useRoute();
const form = ref({
  id: "",
  name: "",
  detailedAddress: "",
  emergencyPeople: "",
  emergencyPhone: "",
  introduction: "",
  createdAt: "",
  updatedAt: "",
  state: "",
  latitude: "",
  longitude: ""
});

const formRef = ref(null);
const richFlag = ref(false);
const adminId = ref(null);
onMounted(() => {
  adminId.value = route.query.id;
  getData();
});

// 地图弹窗
const dialogTableVisible = ref(false);
// 地图位置
const locationPostion = ref([]);
// 存储用户选择的位置信息
const selectedLocation = ref(null);
// 地图范围
const teacherTimeInfo = {
  location_range: 1000
};
// 地图确认
const handleMapConfirm = data => {
  console.log("🎉-----handleMapConfirm-----", data);
  form.value.detailedAddress = data.address;
  form.value.latitude = data.point.lat;
  form.value.longitude = data.point.lng;
  locationPostion.value = [data.point.lng, data.point.lat];

  // 保存选择的位置信息，下次打开时会自动定位到这里
  selectedLocation.value = data;
};

const formData = ref([
  {
    label: "编号",
    type: "text",
    prop: "id",
    check: true,
    placeholder: "请输入机构名称",
    width: "200px"
  },
  {
    label: "创建时间",
    type: "text",
    // check: true,
    prop: "createdAt",
    placeholder: "请输入机构别名",
    width: "200px"
  },
  {
    label: "基地名称",
    type: "text",
    prop: "name",
    span: 2,
    check: true,
    placeholder: "请输入机构名称",
    width: "200px"
  },
  {
    label: "详细地址",
    type: "map",
    span: 2,
    prop: "detailedAddress",
    check: true,
    placeholder: "请输入详细地址",
    width: "200px"
  },
  {
    label: "基地联系人",
    type: "input",
    // check: true,
    prop: "emergencyPeople",
    placeholder: "请输入基地联系人",
    width: "200px"
  },
  {
    label: "基地联系电话",
    type: "input",
    // check: true,
    prop: "emergencyPhone",
    placeholder: "请输入基地联系电话",
    width: "200px"
  },
  {
    label: "基地介绍",
    type: "editor",
    check: true,
    prop: "introduction",
    placeholder: "请输入基地介绍",
    width: "200px"
  }
  //   {
  //     label: "状态",
  //     type: "text",
  //     // check: true,
  //     prop: "state",
  //     placeholder: "请输入机构别名",
  //     width: "200px"
  //   }
]);

const newData = ref();

// 根据id查询
const getData = async () => {
  let params = { id: route.query.id };
  try {
    const { code, data, msg } = await complexFindById(params);
    // console.log("🎁-----data-----", code, data, msg);
    if (code == 200 && data) {
      form.value = data;
      locationPostion.value = [data.longitude, data.latitude];
      newData.value = JSON.parse(JSON.stringify(data));
      richFlag.value = true;

      // 如果有经纬度和地址信息，设置为初始选择位置
      if (data.longitude && data.latitude && data.detailedAddress) {
        selectedLocation.value = {
          point: {
            lng: data.longitude,
            lat: data.latitude
          },
          address: data.detailedAddress
        };
      }
    }
  } catch (error) {
    console.log("catch-----error-----", error);
  }
};
// 自定义校验方法
const validateIntroduction = (rule, value, callback) => {
  const errorMsg = "基地介绍不能为空";

  // 检查是否为空
  if (!value) {
    return callback(new Error(errorMsg));
  }

  // 移除HTML标签和HTML空格字符，并去除前后空格
  let cleanValue = value
    .replace(/<[^>]*>/g, "")
    .replace(/&nbsp;|&ensp;|&emsp;|&thinsp;/g, "")
    .trim();

  return cleanValue === "" ? callback(new Error(errorMsg)) : callback();
};
// 校验规则
const rules = ref({
  detailedAddress: [
    { required: true, message: "详细地址不能为空", trigger: "blur" }
  ],
  introduction: [
    { required: true, validator: validateIntroduction, trigger: "blur" }
  ]
});
const submitForm = debounce(
  () => {
    formRef.value.validate(valid => {
      if (valid) {
        console.log("表单数据:", form.value);
        addbase();
      } else {
        console.log("表单校验失败");
      }
    });
  },
  1000,
  { immediate: true }
);
const getListLoading = ref(false);
const addbase = async () => {
  if (getListLoading.value) return;
  getListLoading.value = true;
  let paramsData = {};
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  paramsData = compareObjects(newData.value, paramsData);
  // if (
  //   paramsData.emergencyPhone &&
  //   paramsData.emergencyPhone == decrypt(newData.value.emergencyPhone || "")
  // ) {
  //   delete paramsData.emergencyPhone;
  // }
  if (paramsData.emergencyPhone) {
    paramsData.emergencyPhone = encryption(paramsData.emergencyPhone || "");
  }
  console.log("🎁-----paramsData-----", paramsData);
  if (Object.keys(paramsData).length == 0) {
    ElMessage({
      type: "warning",
      message: "未修改任何信息"
    });
    getListLoading.value = false;
    return;
  }
  paramsData.id = adminId.value;
  // return;
  const operateLog = {
    operateLogType: "COMPLEX_MANAGEMENT",
    operateType: "编辑了",
    operatorTarget: `"${form.value.name}"的基地信息`
  };
  console.log("🍪-----paramsData-----", paramsData);
  // return;
  const { code } = await complexUpdate(paramsData, operateLog);
  if (code === 200) {
    ElMessage({
      type: "success",
      message: "编辑成功"
    });
    cancel();
  } else {
    ElMessage({
      type: "error",
      message: "编辑失败"
    });
  }
  getListLoading.value = false;
};
//返回上一页
const cancel = () => {
  router.go(-1);
};
const edit = () => {
  router.go(-1);
};
const isView = ref(true);
const isViewFn = () => {
  isView.value = !isView.value;
  if (isView.value) {
    form.value.emergencyPhone = newData.value.emergencyPhone;
  } else {
    form.value.emergencyPhone = decrypt(newData.value.emergencyPhoneCt);
  }
};
</script>

<template>
  <div class="main">
    <el-scrollbar class="scrollbar">
      <div>
        <el-form ref="formRef" :model="form" :rules="rules">
          <el-descriptions
            v-if="richFlag"
            itle=""
            :column="2"
            border
            :label-width="'200px'"
          >
            <el-descriptions-item
              v-for="(item, index) in formData"
              :key="index"
              label-align="center"
              label-class-name="my-label"
              :span="item.span || 1"
            >
              <template #label>
                {{ item.label }}
              </template>
              <el-form-item
                :prop="item.prop"
                :inline-message="item.check"
                style="margin-bottom: 0"
                :show-message="true"
                error-placement="right"
              >
                <!-- text -->
                <template v-if="item.type === 'text'">
                  <div style="color: #a8a8a8">
                    {{
                      item.prop === "createdAt"
                        ? formatTime(form[item.prop], "YYYY-MM-DD HH:mm:ss")
                        : form[item.prop] || "--"
                    }}
                  </div>
                </template>
                <!-- input输入 -->
                <template v-if="item.type === 'input'">
                  <el-input
                    v-model.trim="form[item.prop]"
                    :placeholder="item.placeholder"
                    :style="{ width: item.width }"
                    :disabled="
                      item.prop === 'emergencyPhone' && newData[item.prop]
                        ? isView
                        : false
                    "
                  >
                    <template
                      v-if="item.prop === 'emergencyPhone' && form[item.prop]"
                      #suffix
                    >
                      <el-icon
                        v-if="isView"
                        style="cursor: pointer"
                        @click="isViewFn"
                      >
                        <Hide />
                      </el-icon>
                      <el-icon v-else style="cursor: pointer" @click="isViewFn">
                        <View />
                      </el-icon>
                    </template>
                  </el-input>
                </template>
                <!-- 富文本 -->
                <template v-else-if="item.type === 'editor'">
                  <div style="width: 100%">
                    <RichEditor v-model="form[item.prop]" height="150px" />
                  </div>
                </template>
                <!-- 地图 -->
                <template v-else-if="item.type === 'map'">
                  <div class="selsect-pos">
                    <div class="cover" @click="dialogTableVisible = true" />
                    <el-input
                      v-model="form[item.prop]"
                      class="input-part"
                      placeholder="请选择地址"
                      :style="{ width: item.width }"
                    >
                      <template #suffix>
                        <div class="pos-icon">
                          <img :src="ImgPos" class="wfull h-full" alt="">
                        </div>
                      </template>
                    </el-input>
                  </div>
                </template>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
        </el-form>
      </div>
    </el-scrollbar>

    <div class="account_management">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" :loading="getListLoading" @click="submitForm">
        保存
      </el-button>
    </div>

    <Map
      v-if="dialogTableVisible"
      v-model="dialogTableVisible"
      :center="locationPostion"
      :selected-location="selectedLocation"
      :checkInResult="teacherTimeInfo"
      @confirm="handleMapConfirm"
    />
  </div>
</template>

<style lang="scss" scoped>
.scrollbar {
  // padding-top: 20px;
  // padding: 20px;
  height: calc(100vh - 201px);
  background-color: #fff;
}
.main {
  padding: 20px;
  background: #fff;
}

.header {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 20px;

  .title_left {
    display: flex;

    :nth-child(2) {
      margin-left: 40px;
    }
  }

  .title_rigth {
    display: flex;

    :nth-child(2) {
      margin-left: 40px;
    }
  }
}

.account_management {
  margin: 20px 0 0 0;
  display: flex;
  justify-content: flex-end;
  // :nth-child(2) {
  //   margin-left: 20px;
  // }
}

:deep(.my-label) {
  background: #e1f5ff !important;
}
.star {
  margin-right: 3px;
  color: red;
}

.selsect-pos {
  position: relative;
  // width: 490px;
  // height: 60px;
  // background: red;
  .cover {
    cursor: pointer;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    // background: red;
    width: 100%;
    height: 100%;
  }

  .pos-icon {
    width: 24px;
    height: 24px;
  }
}
</style>
