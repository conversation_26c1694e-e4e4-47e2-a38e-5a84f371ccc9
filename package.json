{"name": "research-learning-admin", "type": "module", "version": "1.8.0", "private": true, "author": {"name": "xiaoxian521", "email": "<EMAIL>", "url": "https://github.com/xiaoxian521"}, "license": "MIT", "homepage": "https://github.com/pure-admin/vue-pure-admin", "repository": {"type": "git", "url": "git+https://github.com/pure-admin/vue-pure-admin.git"}, "bugs": {"url": "https://github.com/pure-admin/vue-pure-admin/issues"}, "keywords": ["vue-pure-admin", "element-plus", "pure-admin", "typescript", "pinia", "vue3", "vite", "esm"], "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "scripts": {"dev": "cross-env --max_old_space_size=4096 vite", "build": "cross-env NODE_OPTIONS=--max_old_space_size=4096 vite build && generate-version-file", "build:brdev": "cross-env NODE_OPTIONS=--max_old_space_size=4096 vite build --mode brdev && generate-version-file", "build:staging": "rimraf dist && cross-env vite build --mode staging", "report": "rimraf dist && cross-env vite build", "preview": "vite preview", "preview:build": "npx build && vite preview", "typecheck": "tsc --noEmit && vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "svgo": "svgo -f . -r", "pnpm:clean:cache": "rimraf .eslintcache && rimraf pnpm-lock.yaml && rimraf node_modules && pnpm store prune && pnpm install", "lint:eslint": "eslint .", "lint:eslint:fix": "eslint . --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,ts,json,tsx,css,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{html,vue,css,scss}\" --cache-location node_modules/.cache/stylelint/", "lint": "npm run lint:eslint && npm run lint:prettier && npm run lint:stylelint", "prepare": "husky", "version": "standard-version --tag-prefix \"v\"", "versionMajor": "standard-version --tag-prefix \"v\" --release-as major"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@iceywu/utils": "^0.0.49", "@iconify-json/carbon": "^1.2.4", "@iconify-json/vscode-icons": "^1.2.2", "@pureadmin/table": "^3.2.1", "@pureadmin/utils": "^2.5.0", "@unocss/reset": "^66.0.0", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vue-office/pdf": "^2.0.10", "@vue-office/pptx": "^1.0.1", "@vueuse/core": "^11.3.0", "@vueuse/motion": "^2.2.6", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "@zxcvbn-ts/core": "^3.0.4", "animate.css": "^4.1.1", "axios": "^1.7.8", "blurhash": "^2.0.5", "boxen": "^8.0.1", "code-inspector-plugin": "^0.18.2", "core-js": "^3.39.0", "cropperjs": "^1.6.2", "cross-env": "^7.0.3", "crypto-js": "^4.2.0", "cssnano": "^7.0.6", "dayjs": "^1.11.13", "echarts": "^5.5.1", "element-plus": "^2.8.8", "eslint-plugin-format": "^0.1.2", "file-saver": "^2.0.5", "gm-crypto": "^0.1.12", "gradient-string": "^3.0.0", "immutable": "^5.0.3", "js-cookie": "^3.0.5", "js-md5": "^0.8.3", "localforage": "^1.10.0", "lodash": "^4.17.21", "lottie-web": "^5.12.2", "mint-filter": "^4.0.3", "mitt": "^3.0.1", "nprogress": "^0.2.0", "path": "^0.12.7", "pinia": "^2.2.6", "pinia-plugin-persistedstate": "^4.1.3", "pinyin-pro": "^3.26.0", "plus-pro-components": "^0.1.18", "qrcode": "^1.5.4", "qs": "^6.13.1", "responsive-storage": "^2.2.0", "rimraf": "^6.0.1", "sortablejs": "^1.15.4", "spark-md5": "^3.0.2", "swiper": "^11.1.15", "terser": "^5.36.0", "tippy.js": "^6.3.7", "typeit": "^8.8.7", "uuid": "^11.0.3", "version-rocket": "^1.7.4", "vue": "^3.5.13", "vue-demi": "^0.14.6", "vue-hooks-pure": "^0.0.30", "vue-i18n": "^10.0.4", "vue-router": "^4.5.0", "vue-tippy": "^6.5.0", "vue-types": "^5.1.3", "xgplayer": "^3.0.20"}, "devDependencies": {"@antfu/eslint-config": "^3.10.0", "@commitlint/cli": "^19.6.0", "@commitlint/config-conventional": "^19.6.0", "@commitlint/types": "^19.5.0", "@faker-js/faker": "^9.2.0", "@iconify-icons/ep": "^1.2.12", "@iconify-icons/ri": "^1.2.10", "@iconify/vue": "^4.1.2", "@intlify/unplugin-vue-i18n": "^6.0.0", "@plus-pro-components/resolver": "^0.0.3", "@pureadmin/theme": "^3.3.0", "@types/js-cookie": "^3.0.6", "@types/js-md5": "^0.7.2", "@types/node": "^22.10.0", "@types/nprogress": "^0.2.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.17", "@types/sortablejs": "^1.15.8", "@unocss/eslint-plugin": "^66.0.0", "@vitejs/plugin-legacy": "^6.0.0", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "autoprefixer": "^10.4.20", "eslint": "^9.15.0", "husky": "^9.1.7", "lint-staged": "^15.2.10", "postcss": "^8.4.49", "postcss-html": "^1.7.0", "postcss-import": "^16.1.0", "postcss-scss": "^4.0.9", "prettier": "^3.4.1", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.79.6", "stylelint": "^16.10.0", "stylelint-config-recess-order": "^5.1.1", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard-scss": "^13.1.0", "stylelint-prettier": "^5.0.2", "svgo": "^3.3.2", "typescript": "^5.7.2", "unocss": "^66.0.0", "unplugin-vue-components": "^28.8.0", "vite": "^6.0.1", "vite-plugin-cdn-import": "^1.0.1", "vite-plugin-checker": "^0.8.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-fake-server": "^2.1.3", "vite-plugin-remove-console": "^2.2.0", "vite-plugin-router-warn": "^1.0.0", "vite-plugin-vue-inspector": "^5.3.1", "vite-svg-loader": "^5.1.0", "vue-tsc": "^2.1.10"}, "lint-staged": {"*": "eslint --fix"}}