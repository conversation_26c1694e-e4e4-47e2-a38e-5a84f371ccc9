import { http } from "@/utils/http";
// 机构/重置密码
// export const ledeterAll = params => {
//   return http.request(
//     "",
//     "/organization/leaderLecturer/findAll",
//     { params },
//     { isNeedEncrypt: true }
//   );
// };
// 机构/重置密码
// export const resetPassword = data => {
//   return http.request(
//     "post",
//     "/organization/resetPassword",
//     { data },
//     { isNeedEncrypt: true }
//   );
// };
//删除基地
export const complexDelete = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/complex/delete",
    { data },
    {
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COMPLEX_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "基地",
        operateType: operateLog?.operateType || "删除了"
      }
    }
  );
};
//新增基地
export const complexSave = (data, operateLog = {}) => {
  // export const complexSave = (data) => {
  return http.request(
    "post",
    "/organization/complex/save",
    { data },
    {
      isNeedEncrypt: true,
      // operateLog: {
      //   operateLogType: "ORGANIZATIONAL_MANAGEMENT",
      //   operatorTarget: "基地",
      //   operateType: "新建了",
      // }
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COMPLEX_MANAGEMENT",
        // detail: operateLog?.detail || "",
        additionalParameter: operateLog?.additionalParameter || "",
        // operator: operateLog?.operateLogType || "",
        operatorTarget: operateLog?.operatorTarget || "基地",
        operateType: operateLog?.operateType || "新建了"
      }
    }
  );
};
//编辑
export const complexUpdate = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/complex/update",
    { data },
    {
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COMPLEX_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "基地",
        operateType: operateLog?.operateType || "编辑了"
      }
    }
  );
};
//根据id查询查询机构
export const organizationFindById = params => {
  return http.request(
    "get",
    "/organization/findById",
    { params },
    { isNeedEncrypt: true }
  );
};

//不分页查询基地
export const findAllNotPage = params => {
  return http.request(
    "get",
    "/organization/complex/findAllNotPage",
    { params },
    { isNeedEncrypt: true }
  );
};
//重置密码
export const resetPassword = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/admin/resetPassword",
    { data },
    {
      isNeedEncrypt: true,
      operateLog: {
        operateLogType:
          operateLog?.operateLogType || "ORGANIZATIONAL_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "重置了管理员密码"
      }
    }
  );
};
// 机构管理-编辑-验证手机号
export const verifyPhone = data => {
  return http.request(
    "post",
    "/organization/verifyPhone",
    { data },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
// 机构管理-编辑
export const organizationUpdate = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/update",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType:
          operateLog?.operateLogType || "ORGANIZATIONAL_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "编辑了机构信息"
      }
    }
  );
};
// 机构管理-修改密码
export const updatePassword = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/admin/updatePassword",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "修改了密码"
      }
    }
  );
};
//根据id查询查询基地
export const complexFindById = params => {
  return http.request(
    "get",
    "/organization/complex/findById",
    { params },
    { isNeedEncrypt: true }
  );
};
//新增日志
export const operateLogSave = data => {
  return http.request(
    "post",
    "/organization/operateLog/save",
    { data },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
//查询日志
export const operateLogFindAll = params => {
  return http.request(
    "get",
    "/organization/operateLog/findAll",
    { params },
    { isNeedEncrypt: true }
  );
};
//首页
export const homeStatistics = params => {
  return http.request(
    "get",
    "/organization/home/<USER>",
    { params },
    { isNeedEncrypt: true }
  );
};
/** 获取机构财务 */
export const findOrganizationFinancial = params => {
  return http.request(
    "get",
    "/organization/financialRecord/findOrganizationFinancial",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 账务分页查询 */
export const financialRecordFindAll = params => {
  return http.request(
    "get",
    "/organization/financialRecord/findAll",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 账务导出 */
export const financialRecordExport = (params, operateLog = {}) => {
  return http.request(
    "get",
    "/organization/financialRecord/export",
    { params },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "导出了账务账单"
      }
    }
  );
};
/**编辑银行账户信息 */
export const editBankAccount = data => {
  return http.request(
    "post",
    "/organization/bank/save",
    { data },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 根据机构id查询银行账户 */
export const getBankAccount = (params = {}) => {
  return http.request(
    "get",
    "/organization/bank/findByOrganizationId",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 关联订单 */
export const getOrderDetails = params => {
  return http.request(
    "get",
    "/organization/orders/getOrderDetails",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
//根据id查询账号详情
export const adminFindById = params => {
  return http.request(
    "get",
    "/organization/admin/findById",
    { params },
    { isNeedEncrypt: true }
  );
};
// 领队讲师/编辑信息
export const editteaInfo = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/admin/update",
    { data },
    {
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "编辑了"
      }
    }
  );
};
// 新增讲师资质文件
export const leaderLecturerSaveLecturerFile = data => {
  return http.request(
    "post",
    "/organization/leaderLecturer/saveLecturerFile",
    { data },
    {
      isNeedEncrypt: true
      //  operateLog: {
      //   operateLogType: operateLog?.operateLogType || "LECTURER_MANAGEMENT",
      //   additionalParameter: operateLog?.additionalParameter || "",
      //   operatorTarget: operateLog?.operatorTarget || "",
      //   operateType: operateLog?.operateType || "修改了讲师资质文件"
      // }
    }
  );
};
// 新增领队资质文件
export const leaderLecturerSaveLeaderFile = data => {
  return http.request(
    "post",
    "/organization/leaderLecturer/saveLeaderFile",
    { data },
    {
      isNeedEncrypt: true
      // operateLog: {
      //   operateLogType: operateLog?.operateLogType || "LEADER_MANAGEMENT",
      //   additionalParameter: operateLog?.additionalParameter || "",
      //   operatorTarget: operateLog?.operatorTarget || "",
      //   operateType: operateLog?.operateType || "修改了领队资质文件"
      // }
    }
  );
};
// 更新手机号
export const updatePhone = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/admin/updatePhone",
    { data },
    {
      isNeedEncrypt: true
      // operateLog: {
      //   operateLogType:
      //     operateLog?.operateLogType || "ORGANIZATIONAL_MANAGEMENT",
      //   additionalParameter: operateLog?.additionalParameter || "",
      //   operatorTarget: operateLog?.operatorTarget || "",
      //   operateType: operateLog?.operateType || "更新了"
      // }
    }
  );
};
// 更新管理员手机号
export const updateAdminPhone = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/admin/updateAdminPhone",
    { data },
    {
      isNeedEncrypt: true
      // operateLog: {
      //   operateLogType:
      //     operateLog?.operateLogType || "ORGANIZATIONAL_MANAGEMENT",
      //   additionalParameter: operateLog?.additionalParameter || "",
      //   operatorTarget: operateLog?.operatorTarget || "",
      //   operateType: operateLog?.operateType || "更新了"
      // }
    }
  );
};
//异步导入
export const asyncTask = params => {
  return http.request(
    "get",
    "/common/asyncTask/findById",
    { params },
    { isNeedEncrypt: true }
  );
};
// 机构管理-编辑-验证账号
export const verifyUsername = data => {
  return http.request(
    "post",
    "/organization/admin/verifyUsername",
    { data },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
// 获取手机号验证码
export const getPhonecode = data => {
  return http.request(
    "post",
    "/common/verificationCode/generateCode",
    { data },
    { isNeedEncrypt: true }
  );
};
// 根据id查询机构详情
export const institutionFindById = params => {
  return http.request(
    "get",
    "/common/dict/findByParentId",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
