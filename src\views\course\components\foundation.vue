<script setup>
import { onMounted, ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { findClassTracking } from "@/api/period.js";
import { to } from "@iceywu/utils";
import { requestTo } from "@/utils/http/tool";
import { ImageThumbnail } from "@/utils/imageProxy.js";
import dayjs from "dayjs";
import { courseFindId, complexId } from "@/api/course.js";
import {
  draftCoursePeriodFindByDraftId,
  findByCourseDraftId
} from "@/api/drafts.js";

const props = defineProps({
  draftId: {
    type: String,
    default: ""
  },
  title: {
    type: String,
    default: "课程基础信息"
  }
});
const router = useRouter();
const route = useRoute();
// 课程基础信息 表头
const tableHeaderVa = ref([
  {
    id: "1",
    label: "课程名",
    value: "",
    width: "107px"
  },
  {
    id: "2",
    label: "课程类型",
    value: "",
    width: "107px"
  },
  {
    id: "3",
    label: "课程年龄段",
    value: "",
    width: "107px"
  },
  {
    id: "4",
    label: "课程亮点标签",
    value: "",
    width: "107px"
  }
]);

// 课期基础信息 表头
const tableHeaderVb = ref([
  {
    id: "1",
    label: "课期名",
    value: "",
    width: "107px"
  },
  {
    id: "2",
    label: "开课时间",
    value: "",
    width: "107px"
  },
  {
    id: "3",
    label: "基地",
    value: "",
    width: "107px"
  },
  {
    id: "4",
    label: "人数上限",
    value: "",
    width: "107px"
  },
  {
    id: "5",
    label: "领队",
    value: "",
    width: "107px"
  },
  {
    id: "6",
    label: "讲师",
    value: "",
    width: "107px"
  }
]);

const url = ref();
let srcList = ref([]);
const trackList = ref([]);
const tableHeader = ref([]);

// 课程基础信息 V1
const getTableList = async data => {
  let paramsData = { id: route.query.courseId };
  const [err, result] = await requestTo(courseFindId(paramsData));
  if (result) {
    tableHeaderVa.value[0].value = result?.name || "--";
    tableHeaderVa.value[1].value = result?.courseType?.name || "--";
    if (result.minAge && result.maxAge) {
      tableHeaderVa.value[2].value = `${result.minAge}到${result.maxAge}岁`;
    } else if (result.minAge && !result.maxAge) {
      tableHeaderVa.value[2].value = `${result.minAge}岁`;
    } else if (!result.minAge && result.maxAge) {
      tableHeaderVa.value[2].value = `${result.maxAge}岁`;
    } else {
      tableHeaderVa.value[2].value = `无年龄限制`;
    }
    // const minAge = result?.minAge || 0;
    // const maxAge = result?.maxAge || 0;
    // tableHeaderVa.value[2].value = minAge + " 到 " + maxAge + " 岁";
    let tag = result?.tags?.join(" 、") || null;
    tableHeaderVa.value[3].value = tag || "--";

    if (result?.files?.length) {
      result?.files?.map(item => {
        srcList.value.push(item?.uploadFile?.url);
      });
      url.value = result.files[0]?.uploadFile?.url;
    }
    tableHeader.value = tableHeaderVa.value;
  } else {
    ElMessage.error(err);
  }
};

// 课程基础信息
const courseChengAdd = async () => {
  let [err, res] = await to(findByCourseDraftId({ draftId: props.draftId }));
  if (res) {
    // console.log("🌈-----res--课程基础信息---", res);
    const { data } = res;
    // console.log("🐬-----res1111--课程基础信息---", data);
    tableHeaderVa.value[0].value = data?.name || "--";
    tableHeaderVa.value[1].value = data?.courseType?.name || "--";
    if (data.minAge && data.maxAge) {
      tableHeaderVa.value[2].value = `${data.minAge}到${data.maxAge}岁`;
    } else if (data.minAge && !data.maxAge) {
      tableHeaderVa.value[2].value = `${data.minAge}岁`;
    } else if (!data.minAge && data.maxAge) {
      tableHeaderVa.value[2].value = `${data.maxAge}岁`;
    } else {
      tableHeaderVa.value[2].value = `无年龄限制`;
    }

    // const minAge = data?.minAge || 0;
    // const maxAge = data?.maxAge || 0;

    // tableHeaderVa.value[2].value = minAge + " 到 " + maxAge + " 岁";
    let tag = data?.tags?.join(" 、") || null;
    tableHeaderVa.value[3].value = tag || "--";
    if (data?.files?.length) {
      data?.files?.map(item => {
        srcList.value.push(item?.uploadFile?.url);
      });
      url.value = data.files[0]?.uploadFile?.url;
    }
    tableHeader.value = tableHeaderVa.value;
  } else {
    // console.log("🐳-----err-----", err);
  }
};

//  基地查询不分页
const complexIdApi = async data => {
  let ee = "";
  let [err, res] = await requestTo(complexId());
  if (res) {
    res.map(it => {
      if (it.id === data?.complex) {
        ee = it?.name || "--";
      }
    });
  }
  tableHeaderVb.value[2].value = ee || "--";
};

const unber = ref(1);
// 课期基础信息
const PeriodCourseAdd = async () => {
  let [err, res] = await to(
    draftCoursePeriodFindByDraftId({ draftId: props.draftId })
  );
  if (res) {
    const { data } = res;
    // console.log("🌳-----res--课程基础信息---", res);
    const courseNameData = generateCourseList(data);

    tableHeaderVb.value[0].value = courseNameData || "--";
    tableHeaderVb.value[1].value = data?.draftCoursePeriodTimes || "--";
    unber.value = data?.draftCoursePeriodTimes.length || 1;

    if (data?.complex?.name) {
      tableHeaderVb.value[2].value = data?.complex?.name || "--";
    } else {
      complexIdApi(data);
    }

    tableHeaderVb.value[3].value = data?.maxPeopleNumber || "无人数上限";
    if (data?.leaders?.length) {
      let learderList = [];
      data?.leaders.map(item => {
        learderList.push(item.name);
      });
      tableHeaderVb.value[4].value = learderList.join(" 、") || "--";
    }
    if (data?.lecturers?.length) {
      let lecturersList = [];
      data?.lecturers.map(item => {
        lecturersList.push(item.name);
      });
      tableHeaderVb.value[5].value = lecturersList.join(" 、") || "--";
    }
    if (data?.files?.length) {
      data?.files.map(item => {
        srcList.value.push(item?.uploadFile?.url);
      });
      url.value = data.files[0]?.uploadFile?.url;
    }
    tableHeader.value = tableHeaderVb.value;
  } else {
    // console.log("🐳-----err-----", err);
  }
};

//  课期名的展示处理函数
function generateCourseList(data) {
  const {
    name,
    prefixRule,
    suffixRule,
    prefix,
    suffix,
    draftCoursePeriodTimes
  } = data;

  const formatTimeToMonthDay = timestamp => {
    // 辅助函数：格式化时间为月日
    const date = new Date(timestamp);
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${month}月${day}日`;
  };
  const generateTermNumber = index => {
    // 辅助函数：生成期数
    return `第${index + 1}期`;
  };

  const generateByRule = (rule, customValue, timeData, index) => {
    // 辅助函数：根据规则生成前缀或后缀
    if (!rule) return null;
    switch (rule) {
      case "OPEN_TIME":
        return formatTimeToMonthDay(timeData.openTime);
      case "OTHER":
        return customValue;
      case "TERM_NUMBER":
        return generateTermNumber(index);
      default:
        return null;
    }
  };

  const courseList = draftCoursePeriodTimes.map((timeData, index) => {
    // 生成课程列表
    let courseName = name;
    const prefixValue = generateByRule(prefixRule, prefix, timeData, index); // 处理前缀
    if (prefixValue) {
      courseName = `${prefixValue}-${courseName}`;
    }
    const suffixValue = generateByRule(suffixRule, suffix, timeData, index); // 处理后缀
    if (suffixValue) {
      courseName = `${courseName}-${suffixValue}`;
    }
    return {
      courseName
    };
  });
  return courseList;
}

function hasValue(obj, field) {
  // 用于判断对象中某个字段是否为空
  const value = obj[field];
  return value !== undefined && value !== null && value !== "";
}
onMounted(() => {
  if (props.title === "课程基础信息") {
    if (hasValue(route.query, "courseId")) {
      getTableList();
    } else {
      courseChengAdd();
    }
  } else {
    PeriodCourseAdd();
  }
});
</script>

<template>
  <div class="class-track">
    <div v-if="tableHeader?.length" class="content">
      <el-descriptions title="" :column="2" label-width="120px">
        <template v-for="(item, index) in tableHeader" :key="index">
          <el-descriptions-item
            :label="item.label"
            width="120px"
            label-align="center"
          >
            <span v-if="item.label === '开课时间'" style="display: inline-grid">
              <span
                v-for="(it, ind) in item.value"
                :key="ind"
                style="margin-right: 10px"
                >{{ dayjs(it.openTime).format("YYYY-MM-DD HH:mm:ss") }}</span>
            </span>
            <span
              v-else-if="item.label === '课期名'"
              style="display: inline-grid"
            >
              <span
                v-for="(it, ind) in item.value"
                :key="ind"
                style="margin-right: 10px"
                >{{ it.courseName }}</span>
            </span>

            <span v-else>{{ item.value }}</span>
          </el-descriptions-item>
        </template>
      </el-descriptions>
      <div class="title">{{ "封面图" }}</div>
      <div v-if="srcList.length > 0" class="imgbox">
        <div v-for="(t, i) in srcList" :key="i" class="img">
          <el-image
            :src="ImageThumbnail(srcList[i], '120px')"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            show-progress
            :initial-index="i"
            :preview-src-list="srcList"
            :hide-on-click-modal="true"
            fit="scale-down"
            class="img-pic"
          />
        </div>
      </div>
      <div v-else class="imgbox">
        <el-image class="h-[85px] w-[120px]">
          <template #error>
            <div class="image-slot">暂无数据</div>
          </template>
        </el-image>
      </div>
    </div>
    <el-empty v-else description="暂无数据" />
  </div>
</template>

<style lang="scss" scoped>
.class-track {
  width: 100%;
  position: relative;
  .content {
    width: 100%;
    overflow-y: auto;
    padding: 10px;
    box-sizing: border-box;
    -ms-overflow-style: none;
    scrollbar-width: none;
    .title {
      width: 100%;
      padding: 20px 0px;
      line-height: 18px;
      font-weight: 500;
    }
    .imgbox {
      display: flex;
      flex-wrap: wrap;
      .img {
        width: 125px;
        height: 85px;
        margin: 0 10px 10px 0px;
        .img-pic {
          width: 125px;
          height: 85px;
        }
      }
    }
  }
}
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
  font-size: 14px;
}
</style>
